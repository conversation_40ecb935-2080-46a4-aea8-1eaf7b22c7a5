T__0=1
T__1=2
T__2=3
T__3=4
T__4=5
T__5=6
T__6=7
T__7=8
T__8=9
T__9=10
T__10=11
T__11=12
T__12=13
T__13=14
T__14=15
T__15=16
T__16=17
T__17=18
LINE_COMMENT=19
BLOCK_COMMENT=20
WS=21
NEWLINE=22
SEMI=23
VAR=24
GLOBAL=25
OF=26
NUM=27
WHERE=28
IF=29
THEN=30
ELSEIF=31
ELSE=32
ENDIF=33
FOR=34
BREAK=35
CONTINUE=36
TO=37
IN=38
DO=39
STEP=40
ENDFOR=41
WHILE=42
ENDWHILE=43
DEFINE=44
ENDDEFINE=45
FUNCTION=46
ENDFUNCTION=47
TRUE=48
FALSE=49
AND=50
OR=51
EQ=52
NEQ=53
LT=54
LE=55
GT=56
GE=57
NOT=58
PLUS=59
MINUS=60
MUL=61
DIV=62
REM=63
DECIMAL_LITERAL=64
GLOBAL_IDENTIFIER=65
LOCAL_IDENTIFIER=66
IDENTIFIER=67
ATTRIB_IDENTIFIER=68
STRING_LITERAL=69
'='=1
'('=2
')'=3
'?'=4
':'=5
'['=6
']'=7
','=8
'.'=9
'RPRO'=10
'DESIGN'=11
'PARAM'=12
'IPARAM'=13
'PWALLT'=14
'TWICE'=15
'DIFFERENCE'=16
'TANF'=17
'SUM'=18
';'=23
'+'=59
'-'=60
'/'=62
'ATTRIB '=68
