#pragma     once

#include    "WDHeader.h"

/**
* @brief 添加子节点之前通知
* @param pNode 当前节点
* @param pChild 添加的子节点
*/
typedef void (*NotifyNodeAddChildBefore)(LPWDNode pNode, LPWDNode pChild);
/**
* @brief 添加子节点之后通知
* @param pNode 当前节点
* @param pChild 添加的子节点
*/
typedef void (*NotifyNodeAddChildAfter)(LPWDNode pNode, LPWDNode pChild);
/**
* @brief 添加子节点之前通知
* @param pNode 当前节点
* @param pChild 插入的子节点
* @param pNextChild 子节点的插入位置(即当前子节点插入在哪个节点之前)
*/
typedef void (*NotifyNodeInsertChildBefore)(LPWDNode pNode, LPWDNode pChild, LPWDNode pNextChild);
/**
* @brief 添加子节点之后通知
* @param pNode 当前节点
* @param pChild 插入的子节点
* @param pNextChild 子节点的插入位置(即当前子节点插入在哪个节点之前)
*/
typedef void (*NotifyNodeInsertChildAfter)(LPWDNode pNode, LPWDNode pChild, LPWDNode pNextChild);
/**
* @brief 移除子节点之前通知
* @param pNode 当前节点
* @param pChild 移除的子节点
*/
typedef void (*NotifyNodeRemoveChildBefore)(LPWDNode pNode, LPWDNode pChild);
/**
* @brief 移除子节点之后通知
* @param pNode 当前节点
* @param pChild 移除的子节点
*/
typedef void (*NotifyNodeRemoveChildAfter)(LPWDNode pNode, LPWDNode pChild);
/**
* @brief 节点更新之前通知
* @param pNode 当前更新的节点
*/
typedef void (*NotifyNodeUpdateBefore)(LPWDNode pNode);
/**
* @brief 节点更新之后通知
* @param pNode 当前更新的节点
*/
typedef void (*NotifyNodeUpdateAfter)(LPWDNode pNode);

/**
* @brief 节点销毁之前通知
* @param pNode 当前销毁的节点
*/
typedef void (*NotifyNodeDestroyBefore)(LPWDNode pNode) ;
/**
* @brief 节点销毁之后通知
*  此时节点已经调用到析构的最后一步，所以这里只能使用节点的裸指针，不建议再使用节点的数据或方法
* @param pNode 当前销毁的节点
*/
typedef void (*NotifyNodeDestroyAfter)(const LPWDNode pNode) ;
/**
* @brief 节点属性值改变通知
* @param name 属性名称
* @param currValue 当前值
* @param prevValue 改变前的值
* @param pNode 发送者
*/
typedef void (*NotifyNodeAttributeValueChanged)(  const char* name
                                            , const LPWDBMAttr currValue
                                            , const LPWDBMAttr prevValue
                                            , LPWDNode pNode);
/**
* @brief 节点的子节点重新排序通知
* @param pNode 发送者
*/
typedef void (*NotifyNodeChildrenReordered)(LPWDNode pNode);

/// NodeTree 通知
/**
* @brief 当前节点改变通知
* @param pCurrNode 当前节点
* @param pPrevNode 前一个节点
* @param sender 发送者
*/
typedef   void(*NoticeCurrentNodeChanged)(LPWDNode pCurrNode, LPWDNode pPrevNode, LPWDNodeTree sender);
/**
* @brief 添加根节点通知
*/
typedef   void(*NoticeAddTopLevelNode)(LPWDNode pNode,LPWDNodeTree sender);
/**
* @brief 移除根节点通知
*/
typedef   void(*NoticeRemoveTopLevelNode)(LPWDNode pNode, LPWDNodeTree sender);

struct  NodeNotify
{

    NotifyNodeAddChildBefore        _NotifyNodeAddChildBefore;
    
    NotifyNodeAddChildAfter         _NotifyNodeAddChildAfter;
    
    NotifyNodeInsertChildBefore     _NotifyNodeInsertChildBefore;
    
    NotifyNodeInsertChildAfter      _NotifyNodeInsertChildAfter;
    
    NotifyNodeRemoveChildBefore     _NotifyNodeRemoveChildBefore;
    
    NotifyNodeRemoveChildAfter      _NotifyNodeRemoveChildAfter; 
    
    NotifyNodeUpdateBefore          _NotifyNodeUpdateBefore;
    
    NotifyNodeUpdateAfter           _NotifyNodeUpdateAfter;
    
    NotifyNodeDestroyBefore         _NotifyNodeDestroyBefore;
    
    NotifyNodeDestroyAfter          _NotifyNodeDestroyAfter;
    
    NotifyNodeAttributeValueChanged _NotifyNodeAttributeValueChanged;

    NotifyNodeChildrenReordered     _NotifyNodeChildrenReordered;
};


struct  NodeTreeNotify
{
    NoticeCurrentNodeChanged    _NoticeCurrentNodeChanged;
    NoticeAddTopLevelNode       _NoticeAddTopLevelNode;
    NoticeRemoveTopLevelNode    _NoticeRemoveTopLevelNode;
};

extern "C"
{
    /**
    * @brief 获取设计树节点
    * @param exeDirPath 程序工作目录
    */
    CORE_API LPWDNodeTree   wdGetTree();
    /// <summary>
    /// 检测名称是否存在
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    CORE_API bool           wdNodeExist(const char* name);
    /// <summary>
    /// 获取树节点下子节点的数量
    /// </summary>
    /// <param name="pTree"></param>
    /// <returns></returns>
    CORE_API size_t         wdGetTreeNodeCount(LPWDNodeTree pTree);
    /// <summary>
    /// 根据索引获取子节点
    /// </summary>
    /// <param name="pTree"></param>
    /// <param name="index"></param>
    /// <returns></returns>
    CORE_API LPWDNode       wdGetTreeNode(LPWDNodeTree pTree,size_t index);
    /// <summary>
    /// 创建节点
    /// </summary>
    /// <param name="typeName">类型</param>
    /// <param name="name">名称</param>
    /// <param name="parent">父节点</param>
    /// <param name="pNext">下一个节点</param>
    /// <returns></returns>
    CORE_API LPWDNode       wdCreateNode(const char* typeName,const char* name,LPWDNode parent,LPWDNode pNext);
    /// <summary>
    /// 删除节点
    /// </summary>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool           wdDestroyNode(LPWDNode);  

    /// <summary>
    /// 获取当前节点
    /// </summary>
    /// <returns></returns>
    CORE_API LPWDNode       wdGetCurrentNode();
    /// <summary>
    /// 设置当前节点
    /// </summary>
    /// <param name="node"></param>
    /// <returns></returns>
    CORE_API void          wdSetCurrentNode(LPWDNode node,bool hightligt);
    /// <summary>
    /// 根据名称查询节点,模糊查询
    /// </summary>
    /// <returns></returns>
    CORE_API LPWDNode       wdFuzzyFindNode(const char* name);
    /// <summary>
    /// 全匹配查询
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    CORE_API LPWDNode       wdFindNodeByName(const char* name);
    /// <summary>
    /// 全匹配查询,通过节点Id查询
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    CORE_API LPWDNode       wdFindNodeById(const WD::WDUuid* id);
    CORE_API LPWDNode       wdFindNodeByIdString(const char* id);
    /// <summary>
    /// 获取父节点
    /// </summary>
    /// <param name="cur"></param>
    /// <returns></returns>
    CORE_API LPWDNode       wdNodeParent(LPWDNode cur);
    /// <summary>
    /// 设置节点的父节点
    /// </summary>
    /// <param name="cur"></param>
    /// <returns></returns>
    CORE_API bool           wdSetNodeParent(LPWDNode cur,LPWDNode parent,LPWDNode pNext);
    /// <summary>
    /// 前一个
    /// </summary>
    /// <param name="cur"></param>
    /// <returns></returns>
    CORE_API LPWDNode       wdPrevNode(LPWDNode cur);
    /// <summary>
    /// 后一个
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    CORE_API LPWDNode       wdNextNode(LPWDNode cur);

    /// <summary>
    /// 获取节点的子节点数量
    /// </summary>
    /// <param name="pNode"></param>
    /// <returns></returns>
    CORE_API size_t         wdGetNodeChildCount(LPWDNode pNode);
    /// <summary>
    /// 获取子节点
    /// </summary>
    /// <param name="parent"></param>
    /// <param name="index"></param>
    /// <returns></returns>
    CORE_API LPWDNode       wdGetNodeChild(LPWDNode parent,size_t index);
    /// <summary>
    /// 获取节点的Id
    /// </summary>
    /// <param name="object"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    CORE_API bool           wdGetNodeId(LPWDNode object,WD::WDUuid* id);
    /// <summary>
    /// 获取节点名称字段的长度
    /// </summary>
    /// <param name="object"></param>
    /// <returns></returns>
    CORE_API size_t         wdGetNodeNameSize(LPWDNode object);

    /// <summary>
    /// 获取节点名称
    /// </summary>
    /// <param name="object"></param>
    /// <returns></returns>
    CORE_API LPCSTR         wdGetNodeName(LPWDNode object,char* name,size_t nBuf);
    /// <summary>
    /// 获取节点类型名称
    /// </summary>
    /// <param name="object"></param>
    /// <returns></returns>
    CORE_API LPCSTR         wdGetNodeType(LPWDNode object);
    /// <summary>
    /// 获取节点位置
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool           wdGetNodeWorldPosition(LPWDNode object,    WDDouble3*);
    /// <summary>
    /// 获取节点的缩放
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool           wdGetNodeWorldScale(LPWDNode object,    WDDouble3*);
    /// <summary>
    /// 获取节点的旋转信息
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool           wdGetNodeWorldRotate(LPWDNode object,   WDQuat*);
    /// <summary>
    /// 获取节点的矩阵
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool           wdGetNodeWorldTransform(LPWDNode object,WDMat4*);

    /// <summary>
    /// 设置回调
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API LPNodeCallBack wdSetNodeCallback(LPWDNode object,NodeNotify* notify);
    /// <summary>
    /// 移除回调
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool          wdRemoveNodeCallback(LPWDNode object,LPNodeCallBack cb);


    /// <summary>
    /// 设置NodeTree回调
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API LPTreeCallBack wdSetNodeTreeCallback(LPWDNodeTree object,NodeTreeNotify* notify);
    /// <summary>
    /// 设置NodeTree回调
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API LPTreeCallBack wdSetNodeTreeCallbackUser(LPWDNodeTree object,void* notify[],size_t nCnt);
    /// <summary>
    /// 移除NodeTree回调
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool           wdRemoveNodeTreeCallback(LPWDNodeTree object,LPTreeCallBack cb);


}
    