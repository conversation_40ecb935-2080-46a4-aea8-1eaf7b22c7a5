#pragma     once

#include    "WDHeader.h"


using   CSSegment3  =   WD::DSegment3;

struct  CSKeyPoint
{
    //位置
    WD::DVec3 _position;
    //方向
    WD::DVec3 _direction;
    //NUMB 关键点序号
    int _numb;
    //BORE 关键点公称直径
    char _bore[128];
    //PCON 连接形式
     char _connType[128];
};
struct  CSLine
{
    //起点
    WD::DVec3 sPosition;
    //起点方向
    WD::DVec3 sDirection;
    //终点位置
    WD::DVec3 ePosition;
    char _key[128];
};

struct CSCaptureResult
{
    WD::WDNode*     _node;
    WD::DVec3       _point;
    CSKeyPoint      _keypoints;
    CSLine          _lineData;
    CSSegment3      _segmentData;
    /// <summary>
    /// -1:表示无效
    /// 0:表示没有拾取到关键点，线，线段
    /// 1:关键点有效
    /// 2:线有效
    /// 3:线段有效
    /// </summary>
    int             _type;
};

/**
* @brief 添加子节点之前通知
* @param pNode 当前节点
* @param pChild 添加的子节点
*/
typedef void (*NotifyOnActived)(const WD::WDCapturePositioning* sender);

/**
* @brief 定位捕捉结果通知,带退出标志,可以决定此次结果完成后是否自动退出捕捉
* @param result 捕捉的结果数据
* @param existFlag 退出标志
*   每次通知结果后，都可以由子类重新指定该值以决定捕捉是否自动退出
*   默认情况 existFlag值为false,即通知一次结果后不会退出，继续捕捉且继续通知结果
*   如果重写时，将existFlag的值修改为true，则此次结果通知完成后，将自动退出捕捉状态
* @param sender 发送者
*/
typedef bool (*NotifyResult)(const CSCaptureResult* result,const WD::WDCapturePositioning* sender);

/**
* @brief 捕捉节点过滤器
* @param node 当前将要参与捕捉计算的节点
* @param sender 发送者
* @return true 表示当前节点参与捕捉; false 表示当前节点不会参与捕捉计算，也就是说，该节点肯定不会作为捕捉结果出现
*/
typedef bool (*NotifyNodeFilter)(WD::WDNode* node,const WD::WDCapturePositioning* sender);
/**
* @brief 定位捕捉将要被取消激活前通知
* @param sender 发送者
*/
typedef void (*NotifyDeactived)(const WD::WDCapturePositioning* sender);

/**
* @brief 开始一次捕捉，如果是鼠标操作的话，对应的就是鼠标按下
*  对于onResult来说，我们只关心此次捕捉的结果而不关心其过程
*  但是 onStart, onHover, onEnd三个搭配使用时，不仅能拿到结果，还可以以获取更详细的捕捉流程
* @param hover 是否有预选的结果,
* @param sender 发送者
*/
typedef void (*NotifyStart)(const CSCaptureResult* hover, const WD::WDCapturePositioning* sender) ;
/**
* @brief 正在进行捕捉，如果是鼠标操作的话，对应的就是鼠标移动
*  对于onResult来说，我们只关心此次捕捉的结果而不关心其过程
*  但是 onStart, onHover, onEnd三个搭配使用时，不仅能拿到结果，还可以以获取更详细的捕捉流程
* @param hover 是否有预选的结果
* @param sender 发送者
*/
typedef void (*NotifyHover)(const CSCaptureResult* hover, const WD::WDCapturePositioning* sender) ;
/**
* @brief 一次捕捉结束，将确认是否有结果，如果是鼠标操作的话，对应的就是鼠标抬起
*  对于onResult来说，我们只关心此次捕捉的结果而不关心其过程
*  但是 onStart, onHover, onEnd三个搭配使用时，不仅能拿到结果，还可以以获取更详细的捕捉流程
* @param hover 是否有预选的结果
* @param existFlag 退出标志
*   每次通知结果后，都可以由子类重新指定该值以决定捕捉是否自动退出
*   默认情况 existFlag值为false,即通知一次结果后不会退出，继续捕捉且继续通知结果
*   如果重写时，将existFlag的值修改为true，则此次结果通知完成后，将自动退出捕捉状态
* @param sender 发送者
*/
typedef bool (*NotifyEnd)(const CSCaptureResult* result, const  WD::WDCapturePositioning* sender);


struct  CaptureNotify
{
    NotifyOnActived     _NotifyOnActived;
    NotifyResult        _NotifyResult;
    NotifyNodeFilter    _NotifyNodeFilter;
    NotifyDeactived     _NotifyDeactived;
    NotifyStart         _NotifyStart;
    NotifyHover         _NotifyHover;
    NotifyEnd           _NotifyEnd;
};

using   WDLPCapture =   void*;


extern "C"
{
    /// <summary>
    /// 激活捕捉工具
    /// </summary>
    /// <param name="object"></param>
    /// <returns></returns>
    CORE_API WDLPCapture    wdActiveCapture(const CaptureNotify* object);
    /// <summary>
    /// 卸载激活
    /// </summary>
    /// <param name="object"></param>
    /// <returns></returns>
    CORE_API void           wdDeativateCapture(WDLPCapture object);

}
    