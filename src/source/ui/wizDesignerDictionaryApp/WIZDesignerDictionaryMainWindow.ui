<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>WIZDesignerDictionaryMainWindow</class>
 <widget class="QMainWindow" name="WIZDesignerDictionaryMainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>858</width>
    <height>831</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>类型属性查看器</string>
  </property>
  <property name="windowIcon">
   <iconset resource="WIZDesignerDictionaryMainWindow.qrc">
    <normaloff>:/wizDesignerDictionaryApp/logo</normaloff>:/wizDesignerDictionaryApp/logo</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_10">
    <item row="0" column="0">
     <widget class="QSplitter" name="splitter_3">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QLabel" name="labelModule">
       <property name="text">
        <string>模块</string>
       </property>
      </widget>
      <widget class="QComboBox" name="comboBoxModule"/>
     </widget>
    </item>
    <item row="0" column="1" rowspan="2">
     <layout class="QGridLayout" name="gridLayout_9" rowstretch="1,0">
      <item row="0" column="0">
       <widget class="QGroupBox" name="groupBoxType">
        <property name="title">
         <string>类型</string>
        </property>
        <layout class="QGridLayout" name="gridLayout_8">
         <item row="0" column="0">
          <widget class="QLabel" name="labelNodeName">
           <property name="text">
            <string>名称</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLineEdit" name="lineEditTypeName"/>
         </item>
         <item row="1" column="0" colspan="2">
          <widget class="QGroupBox" name="groupBox">
           <property name="title">
            <string/>
           </property>
           <layout class="QGridLayout" name="gridLayout_6">
            <item row="0" column="0">
             <widget class="QCheckBox" name="checkBoxTriggerUpdate">
              <property name="text">
               <string>触发更新</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QCheckBox" name="checkBoxAutoGeneration">
              <property name="text">
               <string>自动生成</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item row="2" column="0" colspan="2">
          <layout class="QGridLayout" name="gridLayout_7" rowstretch="0,1,0,1,0,1">
           <item row="0" column="0">
            <widget class="QLabel" name="labelParentsList">
             <property name="text">
              <string>父节点列表</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QTextEdit" name="textEditParentsList"/>
           </item>
           <item row="1" column="1" alignment="Qt::AlignBottom">
            <widget class="QPushButton" name="pushButtonChange">
             <property name="focusPolicy">
              <enum>Qt::ClickFocus</enum>
             </property>
             <property name="text">
              <string>更改</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="labelResource">
             <property name="text">
              <string>资源</string>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QTableWidget" name="tableWidgetResource"/>
           </item>
           <item row="3" column="1" alignment="Qt::AlignBottom">
            <widget class="QSplitter" name="splitter_6">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <widget class="QPushButton" name="pushButtonAdd">
              <property name="focusPolicy">
               <enum>Qt::ClickFocus</enum>
              </property>
              <property name="text">
               <string>添加</string>
              </property>
             </widget>
             <widget class="QPushButton" name="pushButtonRemoveSelects">
              <property name="focusPolicy">
               <enum>Qt::ClickFocus</enum>
              </property>
              <property name="text">
               <string>移除选择</string>
              </property>
             </widget>
             <widget class="QPushButton" name="pushButtonRemoveAll">
              <property name="focusPolicy">
               <enum>Qt::ClickFocus</enum>
              </property>
              <property name="text">
               <string>移除全部</string>
              </property>
             </widget>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="QLabel" name="labelDescription">
             <property name="text">
              <string>描述</string>
             </property>
            </widget>
           </item>
           <item row="5" column="0">
            <widget class="QTextEdit" name="textEditDescription"/>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QGroupBox" name="groupBoxAttribute">
        <property name="title">
         <string>属性</string>
        </property>
        <layout class="QGridLayout" name="gridLayout_4">
         <item row="0" column="2">
          <widget class="QLineEdit" name="lineEditAttrName">
           <property name="focusPolicy">
            <enum>Qt::ClickFocus</enum>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QComboBox" name="comboBoxDataType"/>
         </item>
         <item row="5" column="2">
          <widget class="QLineEdit" name="lineEditRegex"/>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="labelName">
           <property name="text">
            <string>名称</string>
           </property>
          </widget>
         </item>
         <item row="4" column="2">
          <widget class="QGroupBox" name="groupBoxDigit">
           <property name="title">
            <string/>
           </property>
           <layout class="QGridLayout" name="gridLayout_3" columnstretch="0,1">
            <item row="0" column="0">
             <widget class="QLabel" name="labelMin">
              <property name="text">
               <string>最小值</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QDoubleSpinBox" name="doubleSpinBoxMin">
              <property name="minimum">
               <double>-9999999999999999455752309870428160.000000000000000</double>
              </property>
              <property name="maximum">
               <double>9999999999999999455752309870428160.000000000000000</double>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="labelMax">
              <property name="text">
               <string>最大值</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QDoubleSpinBox" name="doubleSpinBoxMax">
              <property name="minimum">
               <double>-9999999999999999455752309870428160.000000000000000</double>
              </property>
              <property name="maximum">
               <double>9999999999999999455752309870428160.000000000000000</double>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="labelStep">
              <property name="text">
               <string>步长</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QDoubleSpinBox" name="doubleSpinBoxStep">
              <property name="minimum">
               <double>-9999999999999999455752309870428160.000000000000000</double>
              </property>
              <property name="maximum">
               <double>9999999999999999455752309870428160.000000000000000</double>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="labelDecimals">
              <property name="text">
               <string>精度</string>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="QSpinBox" name="spinBoxDecimals"/>
            </item>
           </layout>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QLineEdit" name="lineEditAttrSampleName"/>
         </item>
         <item row="3" column="2">
          <widget class="QLineEdit" name="lineEditDefaultValue"/>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="labelAttrSampleName">
           <property name="text">
            <string>简称</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="labelDefaultValue">
           <property name="text">
            <string>默认值</string>
           </property>
          </widget>
         </item>
         <item row="7" column="0" colspan="2">
          <widget class="QLabel" name="labelRefNodeModule">
           <property name="text">
            <string>引用所属模块</string>
           </property>
          </widget>
         </item>
         <item row="8" column="1" colspan="2" alignment="Qt::AlignRight">
          <widget class="QSplitter" name="splitter_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <widget class="QPushButton" name="pushButtonDelete">
            <property name="focusPolicy">
             <enum>Qt::ClickFocus</enum>
            </property>
            <property name="text">
             <string>删除属性</string>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
           <widget class="QPushButton" name="pushButtonNew">
            <property name="focusPolicy">
             <enum>Qt::ClickFocus</enum>
            </property>
            <property name="text">
             <string>新建属性</string>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
          </widget>
         </item>
         <item row="6" column="2">
          <widget class="QLineEdit" name="lineEditEnumDictName"/>
         </item>
         <item row="5" column="0">
          <widget class="QLabel" name="labelRegex">
           <property name="text">
            <string>正则表达式</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="labelDataType">
           <property name="text">
            <string>数据类型</string>
           </property>
          </widget>
         </item>
         <item row="7" column="2">
          <widget class="QComboBox" name="comboBoxRefNodeModule"/>
         </item>
         <item row="6" column="0">
          <widget class="QLabel" name="labelEnumDictName">
           <property name="text">
            <string>字典名称</string>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QGroupBox" name="groupBoxFlags">
           <property name="title">
            <string>标志</string>
           </property>
           <layout class="QGridLayout" name="gridLayout">
            <item row="0" column="0">
             <widget class="QCheckBox" name="checkBoxReadOnly">
              <property name="focusPolicy">
               <enum>Qt::ClickFocus</enum>
              </property>
              <property name="text">
               <string>只读</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QCheckBox" name="checkBoxHide">
              <property name="focusPolicy">
               <enum>Qt::ClickFocus</enum>
              </property>
              <property name="text">
               <string>隐藏</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <item row="1" column="0">
     <widget class="QSplitter" name="splitter_5">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <widget class="QGroupBox" name="groupBoxNodeTypes">
       <property name="title">
        <string>节点类型</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_5">
        <item row="0" column="0">
         <widget class="QLabel" name="labelNodeSearch">
          <property name="text">
           <string>搜索</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLineEdit" name="lineEditTypeSearch">
          <property name="focusPolicy">
           <enum>Qt::ClickFocus</enum>
          </property>
         </widget>
        </item>
        <item row="1" column="0" colspan="2">
         <widget class="QTableWidget" name="tableWidgetTypes"/>
        </item>
       </layout>
      </widget>
      <widget class="QGroupBox" name="groupBoxAttributeTypes">
       <property name="title">
        <string>属性类型</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <widget class="QLabel" name="labelAttributeSearch">
          <property name="text">
           <string>搜索</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLineEdit" name="lineEditAttributeSearch">
          <property name="focusPolicy">
           <enum>Qt::ClickFocus</enum>
          </property>
         </widget>
        </item>
        <item row="1" column="0" colspan="2">
         <widget class="QTableWidget" name="tableWidgetAttributes"/>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item row="2" column="1" alignment="Qt::AlignRight">
     <widget class="QSplitter" name="splitter">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QPushButton" name="pushButtonSave">
       <property name="focusPolicy">
        <enum>Qt::ClickFocus</enum>
       </property>
       <property name="text">
        <string>保存更改</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButtonExit">
       <property name="focusPolicy">
        <enum>Qt::ClickFocus</enum>
       </property>
       <property name="text">
        <string>退出</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="WIZDesignerDictionaryMainWindow.qrc"/>
 </resources>
 <connections/>
</ui>
