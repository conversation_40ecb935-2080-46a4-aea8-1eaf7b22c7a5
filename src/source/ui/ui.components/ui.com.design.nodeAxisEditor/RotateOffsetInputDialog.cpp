#include "RotateOffsetInputDialog.h"
#include "core/WDCore.h"
#include "core/undoRedo/WDUndoStack.h"


RotateOffsetInputDialog::RotateOffsetInputDialog(WD::WDCore& core
    , WD::WDObjectAxisEditor* pEditor
    , QWidget *parent)
    : QDialog(parent)
    , _core(core)
    , _pEditor(pEditor)
{
    _pPreviewCommand = nullptr;

    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    _axis   = WD::DVec3::AxisX();
    _center = WD::DVec3::Zero();

    connect(ui.pushButtonPreview
        , &QPushButton::clicked
        , this
        , &RotateOffsetInputDialog::slotPushButtonPreviewClicked);

    connect(ui.pushButtonOk
        , &QPushButton::clicked
        , this
        , &RotateOffsetInputDialog::slotPushButtonOkClicked);

    connect(ui.pushButtonCancel, &QPushButton::clicked, this, &RotateOffsetInputDialog::close);

    this->retranslateUi();
}

RotateOffsetInputDialog::~RotateOffsetInputDialog()
{
    if (_pPreviewCommand != nullptr)
    {
        delete _pPreviewCommand;
        _pPreviewCommand = nullptr;
    }
}

void RotateOffsetInputDialog::showEvent([[maybe_unused]] QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();

    ui.doubleSpinBoxAngle->setValue(0.0);
}

void RotateOffsetInputDialog::hideEvent([[maybe_unused]] QHideEvent* evt)
{
    if (_pPreviewCommand != nullptr)
    {
        _pPreviewCommand->undo();
        delete _pPreviewCommand;
        _pPreviewCommand = nullptr;

        _core.needRepaint();
    }
}

void RotateOffsetInputDialog::slotPushButtonPreviewClicked()
{
    if (_pEditor == nullptr)
        return;

    auto pObject = _pEditor->object();
    if (pObject == nullptr)
        return;

    if (_pPreviewCommand != nullptr)
    {
        _pPreviewCommand->undo();
        delete _pPreviewCommand;
        _pPreviewCommand = nullptr;
    }

    double angle = ui.doubleSpinBoxAngle->value();
    if (WD::Abs(angle) > WD::NumLimits<float>::Epsilon)
    {
        _pPreviewCommand = WD::WDObjectAxisEditor::Object::MakeRotateCommand(_core
            , pObject
            , _axis, angle, _center
            , false);

        if (_pPreviewCommand != nullptr)
        {
            _pPreviewCommand->redo();
            _core.needRepaint();
        }
    }
}
void RotateOffsetInputDialog::slotPushButtonOkClicked()
{
    if (_pEditor == nullptr)
        return;

    auto pObject = _pEditor->object();
    if (pObject == nullptr)
        return;

    // 如果有预览命令,这里撤销预览操作并删除预览命令
    if (_pPreviewCommand != nullptr)
    {
        _pPreviewCommand->undo();
        delete _pPreviewCommand;
        _pPreviewCommand = nullptr;
    }
    double angle = ui.doubleSpinBoxAngle->value();
    if (WD::Abs(angle) > WD::NumLimits<float>::Epsilon)
    {
        switch (WD::WDObjectAxisEditor::EditType(_editType))
        {
        case WD::WDObjectAxisEditor::ET_None:
        case WD::WDObjectAxisEditor::ET_Move:
        case WD::WDObjectAxisEditor::ET_Scale:
            assert(false);
            break;
        case WD::WDObjectAxisEditor::ET_Rotate:
            {
                auto& axisR = _pEditor->getAxisR();
                axisR.execRotate(WD::WDEditAxisRotate::Axis(_editAxis), angle);
            }
            break;
        case WD::WDObjectAxisEditor::ET_MoveRotate:
            {
                auto& axisMR = _pEditor->getAxisMR();
                axisMR.exec(WD::WDEditAxisMoveRotate::Axis(_editAxis), angle);
            }
            break;
        case WD::WDObjectAxisEditor::ET_SingleMoveRotate:
            {
                auto& axisSMR = _pEditor->getAxisSingleMR();
                axisSMR.exec(WD::WDEditSingleAxisMoveRotate::Axis(_editAxis), angle);
            }
            break;
        default:
            break;
        }
    }
    _core.needRepaint();

    this->accept();
}

void RotateOffsetInputDialog::retranslateUi()
{
    Trs("RotateOffsetInputDialog"
        , static_cast<QDialog*>(this)
        , ui.label
        , ui.pushButtonPreview
        , ui.pushButtonOk
        , ui.pushButtonCancel
    );
}