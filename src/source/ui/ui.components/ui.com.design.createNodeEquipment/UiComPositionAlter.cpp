#include "UiComPositionAlter.h"
#include "core/message/WDMessage.h"
#include "businessModule/design/WDBMDesign.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/WDBMAuthorityMgr.h"
#include "core/undoRedo/WDUndoStack.h"

UiComPositionAlter::UiComPositionAlter(WD::WDCore& core, QWidget *parent)
	: QDialog(parent), _core(core), _positionCaptureHelpter(core)
{
	ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 界面文本翻译
    this->retranslateUi();

    {
        _positionCaptureHelpter.setCaptureTimes(UiPositionCaptureHelpter::CT_Repeat);
        // 设置坐标拾取对象
        _positionCaptureHelpter.setDoubleSpinBoxXYZ(
            ui.doubleSpinBoxX
            , ui.doubleSpinBoxY
            , ui.doubleSpinBoxZ
        );
        _positionCaptureHelpter.setCheckBoxCapture(ui.checkBoxCapture);
        _positionCaptureHelpter.setCheckBoxXYZ(
            ui.checkBoxX
            , ui.checkBoxY
            , ui.checkBoxZ
        );
    }

    // 绑定事件响应
	connect(ui.pushButtonOk,    &QPushButton::clicked, this, &UiComPositionAlter::slotOkButtonClicked);
	connect(ui.pushButtonCancel,&QPushButton::clicked, this, &UiComPositionAlter::slotCancelButtonClicked);
    connect(&_positionCaptureHelpter, &UiPositionCaptureHelpter::sigPositionChanged, this, &UiComPositionAlter::slotPositionChanged);
}
UiComPositionAlter::~UiComPositionAlter()
{
}

bool UiComPositionAlter::updateWidget(WD::WDNode::SharedPtr pNode)
{
	if (pNode == nullptr)
		return false;
    if (!pNode->isAnyOfType("EQUI", "SUBE"))
    {
        WD_WARN_T("UiComPositionAlter", "IsnotEquipmentNode");
        return false;
    }
    _equipment = pNode;
	ui.lineEditCe->setText(QString::fromUtf8(pNode->name().c_str()));
    _positionCaptureHelpter.setPosition(pNode->globalTranslation());
    return true;
}
void UiComPositionAlter::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);
    this->updateWidget(_core.nodeTree().currentNode());
    // 触发重绘
    _core.needRepaint();
}
void UiComPositionAlter::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
    _positionCaptureHelpter.exit(true);
    // 触发重绘
    _core.needRepaint();
}

WD::Vec3 UiComPositionAlter::worldPosition()
{
    // 目前默认相对世界，若此逻辑变更此处也需变更
    return _positionCaptureHelpter.position();
}

void UiComPositionAlter::slotOkButtonClicked()
{
    auto    pEqui   =   _equipment.lock();
    if (pEqui == nullptr)
    {
        // !TODO: 提示当前设备节点为空
        return ;
    }
    // 申领对象
    if (!_core.getBMDesign().authorityMgr().checkModify({ pEqui }))
        return;
    
    // 计算设备定位点偏移
    auto    tarPos  = _positionCaptureHelpter.position();
    auto    offset  =   tarPos - pEqui->globalTranslation();
    if (offset.lengthSq() < WD::NumLimits<float>::Epsilon)
    {
        // !TODO: 提示当前设备节点偏移量过小
        return ;
    }
    _core.undoStack().push(new EquiMoveCommand(_core, pEqui, offset));
}
void UiComPositionAlter::slotCancelButtonClicked()
{
    this->clearWidget();
	this->reject();
}
void UiComPositionAlter::slotPositionChanged(const WD::DVec3& currPosition, const WD::DVec3& prevPosition, const WD::DMat4& transform)
{
    WDUnused2(prevPosition, transform);
    emit sigPositionChanged(currPosition);
}

void UiComPositionAlter::clearWidget()
{
    _equipment.reset();

    ui.lineEditCe->clear();
    _positionCaptureHelpter.setPosition(WD::DVec3(0.0f, 0.0f, 0.0f));
}
void UiComPositionAlter::retranslateUi()
{
    Trs("UiComPositionAlter"
        , static_cast<QDialog*>(this)
        , ui.pushButtonOk
        , ui.pushButtonCancel
        , ui.groupBoxOffset
        , ui.checkBoxCapture
        , ui.labelCe
        , ui.checkBoxCapture);
}