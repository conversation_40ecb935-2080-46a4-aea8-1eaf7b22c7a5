#pragma once

#include    <QObject>

#include	"core/WDCore.h"
#include	"core/businessModule/design/WDBMDesign.h"
#include    "../../wizDesignerApp/UiInterface/UiInterface.h"

/**
 * @brief 处理轴网数据
*/
class HandleAxisNet
	: public QObject
{
	Q_OBJECT

public:
	HandleAxisNet(QObject *parent = nullptr);
    ~HandleAxisNet();
	//轴网类型
	enum GridType
	{
		//辅助轴网
		GT_ASSISTANT = 0,
		//结构轴网
		GT_STRUCTURE
	};
	/**
	 * @brief 单个轴（X,Y,EL）数据
	*/
	struct AxisData
	{
		std::string lable;//自定义标签
		std::string type;//XYZ类型
		std::vector<std::pair<std::string, double>> idOffsets;
	};
	/**
	 * @brief 轴网系统数据
	*/
	struct SystemData
	{
		std::string name;
		// 显示设置
		int displayFlag;
		//轴偏移量
		std::vector<AxisData> xyz;
	};
	// 轴网数据结构体
	struct CreateAxisNetMsg
	{
		// 轴网系统名称
		std::string strAxisName;
		// 轴网轴标签名
		std::string strLabel;
		// 轴网轴名
		std::string strAxis;
		// key: 轴网线偏移量 value:轴网线编号
		std::map<double, std::string> mapIdOffset;
		// 显示设置
		int displayFlag;
	};

public:
	/**
	* @brief 判断当前节点类型是否为GRINLN、GRIDAX、GRIDSY中的一种
	*/
	bool judgeNodesType(WD::WDNode::SharedPtr pNode);
	/**
	 * @brief 设置Z轴GRINLN节点显隐
	 * @param pCurrentNode 当前选中节点
	 * @param hide true 隐藏, false 显示
	*/
	void setAxisZHidden(WD::WDNode::SharedPtr pCurrentNode, bool hide);
	/**
	 * @brief 获取轴网系统数据
	 * @param nodeSys 
	 * @return 
	*/
	static SystemData GetSystemNodeData(WD::WDNode& nodeSys);
public:
	/**
	* @brief 判断是否有重复
	*/
	bool judgeIsRepetition(const QStringList& lstData);
private:
	// key: 辅助轴网轴uuid, value: 辅助轴网轴下的辅助轴网线GRINLN
	using mapNodes = std::map<std::string, std::vector<WD::WDNode::SharedPtr>>;
	mapNodes _mapZ;
};

