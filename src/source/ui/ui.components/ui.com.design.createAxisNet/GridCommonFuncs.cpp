#include "GridCommonFuncs.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/WDBMAuthorityMgr.h"
#include "core/message/WDMessage.h"
#include "core/undoRedo/WDUndoStack.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include <queue>
#include <QDialog>
#include <QTreeWidgetItemIterator> 
#include <QTreeWidgetItem>
GridCommonFuncs::GridCommonFuncs(WD::WDCore& iCore) :_core(iCore)
{

}

WD::WDNode::SharedPtr GridCommonFuncs::createNode(WD::WDNode& parent, const std::string& name, const std::string& type)
{
    WD::WDNode::SharedPtr pParent = WD::WDNode::ToShared(&parent);
    // 申领对象
    if (!_core.getBMDesign().authorityMgr().checkAdd({ pParent }))
    {
        return nullptr;
    }
    // 校验是否存在相同名称
    if (_core.getBMDesign().nameExists(name))
    {
        std::string msg = WD::WDTs("AddNewStructureNodeDialog", "Same Name Exists!") + " " + name;
        WD::Core().message().warn(msg);
        return nullptr;
    }
    auto newNode = _core.getBMDesign().create(pParent, type, nullptr, name);
    if (newNode == nullptr)
    {
        WD_WARN_T("AddNewStructureNodeDialog", "CreateNodeFailed");
        return nullptr;
    }
    return newNode;
}

void GridCommonFuncs::addOperationCmd(WD::WDUndoCommand* pCmd)
{
    if (pCmd == nullptr)
    {
        return;
    }

    _operationCmds.emplace_back(pCmd);    
}

void GridCommonFuncs::setAttributesInOperation(WD::WDNode& node, const KeyValues& kvs)
{
    std::vector<WD::WDBMBase::CmdAttribute> attrs;
    attrs.reserve(kvs.size());
    for (size_t i = 0; i < kvs.size(); ++i)
    {
        auto& key = kvs[i].first;
        auto& value = kvs[i].second;
        auto originAttr = node.getAttribute(key);
        if (!originAttr.valid())
        {
            continue;
        }
        if (originAttr == value)
        {
            continue;
        }
        if (!node.setAttribute(key, value))
        {
            continue;
        }
        attrs.emplace_back(key, originAttr);
    }
    if (attrs.size() == 0)
    {
        return;
    }
    std::vector<WD::WDBMBase::CmdNodeAttributes> modifiedAttrs;
    modifiedAttrs.emplace_back(WD::WDNode::ToShared(&node), attrs);
    WD::WDUndoCommand* pUndoAttrCmd = WD::WDBMBase::MakeAttributesSetedCommand(modifiedAttrs);
    addOperationCmd(pUndoAttrCmd);
}

void GridCommonFuncs::operationDone(const std::string& operation)
{
    assert(operation == _currentOperation);
    _core.undoStack().beginMarco(_currentOperation);
    for (auto pCmd : _operationCmds)
    {
        _core.undoStack().push(pCmd);
    }
    _core.undoStack().endMarco();
    _operationCmds.clear();
    _currentOperation.clear();
}

void GridCommonFuncs::operationStart(const std::string& operation)
{
    assert(_currentOperation.empty() && _operationCmds.empty());
    _currentOperation = operation;
}

void GridCommonFuncs::nodeCreatedInOperation(WD::WDNode& node)
{
    WD::WDNode::Nodes nodes = { WD::WDNode::ToShared(&node) };
    addOperationCmd(WD::WDBMBase::MakeCreatedCommand(nodes));
}

WD::WDNode::SharedPtr GridCommonFuncs::createNodeInOperation(WD::WDNode& parent, const std::string& name, const std::string& type)
{
    WD::WDNode::SharedPtr pNewNode = createNode(parent, name, type);
    if (pNewNode == nullptr)
    {
        return nullptr;
    }
    nodeCreatedInOperation(*pNewNode);
    return pNewNode;
}

std::vector<WD::WDNode::SharedPtr> GridCommonFuncs::GetSpecificNodes(WD::WDNode& node,
    const std::function<bool(WD::WDNode::SharedPtr)>& strategy4child)
{
    std::vector<WD::WDNode::SharedPtr> nodes;
    std::queue<WD::WDNode::SharedPtr> que;
    for (auto& child : node.children())
    {
        que.push(child);
    }
    while (!que.empty())
    {
        WD::WDNode::SharedPtr frontNode = que.front();
        if (strategy4child(frontNode))
        {
            nodes.push_back(frontNode);
        }
        for (auto& child : frontNode->children())
        {
            que.push(child);
        }
        que.pop();
    }
    return nodes;
}

QTreeWidgetItem* GridCommonFuncs::LocateNode(const QTreeWidget& treeWidget, WD::WDNode& targetNode)
{
    for (QTreeWidgetItemIterator it(const_cast<QTreeWidget*>(&treeWidget)); *it; ++it)
    {
        auto pItem = *it;
        WD::WDNode::SharedPtr pNode = WD::WDNode::SharedCast(pItem->data(0, Qt::UserRole).value<UiWeakObject>().object());
        if (pNode.get() == &targetNode)
        {
            return pItem;
        }
    }
    return nullptr;
}

QTreeWidgetItem& GridCommonFuncs::AddNode(QTreeWidget& treeWidget, QTreeWidgetItem* pParent, WD::WDNode& node)
{
    QTreeWidgetItem* pNewItem = nullptr;
    if (pParent == nullptr)
    {
        pNewItem = new QTreeWidgetItem;
        treeWidget.addTopLevelItem(pNewItem);
    }
    else
    {
        pNewItem = new QTreeWidgetItem(pParent);
    }
    QVariant userData;
    userData.setValue(UiWeakObject(WD::WDNode::ToShared(&node)));
    pNewItem->setText(0, QString::fromUtf8(node.name().c_str()));    
    pNewItem->setData(0, Qt::UserRole, userData);
    return *pNewItem;
}


GridCommonFuncs::Operation::Operation(const std::string& id, GridCommonFuncs& delegate) :_id(id), _delegate(delegate)
{
    _delegate.operationStart(_id);
}

GridCommonFuncs::Operation::~Operation()
{
    _delegate.operationDone(_id);
}