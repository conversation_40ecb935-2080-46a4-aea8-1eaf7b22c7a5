#include "HvacMainCreateDialog.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/message/WDMessage.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "Common.h"
#include "businessModule/design/WDBMDesign.h"
#include "core/viewer/WDViewer.h"
#include "core/undoRedo/WDUndoStack.h"
#include "core/businessModule/WDBMAuthorityMgr.h"

constexpr const char* s_Type = "HVAC";

HvacMainCreateDialog::HvacMainCreateDialog(WD::WDCore& core, QWidget *parent)
    : QDialog(parent)
    , _core(core)
    , _nameHelpter(_core.getBMDesign())
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    // 初始化操作类型下拉框
    this->initOperationTypeCombo();
    // 初始化主体系统下拉框
    this->initPrimarySystemCombo();

    // 界面翻译
    this->retranslateUi();

    // 绑定事件通知响应
    connect(ui.pushButtonOk,        SIGNAL(clicked()), this, SLOT(slotOkClicked()));
    connect(ui.pushButtonCancel,    SIGNAL(clicked()), this, SLOT(slotCancelClicked()));

    _nameHelpter.setLineEdit(ui.lineEdit);
}
HvacMainCreateDialog::~HvacMainCreateDialog()
{
}

void HvacMainCreateDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);

    // 自动获取节点名称
    _nameHelpter.resetName();
}
void HvacMainCreateDialog::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
}

void HvacMainCreateDialog::slotOkClicked()
{
    auto operationType = (WD::OperationType)ui.comboBoxOperationType->currentData().value<int>();
    switch (operationType)
    {
    case WD::OT_Create:
        {
            // 创建管线节点
            this->createHVAC();
        }
        break;
    case WD::OT_Modify:
        break;
    default:
        break;
    }
}
void HvacMainCreateDialog::slotCancelClicked()
{
    this->reject();
}
void HvacMainCreateDialog::initOperationTypeCombo()
{
    ui.comboBoxOperationType->addItem("Create", WD::OT_Create);
    ui.comboBoxOperationType->addItem("Modify", WD::OT_Modify);

    // 默认设置第一个index
    ui.comboBoxOperationType->setCurrentIndex(0);
}
void HvacMainCreateDialog::initPrimarySystemCombo()
{
    ui.comboBoxPrincipalSys->addItem("No System", PS_None);

    // 默认设置第一个index
    ui.comboBoxPrincipalSys->setCurrentIndex(0);
}

void HvacMainCreateDialog::createHVAC()
{
    // 可创建校验
    auto        pParent =   WD::ParentNode(_core.getBMDesign(), _core.nodeTree().currentNode(), s_Type);
    if (pParent == nullptr)
    {
        WD_WARN_T("ErrorHvacCreateDialog", "Invalid parent!");
        return;
    }

    // 申领节点
    if (!WD::Core().getBMDesign().authorityMgr().checkAdd({ pParent }))
        return ;

    if (_nameHelpter.exists())
    {
        WD_WARN_T("ErrorHvacCreateDialog", "Name is exists!");
        return;
    }
    std::string name = _nameHelpter.name();
    // 根据业务数据创建桥架主体节点
    auto pHVAC = _core.getBMDesign().create(pParent, "HVAC", name);
    if (pHVAC == nullptr)
    {
        WD_WARN_T("ErrorHvacCreateDialog", "Failed to create node!");
        return;
    }
    pHVAC->update();
    // 创建完成即默认加入到场景中
    _core.scene().add(pHVAC);
    _core.needRepaint();

    // 创建桥架主体节点后默认设置为当前节点
    _core.nodeTree().setCurrentNode(pHVAC);

    // 复制节点命令
    auto pCmdCreateNode = WD::WDBMBase::MakeCreatedCommand({pHVAC});
    if (pCmdCreateNode != nullptr)
    {
        _core.undoStack().push(pCmdCreateNode);
    }
    
    this->reject();
}

void HvacMainCreateDialog::retranslateUi()
{
    Trs("HvacMainCreateDialog"
        , static_cast<QDialog*>(this)
        , ui.labelDuctName
        , ui.labelPrincipalSys

        , ui.comboBoxOperationType
        , ui.comboBoxPrincipalSys

        , ui.pushButtonOk
        , ui.pushButtonCancel);
}
