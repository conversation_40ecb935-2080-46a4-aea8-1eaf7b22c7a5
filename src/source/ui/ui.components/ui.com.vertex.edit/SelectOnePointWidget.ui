<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SelectOnePointWidget</class>
 <widget class="QWidget" name="SelectOnePointWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>381</width>
    <height>180</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="0" column="0">
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <layout class="QGridLayout" name="gridLayout" columnstretch="0,1,0">
        <item row="1" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxX">
          <property name="minimum">
           <double>-999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999.000000000000000</double>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxY">
          <property name="minimum">
           <double>-999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999.000000000000000</double>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QLabel" name="labelRadius">
          <property name="text">
           <string>BevelRadius</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="labelX">
          <property name="text">
           <string>X</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QCheckBox" name="checkBoxX">
          <property name="text">
           <string>Lock</string>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <widget class="QCheckBox" name="checkBoxY">
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>Lock</string>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxZ">
          <property name="minimum">
           <double>-999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999.000000000000000</double>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="labelY">
          <property name="text">
           <string>Y</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="labelVertex">
          <property name="text">
           <string>Vertex</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1" colspan="2">
         <widget class="QSpinBox" name="spinBoxVertexIndex">
          <property name="minimum">
           <number>0</number>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButtonInsert">
          <property name="minimumSize">
           <size>
            <width>30</width>
            <height>30</height>
           </size>
          </property>
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="text">
           <string>Insert</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButtonDelete">
          <property name="minimumSize">
           <size>
            <width>30</width>
            <height>30</height>
           </size>
          </property>
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="text">
           <string>Delete</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButtonModify">
          <property name="minimumSize">
           <size>
            <width>30</width>
            <height>30</height>
           </size>
          </property>
          <property name="text">
           <string>Modify</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>spinBoxVertexIndex</tabstop>
  <tabstop>doubleSpinBoxX</tabstop>
  <tabstop>checkBoxX</tabstop>
  <tabstop>doubleSpinBoxY</tabstop>
  <tabstop>checkBoxY</tabstop>
  <tabstop>doubleSpinBoxZ</tabstop>
  <tabstop>pushButtonInsert</tabstop>
  <tabstop>pushButtonDelete</tabstop>
  <tabstop>pushButtonModify</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
