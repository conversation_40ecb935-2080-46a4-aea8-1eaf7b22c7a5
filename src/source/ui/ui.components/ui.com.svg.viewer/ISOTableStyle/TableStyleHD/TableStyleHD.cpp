#include    "TableStyleHD.h"
#include    <qdatetime.h>
#include    "core/businessModule/catalog/WDBMCatalog.h"
#include    "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include    "core/businessModule/WDBDBase.h"
#include    "WDPainter/WDIsoPainter/WDIsoSvgPainter.h"
#include    "WDPainter/WDIsoPainter/WDIsoDxfPainter.h"

WD_NAMESPACE_USE

// 标准大小,便于后续对表格进行缩放,不可轻易改动!
DVec2 StandardTitleBarSize = DVec2(200.0, 66.0);
DVec2 StandardSignBarSize = DVec2(194.0, 33.0);
DVec2 StandardPaperDescAreaSize = DVec2(180.0, 33.0);
class TableStyleHDPrivate
{
public:
    // 标题栏项
    struct TitleBarItem
    {
        // 坐标起始点
        DVec2 position;
        // 项的大小
        DVec2 size;
        // 标题
        QString title;
        // 文本内容
        QString text;
        // 文本的对齐方式
        WDAlign textAlign;
        // 文本的字体
        WDFontStyle textStyle;
        // 标题栏项默认标题在上,这里设置标题和文本的占行高的比例,标题为空时文本会占满item
        IVec2 proportion;
        // 当前项是否是logo项
        bool isLogo;
        TitleBarItem()
        {
            position = DVec2::Zero();
            size = DVec2::Zero();
            title = "";
            text = "";
            textAlign = {WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center};
            proportion = IVec2(1,2);
            isLogo = false;
        }
        // 项是否有效
        bool valid() const
        {
            return size != DVec2::Zero();
        }
    };
    using TitleBarItems = std::vector<TitleBarItem>;
    /**
    * @brief 绘制线框
    */
    static void    DrawLineSide(WDAbstractPainter2D& painter, ISOPaper& paper)
    {
        auto& size = paper.size();
        int width = size.width();
        int height = size.height();

        bool bDisplayBorderNum = paper.frame.bDisplayBorderNum;
        const double marginTop = static_cast<double>(paper.margins().top());
        const double marginBottom = static_cast<double>(paper.margins().bottom());
        const double marginLeft = static_cast<double>(paper.margins().left());
        const double marginRight = static_cast<double>(paper.margins().right());

        const auto innerTop      = paper.paintArea().top();
        const auto innerBottom   = paper.paintArea().bottom();
        const auto innerLeft     = paper.paintArea().left();
        const auto innerRight    = paper.paintArea().right();

        const auto outerTop      = paper.paintArea().top() - paper.frame.top;
        const auto outerBottom   = paper.paintArea().bottom() + paper.frame.bottom;
        const auto outerLeft     = paper.paintArea().left() - paper.frame.left;
        const auto outerRight    = paper.paintArea().right() + paper.frame.right;

        
        WDFontStyle fontStyle;
        fontStyle.family = "TrueType";

        // 背景使用白色填充
        painter.fillRect(DVec2(0), DVec2(width, height), WDShapeFillStyle(Color::white));
        // 内框
        painter.drawRect(DVec2(innerLeft, innerTop), DVec2(innerRight - innerLeft, innerBottom - innerTop), paper.frame.lineStyle);
        // 外框
        painter.drawRect(DVec2(outerLeft, outerTop), DVec2(outerRight - outerLeft, outerBottom - outerTop), paper.frame.lineStyle);

        // 线条间隔的像素宽度
        int lineInterval = 50.0;
        // 线条间隔的像素宽度
        int halfLineInterval = lineInterval / 2;
        // 计算边缘线条比其他线间隔宽出来的部分
        int margin = (width / 2) % lineInterval;
        margin = margin > halfLineInterval ? (margin - lineInterval) : margin;
        // 字体大小
        auto minLineSize = Min(Min(Min(marginTop, marginBottom), marginLeft), marginRight);
        fontStyle.fontSize = minLineSize * 0.8;
        // 画横向的线和数字,计算数字数量
        size_t numberCnt = (width / 2 - margin) / lineInterval * 2;
        std::vector<DVec2> lines;
        if (bDisplayBorderNum)
        {
            for (int numberIndex = 1; numberIndex < numberCnt; ++numberIndex)
            {
                int x = margin + numberIndex * lineInterval;
                // 顶面线和数据
                painter.drawText(ToString(numberIndex), DVec2(x - lineInterval, 0), DVec2(x, marginTop), fontStyle);
                lines.push_back(DVec2(x, 0));
                lines.push_back(DVec2(x, marginTop));
                // 底面线和数据
                painter.drawText(ToString(numberIndex), DVec2(x - lineInterval, height - marginBottom), DVec2(x, height), fontStyle);
                lines.push_back(DVec2(x, height - marginBottom));
                lines.push_back(DVec2(x, height));
            }
            // 这里画末尾的数字
            painter.drawText(ToString(numberCnt)
                , DVec2(width - margin - lineInterval, 0)
                , DVec2(width - margin, marginTop)
                ,  fontStyle);
            painter.drawText(ToString(numberCnt)
                , DVec2(width - margin - lineInterval, height - marginBottom)
                , DVec2(width - margin, height)
                , fontStyle);

            // 计算边缘线条比其他线间隔宽出来的部分
            margin = size_t(height / 2) % lineInterval;
            margin = margin > halfLineInterval ? (margin - lineInterval) : margin;
            StringVector words = { "A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z" };
            // 画纵向的线和字母,计算字母的数量
            size_t wordCnt = (height / 2 - margin) / lineInterval * 2;
            for (size_t index = 1; index < wordCnt; ++index)
            {
                // 因为字母是从底面向顶面增大，这里从底面向顶面画线和字母
                auto y = height - (margin + index * lineInterval);
                size_t wordIndex = (index - 1) % words.size();
                // 左面线和数据
                painter.drawText(words[wordIndex], DVec2(0, y), DVec2(marginLeft, y + lineInterval), fontStyle);
                lines.push_back(DVec2(0, y));
                lines.push_back(DVec2(marginLeft, y));
                // 右面线和数据
                painter.drawText(words[wordIndex], DVec2(width - marginRight, y), DVec2(width, y + lineInterval), fontStyle);
                lines.push_back(DVec2(width - marginRight, y));
                lines.push_back(DVec2(width, y));
            }
            // 这里画末尾的字母
            size_t wordIndex = (wordCnt - 1) % words.size();
            painter.drawText(words[wordIndex], DVec2(0, margin), DVec2(marginLeft, margin + lineInterval), fontStyle);
            painter.drawText(words[wordIndex], DVec2(width - marginRight, margin), DVec2(width, margin + lineInterval), fontStyle);
        }
        else
        {
            for (size_t numberIndex = 1; numberIndex < numberCnt; ++numberIndex)
            {
                auto x = margin + numberIndex * lineInterval;
                lines.push_back(DVec2(x, 0));
                lines.push_back(DVec2(x, marginTop));
                lines.push_back(DVec2(x, height - marginBottom));
                lines.push_back(DVec2(x, height));
            }
            // 计算边缘线条比其他线间隔宽出来的部分
            margin = float(size_t(height / 2) % lineInterval);
            margin = margin > halfLineInterval ? (margin - lineInterval) : margin;
            // 画纵向的线,计算线的数量
            size_t wordCnt = size_t((height / 2 - margin) / lineInterval) * 2;
            for (size_t index = 1; index < wordCnt; ++index)
            {
                auto y = height - (margin + index * lineInterval);
                lines.push_back(DVec2(0, y));
                lines.push_back(DVec2(marginLeft, y));
                lines.push_back(DVec2(width - marginRight, y));
                lines.push_back(DVec2(width, y));
            }
        }
        painter.drawLines(lines, paper.frame.lineStyle);
    }
    /**
    * @brief 获取标题栏的数据
    */
    static void    GetTitleBarData(WDNode& branch, ISOPaper& paper, TitleBarItems& items, const TableStyleHD& style)
    {
        const auto& titleBar = paper.titleBar;
        if (!titleBar.bVisible)
            return;
        const DVec2 position = titleBar.position();
        // 标题栏暂时用写死的字体族和大小
        const DVec2 size = DVec2(titleBar.width, titleBar.height);
        const double xScale = size.x / StandardTitleBarSize.x;
        const double yScale = size.y / StandardTitleBarSize.y;
        const DVec2 scale = DVec2(xScale, yScale);

        // 合法所有人
        items.push_back({});
        items.back().position = position + DVec2(0, 0) * scale;
        items.back().size = DVec2(164, 18) * scale;
        items.back().text = QString::fromUtf8(WD::WDTs("TableStyle", "HDCompany").c_str());
        items.back().textStyle.fontSize = 9 * yScale;
        items.back().textAlign = {WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center};

        // 项目名称
        items.push_back({});
        items.back().position = position + DVec2(0, 18) * scale;
        items.back().size = DVec2(164, 18) * scale;
        items.back().text = QString::fromUtf8(WD::WDTs("TableStyle", "HDProject").c_str());
        items.back().textStyle.fontSize = 7 * yScale;
        items.back().textAlign = {WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center};

        // logo
        items.push_back({});
        items.back().position = position + DVec2(164.0, 0) * scale;
        items.back().size = DVec2(36.0) * scale;
        items.back().isLogo = true;

        //创建者
        items.push_back({});
        items.back().position = position + DVec2(0, 36) * scale;
        items.back().size = DVec2(40, 15) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("TableStyle", "ISODesign").c_str());
        items.back().text = QString::fromUtf8("");
        items.back().textStyle.fontSize = 5 * yScale;

        // 技术引用
        items.push_back({});
        items.back().position = position + DVec2(40, 36) * scale;
        items.back().size = DVec2(40, 15) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("TableStyle", "ISOCheckY").c_str());
        items.back().text = QString::fromUtf8("");
        items.back().textStyle.fontSize = 5 * yScale;

        // 批准人
        items.push_back({});
        items.back().position = position + DVec2(80, 36) * scale;
        items.back().size = DVec2(40, 15) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("TableStyle", "ISOProcess").c_str());
        items.back().text = QString::fromUtf8("");
        items.back().textStyle.fontSize = 5 * yScale;

        // 版本修改
        items.push_back({});
        items.back().position = position + DVec2(120, 36) * scale;
        items.back().size = DVec2(80, 15) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("TableStyle", "VersionChange").c_str());
        items.back().text = QString::fromUtf8("");
        items.back().textStyle.fontSize = 4 * yScale;

        // 等轴测图纸编号
        items.push_back({});
        items.back().position = position + DVec2(0, 51) * scale;
        items.back().size = DVec2(95, 15) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("TableStyle", "ISOPaperNumber").c_str());
        items.back().text = QString::fromUtf8(branch.name().c_str());
        items.back().textStyle.fontSize = 5 * yScale;

        // 日期
        items.push_back({});
        items.back().position = position + DVec2(95, 51) * scale;
        items.back().size = DVec2(65, 15) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("TableStyle", "Date").c_str());
        items.back().text = QDate::currentDate().toString("yyyy/MM/dd");
        items.back().textStyle.fontSize = 5 * yScale;

        // 版本
        items.push_back({});
        items.back().position = position + DVec2(160, 51) * scale;
        items.back().size = DVec2(20, 15) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("TableStyle", "VersionB").c_str());
        items.back().text = QString::fromUtf8("A");
        items.back().textStyle.fontSize = 5 * yScale;

        // 页码
        items.push_back({});
        items.back().position = position + DVec2(180, 51) * scale;
        items.back().size = DVec2(20, 15) * scale;
        {
            char text[64] = { 0 };
            sprintf_s(text, sizeof(text), WD::WDTs("TableStyle", "PageNumber").c_str(), static_cast<int>(style.paperIndex));
            items.back().title = QString::fromUtf8(text);
        }
        {
            char text[64] = { 0 };
            sprintf_s(text, sizeof(text), WD::WDTs("TableStyle", "NumberOfPages").c_str(), static_cast<int>(style.allPaperCnt));
            items.back().text = QString::fromUtf8(text);
        }
        items.back().textStyle.fontSize = 5 * yScale;
    }
    /**
    * @brief 绘制标题栏区
    */
    static void    DrawTitleBar(WDAbstractPainter2D& painter, ISOPaper& paper, const TitleBarItems& items)
    {
        auto& titleBar = paper.titleBar;
        if (items.empty() || !titleBar.bVisible)
            return;

        WDFontStyle fontStyle;
        fontStyle.fontSize = 3.5;

        // 这里外边框的起始位置取items的第一项的位置
        auto& position = items[0].position;
        if (titleBar.bVisibleGrid)
            painter.drawRect(position,DVec2(titleBar.width, titleBar.height), titleBar.outerBorderLine);

        for(auto& item : items)
        {
            if (!item.valid())
                continue;
            DVec2 titlePosS = item.position;
            auto& size = item.size;
            if (titleBar.bVisibleGrid)
                painter.drawRect(item.position, size, titleBar.innerBorderLine);
            IVec2 proportion = item.proportion;
            proportion.x = Max(proportion.x, 1);
            proportion.y = Max(proportion.y, 1);

            if (item.title.isEmpty())
            {
                DVec2 textPosS = item.position;
                DVec2 textPosE = item.position + size;
                painter.drawText(item.text.toUtf8().data(), textPosS, textPosE, item.textStyle, item.textAlign, true);
                if (item.isLogo)
                {
                    // 这里计算logo项的size(以短边为宽，做正方形)
                    auto minLeng = Min(size.x, size.y);

                    char logoPath[1024] = { 0 };
                    sprintf_s(logoPath, sizeof(logoPath), "%s/iso/logo/logo.png", WD::Core().dataDirPath());
                    WDImage image;
                    image.load(logoPath);
                    // -3 是因为图标的形状不是标准的正方形， -3让他看起来像正方形  + ((minLeng - size.x) / 2, (minLeng - size.y) / 2 + 1.5)是让图标偏移，使其在项的中心
                    painter.drawImage(item.position + DVec2((minLeng - size.x) / 2, (minLeng - size.y) / 2 + 1.5), image, DVec2(minLeng, minLeng - 3));
                }
            }
            else
            {
                auto titlePosE = titlePosS + DVec2(size.x, size.y / (proportion.x + proportion.y) * proportion.x);
                // 标题为左对齐, 这里加 2.0 是为了让标题的位置看起来更协调
                titlePosS.x += 2.0;
                painter.drawText(item.title.toUtf8().data(), titlePosS, titlePosE, fontStyle, {WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center}, true);
                auto textPosS = DVec2(item.position.x, titlePosE.y);
                auto textPosE = item.position + size;
                painter.drawText(item.text.toUtf8().data(), textPosS, textPosE, item.textStyle, item.textAlign, true);
            }
        }
    }
    /**
    * @brief 绘制签署栏区
    */
    static void    DrawSignBar(WDAbstractPainter2D& painter, ISOPaper& paper, const TitleBarItems& items)
    {
        auto& signBar = paper.signBar;
        if (!signBar.bVisible)
        {
            assert(false);
            return;
        }

        const DVec2 position = paper.titleBar.position();
        const DVec2 size = DVec2(signBar.width, signBar.height);
        const double xScale = size.x / StandardSignBarSize.x;
        const double yScale = size.y / StandardSignBarSize.y;

        // 设置材料区需要绘制的列, 暂时写死要绘制的内容
        // 图纸说明
        ISOTableWidget signBarTable;
        signBarTable.bGridVisible = signBar.bVisibleGrid;
        signBarTable.setTableHeight(signBar.height * yScale);
        signBarTable.innerBorderLine = signBar.innerBorderLine;
        signBarTable.outerBorderLine = signBar.outerBorderLine;
        signBarTable.innerBorderLine.width = signBarTable.innerBorderLine.width;
        signBarTable.outerBorderLine.width = signBarTable.outerBorderLine.width;
        int rowCnt = 4;
        int colCnt = 6;
        signBarTable.setSizeMatrix(rowCnt, colCnt);
        signBarTable.setHorizontalTexts({WD::WDTs("TableStyle", "VersionB")
            ,  WD::WDTs("TableStyle", "Date")
            ,  WD::WDTs("TableStyle", "Describe")
            ,  WD::WDTs("TableStyle", "ISODesign")
            ,  WD::WDTs("TableStyle", "ISOCheckY")
            ,  WD::WDTs("TableStyle", "ISOProcess")}, 3);
        WDFontStyle font;
        font.fontSize = paper.signBar.textFont.fontSize;
        font.weight = WDFontStyle::Bold;
        signBarTable.setRowFont(font, rowCnt - 1);
        std::vector<double> vec = signBar.colWidths;
        for (auto& each : vec)
            each = each * xScale;
        signBarTable.tableDistribution.horizontal = ISOTableHorizontal::H_AllFixed;
        signBarTable.setColWidths(vec);
        signBarTable.position = signBar.position();

        // 这里从材料表的数据中获取 版本/日期/设计/校验/审核的数据，理论上应该是从表格中获取，但因为现在暂时材料表不是真正的表格，这里暂时遍历其数据获取
        for (auto& item : items)
        {
            if (item.title == QString::fromUtf8(WD::WDTs("TableStyle", "VersionB").c_str()))
                signBarTable.setText(2, 0, item.text.toUtf8().data());
            else if (item.title == QString::fromUtf8(WD::WDTs("TableStyle", "Date").c_str()))
                signBarTable.setText(2, 1, item.text.toUtf8().data());
            else if (item.title == QString::fromUtf8(WD::WDTs("TableStyle", "ISODesign").c_str()))
                signBarTable.setText(2, 3, item.text.toUtf8().data());
            else if (item.title == QString::fromUtf8(WD::WDTs("TableStyle", "ISOCheckY").c_str()))
                signBarTable.setText(2, 4, item.text.toUtf8().data());
            else if (item.title == QString::fromUtf8(WD::WDTs("TableStyle", "ISOProcess").c_str()))
                signBarTable.setText(2, 5, item.text.toUtf8().data());
        }
        // 这个暂时写死
        signBarTable.setText(2, 2, WD::WDTs("TableStyle", "HDFirstDesign"));
        signBarTable.setColAlign(WDAlign(), 0);

        font.fontSize = paper.signBar.textFont.fontSize * yScale;
        font.weight = WDFontStyle::Normal;

        signBarTable.update(painter, font, {WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center});
    }
    /**
    * @brief 绘制图纸说明
    */
    static void    DrawPaperDesc(WDAbstractPainter2D& painter, WDNode& branch, ISOPaper& paper)
    {
        auto& paperDescArea = paper.paperDescArea;
        if (!paperDescArea.bVisible)
        {
            assert(false);
            return;
        }

        const DVec2 size = DVec2(paperDescArea.width, paperDescArea.height);
        const double xScale = size.x / StandardPaperDescAreaSize.x;
        const double yScale = size.y / StandardPaperDescAreaSize.y;

        // 设置材料区需要绘制的列, 暂时写死要绘制的内容
        WDFontStyle font;
        // 图纸说明
        font.fontSize = 3.0;
        ISOTableWidget paperDescTable;
        paperDescTable.bGridVisible = paperDescArea.bVisibleGrid;
        paperDescTable.setTableHeight(paperDescArea.height * yScale);
        paperDescTable.innerBorderLine.width = 0.18;
        paperDescTable.outerBorderLine.width = 0.18;
        int rowCnt = 4;
        int colCnt = 8;
        font.weight = WDFontStyle::Bold;
        paperDescTable.setSizeMatrix(rowCnt, colCnt);

        paperDescTable.setVerticalTexts({
            WD::WDTs("TableStyle", "HDFirstRoom")
            , WD::WDTs("TableStyle", "HDPlant")
            , WD::WDTs("TableStyle", "PipingClass")
            , WD::WDTs("TableStyle", "HDNominalDiameter")}, 0);
        paperDescTable.setColFont(font, 0);
        paperDescTable.setVerticalTexts({
            WD::WDTs("TableStyle", "RCCM")
            , WD::WDTs("TableStyle", "HDDesignTemperature")
            , WD::WDTs("TableStyle", "HDDesignPressure")
            , WD::WDTs("TableStyle", "HDTestPressure")}, 2);
        paperDescTable.setColFont(font, 2);
        paperDescTable.setVerticalTexts({
            WD::WDTs("TableStyle", "InsulationThickness")
            , WD::WDTs("TableStyle", "InsulationLevel")
            , WD::WDTs("TableStyle", "InsulationMat")
            , WD::WDTs("TableStyle", "Tracing")}, 4);
        paperDescTable.setColFont(font, 4);
        paperDescTable.setVerticalTexts({
            WD::WDTs("TableStyle", "SafeLevel")
            , WD::WDTs("TableStyle", "QualityLevel")
            , WD::WDTs("TableStyle", "VacuumLevel")
            , WD::WDTs("TableStyle", "SeismicLevel")}, 6);
        paperDescTable.setColFont(font, 6);
        std::vector<double> vec = paperDescArea.colWidths;
        for (auto& each : vec)
            each = each * xScale;

        paperDescTable.tableDistribution.horizontal = ISOTableHorizontal::H_AllFixed;
        paperDescTable.setColWidths(vec);
        paperDescTable.position = paperDescArea.position();

        if (branch.isType("BRAN"))
        {
            auto RemoveRedundant0 = [](std::string& str)
            {
                if (str.find('.') == std::string::npos)
                    return;
                while(!str.empty())
                {
                    if (str.back() == '.')
                    {
                        str.pop_back();
                        return;
                    }
                    else if (str.back() == '0')
                    {
                        str.pop_back();
                    }
                    else
                    {
                        return;
                    }
                }
            };

            char value[1024] = { 0 };
            std::string str;
            // 这里暂时手动的写死已知数据来源的数据

            // 管道等级
            const auto pSpec = branch.getAttribute("Pspec").toNodeRef();
            if (pSpec.valid())
            {
                sprintf_s(value, sizeof(value), "%s",pSpec.refNode()->name().c_str());
                paperDescTable.setText(2, 1, value);
            }

            // 公称直径
            int diameter = 0;
            // 先尝试在branch下面的管道中获取公称直径
            for (auto& pChild : branch.children())
            {
                if (pChild == nullptr || !pChild->isType("TUBI"))
                    continue;
                bool bOk = false;
                diameter = int(pChild->getAttribute("Abore").toDouble(&bOk));
                if (bOk)
                    break;
                diameter = 0;
            }
            // 如果branch下面的管道中获取公称直径失败,则尝试在管件中获取
            if (diameter == 0)
            {
                for (auto& pChild : branch.children())
                {
                    if (pChild == nullptr || !WDBMDPipeUtils::IsPipeComponent(*pChild))
                        continue;
                    bool bOk = false;
                    diameter = int(pChild->getAttribute("Abore").toDouble(&bOk));
                    if (bOk)
                        break;
                    diameter = 0;
                }
            }
            if (diameter != 0)
            {
                memset(value, 0, sizeof(value));
                sprintf_s(value, sizeof(value), "%d", diameter);
                paperDescTable.setText(3, 1, value);
            }

            // 设计温度
            memset(value, 0, sizeof(value));
            auto temperature = branch.getAttribute("Temperature").toDouble();
            sprintf_s(value, sizeof(value), "%.2lf", temperature);
            str = std::string(value);
            RemoveRedundant0(str);
            paperDescTable.setText(1, 3, str);

            // 设计压力 (获取的压力单位是pa，这里需要的单位是barg        1barg = 100000pa)
            memset(value, 0, sizeof(value));
            auto pressure = branch.getAttribute("Pressure").toDouble() / 100000.0;
            sprintf_s(value, sizeof(value), "%.2lf", pressure);
            str = std::string(value);
            RemoveRedundant0(str);
            paperDescTable.setText(2, 3, str);

            // 测试压力  (获取的压力单位是pa，这里需要的单位是barg        1barg = 100000pa)(测试压力为空时 取设计压力的1.5倍值)
            memset(value, 0, sizeof(value));
            auto testPre = branch.getAttribute("TPressure").toDouble() / 100000.0;
            if (testPre < NumLimits<double>::Epsilon)
                testPre = 1.5 * pressure;
            sprintf_s(value, sizeof(value), "%.2lf", testPre);
            str = std::string(value);
            RemoveRedundant0(str);
            paperDescTable.setText(3, 3, str);

            // 保温厚度
            std::optional<std::vector<double>> thickness = std::nullopt;
            for (auto& pChild : branch.children())
            {
                if (pChild == nullptr)
                    continue;
                thickness = pChild->getAttribute("Iparam").toDoubleVector();
                if (thickness)
                    break;
            }
            if (thickness && thickness.value().size() != 0)
            {
                memset(value, 0, sizeof(value));
                sprintf_s(value, sizeof(value), "P(%.2lf)", thickness.value().at(0));
                str = std::string(value);
                RemoveRedundant0(str);
                paperDescTable.setText(0, 5, str);
            }
            else
            {
                paperDescTable.setText(0, 5, "P(-)");
            }

            // 保温等级
            const auto iSpec = branch.getAttribute("Ispec").toNodeRef();
            if (!iSpec.empty())
            {
                auto refNode = iSpec.refNode();
                memset(value, 0, sizeof(value));
                sprintf_s(value, sizeof(value), "%s", refNode->name().c_str());
                paperDescTable.setText(1, 5, value);
            }
            // 保温材料

            // 伴热
            const auto tSpec =  branch.getAttribute("Tspec").toNodeRef();
            if (tSpec.empty())
                paperDescTable.setText(3, 5, "N");
            else
                paperDescTable.setText(3, 5, "Y");
        }
        paperDescTable.setColAlign(WDAlign(), 0);
        font.fontSize = paper.paperDescArea.textFont.fontSize * yScale;
        font.weight = WDFontStyle::Normal;
        paperDescTable.update(painter, font, {WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center});
    }
    /**
    * @brief 绘制图例
    */
    static void    DrawLegend(WDAbstractPainter2D& painter, ISOPaper& paper)
    {
        DVec2 position = DVec2::Zero();
        switch (paper.signBar.align)
        {
        case ISOSignBarArea::SBA_Left:
            position = paper.signBar.position();
            break;
        case ISOSignBarArea::SBA_Top:
            position = paper.paperDescArea.position();
            break;
        default:
            {
                assert(false);
                position = DVec2::Zero();
            }
            break;
        }
        // 设置材料区需要绘制的列, 暂时写死要绘制的内容
        // 图纸说明
        WDFontStyle fontStyle;
        fontStyle.family = WD::WDTs("Common", "FangSong");
        fontStyle.fontSize = 3.5;
        WDLineStyle lineStyle;
        lineStyle.color = WD::Color::black;
        WDShapeFillStyle shapeStyle;
        shapeStyle.color = WD::Color::black;
        // 绘制图例
        std::string str = "SHOP WELD";
        // (有粗实线的)图例的宽(即线长)
        int legendWidth = 14.0;
        // 图例的高
        int legendHeight = 5.0;
        int halfLegendWidth = legendWidth / 2.0;
        // 项的宽度等于字符串和图例中宽度的较大值的1.2倍
        auto strSize = painter.calcTextSize(str, fontStyle);
        auto itemWidth = Max(int(strSize.x), legendWidth) * 1.2;
        DVec2 posS = position + DVec2(3, -float(legendHeight) / 2.0);
        DVec2 posE = DVec2(posS.x + itemWidth, posS.y);
        auto strHeight = strSize.y;

        painter.drawText(std::string("Indicated length are theoretical and must be checked on site before piping fabrication")
            , (posS - DVec2(1, (strHeight + legendHeight) * 1.2))
            , fontStyle
            , WDAlign{ WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Bottom });

        auto center = (posS + posE) / 2;
        lineStyle.width = 1;
        painter.drawLine(center - DVec2(halfLegendWidth, 0), center + DVec2(halfLegendWidth, 0), lineStyle);
        painter.fillCircle(center, 0.75, shapeStyle);
        painter.drawText(str, center - DVec2(0, legendHeight), fontStyle);

        posS = posE;
        str = "FIELD WELD";
        itemWidth = Max(int(painter.calcTextSize(str, fontStyle).x), legendWidth) * 1.2;
        posE = posS + DVec2(itemWidth, 0);
        center = (posS + posE) / 2;
        painter.drawLine(center - DVec2(halfLegendWidth, 0), center + DVec2(halfLegendWidth, 0), lineStyle);
        painter.fillCircle(center, 0.75, shapeStyle);
        lineStyle.width = 0.2;
        painter.drawLines({center - 1.5, center + 1.5, center + DVec2(-1.5, 1.5), center + DVec2(1.5, -1.5)}, lineStyle);
        painter.drawText(str, center - DVec2(0, legendHeight), fontStyle);

        posS = posE;
        str = "SOCKET WELD";
        itemWidth = Max(int(painter.calcTextSize(str, fontStyle).x), legendWidth) * 1.2;
        posE = posS + DVec2(itemWidth, 0);
        center = (posS + posE) / 2;
        lineStyle.width = 1;
        painter.drawLine(center - DVec2(halfLegendWidth, 0), center + DVec2(halfLegendWidth, 0), lineStyle);
        painter.fillCircle(center, 0.75, shapeStyle);
        lineStyle.width = 0.2;
        painter.drawPolyLines({center + DVec2(1.5, -1.5), center - DVec2(0, 1.5), center + DVec2(0, 1.5), center + 1.5}, lineStyle);
        painter.drawText(str, center - DVec2(0, legendHeight), fontStyle);

        posS = posE;
        str = "SCREWED JOINT";
        itemWidth = Max(int(painter.calcTextSize(str, fontStyle).x), legendWidth) * 1.2;
        posE = posS + DVec2(itemWidth, 0);
        center = (posS + posE) / 2;
        lineStyle.width = 1;
        painter.drawLine(center - DVec2(halfLegendWidth, 0), center + DVec2(halfLegendWidth, 0), lineStyle);
        lineStyle.width = 0.2;
        painter.drawPolyLines({center + DVec2(1.5, -1.5), center - DVec2(0, 1.5), center + DVec2(0, 1.5), center + 1.5}, lineStyle);
        painter.drawText(str, center - DVec2(0, legendHeight), fontStyle);

        posS = posE;
        str = "PIPE SUPPORT";
        itemWidth = Max(int(painter.calcTextSize(str, fontStyle).x), legendWidth) * 1.2;
        posE = posS + DVec2(itemWidth, 0);
        center = (posS + posE) / 2;
        lineStyle.width = 1;
        painter.drawLine(center - DVec2(halfLegendWidth, 0), center + DVec2(halfLegendWidth, 0), lineStyle);
        lineStyle.width = 0.2;
        painter.drawLines({center + DVec2(1.5, -1.5), center - DVec2(1.5), center + DVec2(-1.5, 1.5), center + DVec2(1.5)}, lineStyle);
        painter.drawText(str, center - DVec2(0, legendHeight), fontStyle);

        posS = posE;
        str = "INSULATED PIPE";
        itemWidth = Max(int(painter.calcTextSize(str, fontStyle).x), legendWidth) * 1.2;
        posE = posS + DVec2(itemWidth, 0);
        center = (posS + posE) / 2;
        lineStyle.width = 1;
        painter.drawLine(center - DVec2(halfLegendWidth, 0), center + DVec2(halfLegendWidth, 0), lineStyle);
        lineStyle.width = 0.2;
        lineStyle.type = WDLineStyle::LT_Dash;
        lineStyle.dashArray = {3, 1};
        painter.drawLine(center - DVec2(halfLegendWidth, 1.5), center + DVec2(halfLegendWidth, -1.5), lineStyle);
        painter.drawLine(center - DVec2(halfLegendWidth, -1.5), center + DVec2(halfLegendWidth, 1.5), lineStyle);
        lineStyle.type = WDLineStyle::LT_Solid;
        painter.drawText(str, center - DVec2(0, legendHeight), fontStyle);

        posS = posE;
        str = "TRACED PIPE";
        itemWidth = Max(int(painter.calcTextSize(str, fontStyle).x), legendWidth) * 1.2;
        posE = posS + DVec2(itemWidth, 0);
        center = (posS + posE) / 2;
        lineStyle.width = 1;
        painter.drawLine(center - DVec2(halfLegendWidth, 0), center + DVec2(halfLegendWidth, 0), lineStyle);
        lineStyle.width = 0.2;
        lineStyle.type = WDLineStyle::LT_Dash;
        painter.drawLine(center - DVec2(halfLegendWidth, -1.5), center + DVec2(halfLegendWidth, 1.5), lineStyle);
        lineStyle.type = WDLineStyle::LT_Solid;
        painter.drawText(str, center - DVec2(0, legendHeight), fontStyle);

        posS = posE;
        str = "ITEM NO.";
        itemWidth = Max(int(painter.calcTextSize(str, fontStyle).x), legendWidth) * 1.2;
        posE = posS + DVec2(itemWidth, 0);
        center = (posS + posE) / 2;
        painter.drawRect(center - DVec2(0.75, 1.5), DVec2(1.5, 3), lineStyle);
        painter.drawText(str, center - DVec2(0, legendHeight), fontStyle);

        posS = posE;
        str = "WELD NO.";
        itemWidth = Max(int(painter.calcTextSize(str, fontStyle).x), legendWidth) * 1.2;
        posE = posS + DVec2(itemWidth, 0);
        center = (posS + posE) / 2;
        painter.drawCircle(center, 1.5, lineStyle);
        painter.drawText(str, center - DVec2(0, legendHeight), fontStyle);

        posS = posE;
        str = "FLOW DIRECTION";
        itemWidth = Max(int(painter.calcTextSize(str, fontStyle).x), legendWidth) * 1.2;
        posE = posS + DVec2(itemWidth, 0);
        center = (posS + posE) / 2;
        lineStyle.width = 1;
        painter.drawLine(center - DVec2(halfLegendWidth, 0), center + DVec2(halfLegendWidth, 0), lineStyle);
        painter.fillPolygon({center - DVec2(3, 0), center + DVec2(1.5, -1.5), center + DVec2(2.5, 1.2)}, shapeStyle);
        painter.drawText(str, center - DVec2(0, legendHeight), fontStyle);
    }
};
TableStyleHD::TableStyleHD(WDCore& core) : TableStyleBase(core)
{
}

TableStyleHD::~TableStyleHD()
{
}

void TableStyleHD::init(ISOPaper& paper) const
{
    // 图纸大小
    ISOPaper::Size paperSize = ISOPaper::Size(594.0, 420.0);
    // 图纸边缘留白
    ISOPaper::Margins paperMargins = ISOPaper::Margins(10.0, 10.0, 10.0, 10.0);
    // 图纸边框大小
    ISOPaper::Margins frameMargins = ISOPaper::Margins(10.0, 10.0, 10.0, 10.0);

    paper.setSize(paperSize);
    paper.setMargin(paperMargins);
    paper.frame = ISOPaper::Frame(10.0, 10.0, 10.0, 10.0, 0.35, true);
    // 材料表区
    auto& materialTable = paper.materialTable;
    materialTable.innerBorderLine.width = 0.18f;
    materialTable.outerBorderLine.width = 0.18f;
    for (auto& i : ISOMaterialArea::AllFlags)
    {
        if (!materialTable.flags.hasFlag(i))
        {
            continue;
        }
        materialTable.flags.removeFlag(i);
    }
    materialTable.flags.addFlags(ISOMaterialArea::MPF_Number
        , ISOMaterialArea::MPF_Descrip
        , ISOMaterialArea::MPF_Spec
        , ISOMaterialArea::MPF_Material
        , ISOMaterialArea::MPF_MaterialCode
        , ISOMaterialArea::MPF_Count
        , ISOMaterialArea::MPF_Weight
        , ISOMaterialArea::MPF_TotalWeight);

    materialTable.details = ISOMaterialArea::DT_RText;
    materialTable.texture = ISOMaterialArea::TT_XText;
    materialTable.align   = ISOMaterialArea::MTA_RightTop;

    // 列宽和表头名称应该由界面上根据设置的flag及对应的信息决定，这里暂时写死
    materialTable.colWidths = {8.0, 88.0, 16.0, 24.0, 16.0, 16.0, 16.0, 16.0};
    materialTable.width = 0.0;
    for (auto& each : materialTable.colWidths)
        materialTable.width += each;
    materialTable.defaultRowHeight = 6;
    materialTable.bVisibleGrid = true;

    auto& titleBar = paper.titleBar;
    // 标题栏
    titleBar.innerBorderLine.width = 0.18f;
    titleBar.outerBorderLine.width = 0.18f;
    titleBar.width = 200.0;
    titleBar.height = 66.0;
    titleBar.align = ISOTitleBarArea::TBA_RightBottom;
    // 签名栏
    auto& signBar = paper.signBar;
    signBar.innerBorderLine.width = 0.18f;
    signBar.outerBorderLine.width = 0.18f;
    signBar.height = 33.0;
    signBar.textFont.fontSize = 3.5;
    signBar.align = ISOSignBarArea::SBA_Left;
    signBar.colWidths = {13.0, 27.0, 103.0, 17.0, 17.0 ,17.0};
    signBar.width = 0.0;
    for (auto& each : signBar.colWidths)
        signBar.width += each;
    // 图纸描述区
    auto& paperDescArea = paper.paperDescArea;
    paperDescArea.innerBorderLine.width = 0.18f;
    paperDescArea.outerBorderLine.width = 0.18f;
    paperDescArea.height = 33.0;
    paperDescArea.textFont.fontSize = 3.5;
    paperDescArea.colWidths = {22.5, 22.5, 22.5, 22.5, 22.5, 22.5, 22.5, 22.5};
    paperDescArea.width = 0.0;
    for (auto& each : paperDescArea.colWidths)
        paperDescArea.width += each;
    paperDescArea.setPosition(DVec2(paper.paintArea().left() + signBar.width, paper.paintArea().bottom() - paperDescArea.height));
    paper.update(std::nullopt);
}

void TableStyleHD::exec(WDISOPainterCmd& cmdPainter
    , WD::WDNode& branNode
    , ISOPaper& paper
    , const TuLine& line) const
{
    paper.update(branNode.aabb());
    // 绘制图纸的边框
    cmdPainter.appendGroup("sideArea");
    TableStyleHDPrivate::DrawLineSide(cmdPainter, paper);
    // 绘制标题栏区
    TableStyleHDPrivate::TitleBarItems items;
    TableStyleHDPrivate::GetTitleBarData(branNode, paper, items, *this);
    cmdPainter.appendGroup("titleBar");
    TableStyleHDPrivate::DrawTitleBar(cmdPainter, paper, items);
    // 绘制签署栏
    cmdPainter.appendGroup("signBar");
    TableStyleHDPrivate::DrawSignBar(cmdPainter, paper, items);
    // 绘制图纸说明
    cmdPainter.appendGroup("papterDescription");
    TableStyleHDPrivate::DrawPaperDesc(cmdPainter, branNode, paper);
    // 绘制图例
    cmdPainter.appendGroup("legend");
    TableStyleHDPrivate::DrawLegend(cmdPainter, paper);
    // 统计并更新材料表
    cmdPainter.appendGroup("MaterialTable");
    UpdateMaterialTable(cmdPainter, _core, &line, paper, this->style());
}


DependJsonMgr* TableStyleHD::dependJsonMgr()
{
    return nullptr;
}

void TableStyleHD::setDependJsonMgr(DependJsonMgr* pJsonMgr)
{
    WDUnused(pJsonMgr);
}
