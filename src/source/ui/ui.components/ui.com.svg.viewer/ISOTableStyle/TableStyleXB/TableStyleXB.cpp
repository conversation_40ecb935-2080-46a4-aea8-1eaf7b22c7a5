#include    "TableStyleXB.h"
#include    <qdatetime.h>
#include    "core/businessModule/catalog/WDBMCatalog.h"
#include    "core/businessModule/WDBDBase.h"
#include    "WDPainter/WDIsoPainter/WDIsoSvgPainter.h"
#include    "WDPainter/WDIsoPainter/WDIsoDxfPainter.h"
#include    "CSDataJson/CSJsonMgr.h"
#include    "CSDataJson/DesignParameterJson.h"
#include    "../../MaterialDataStatistics/MaterialDataStatisticsHelper.h"

WD_NAMESPACE_USE

// 标准大小,便于后续对表格进行缩放,不可轻易改动!
DVec2 StandardTitleBarSizeXB = DVec2(180.0, 40.0);
DVec2 StandardSignBarSizeXB = DVec2(180.0, 35.0);
DVec2 StandardPaperDescAreaSizeXB = DVec2(180.0, 33.0);
class TableStyleXBPrivate
{
public:
    // 标题栏项
    struct TitleBarItem
    {
        // 坐标起始点
        DVec2 position;
        // 项的大小
        DVec2 size;
        // 标题
        QString title;
        // 文本内容
        QString text;
        //多行文本内容
        std::vector<QString> texts;
        // 文本的对齐方式
        WDAlign textAlign;
        // 文本的字体
        WDFontStyle textStyle;
        // 标题栏项默认标题在上,这里设置标题和文本的占行高的比例,标题为空时文本会占满item
        IVec2 proportion;
        // 当前项是否是logo项
        bool isLogo;
        TitleBarItem()
        {
            position = DVec2::Zero();
            size = DVec2::Zero();
            title = "";
            text = "";
            textAlign = { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center };
            proportion = IVec2(1, 2);
            isLogo = false;
        }
        // 项是否有效
        bool valid() const
        {
            return size != DVec2::Zero();
        }
    };
    using TitleBarItems = std::vector<TitleBarItem>;
    static void    DrawTableItem(WDAbstractPainter2D& painter, ISOPaper& paper, const TitleBarItems& items,bool border = true)
    {
        if (items.empty())
            return;

        WDFontStyle fontStyle;
        fontStyle.fontSize = 1;

        // 这里外边框的起始位置取items的第一项的位置
        for (auto& item : items)
        {
            if (!item.valid())
                continue;
            DVec2 titlePosS = item.position;
            auto& size = item.size;
            if (border)
                painter.drawRect(item.position, size,paper.frame.lineStyle);
            IVec2 proportion = item.proportion;
            proportion.x = Max(proportion.x, 1);
            proportion.y = Max(proportion.y, 1);
            std::vector<std::string> texts;
            if (!item.texts.empty())
            {

                for (auto& i : item.texts)
                {
                    texts.emplace_back(i.toUtf8().data());
                }
            }
            else
            {
                texts.emplace_back(item.text.toUtf8().data());
            }

            if (item.title.isEmpty())
            {
                DVec2 textPosS = item.position;
                DVec2 textPosE = item.position + size;

                painter.drawTextV(texts, textPosS, textPosE, item.textStyle, item.textAlign);
            }
            else
            {
                auto titlePosE = titlePosS + DVec2(size.x, size.y / (proportion.x + proportion.y) * proportion.x);
                // 标题为左对齐, 这里加 2.0 是为了让标题的位置看起来更协调
                titlePosS.x += 2.0;
                painter.drawTextV({ item.title.toUtf8().data() }, titlePosS, titlePosE, fontStyle, { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center });
                auto textPosS = DVec2(item.position.x, titlePosE.y);
                auto textPosE = item.position + size;
                painter.drawTextV(texts, textPosS, textPosE, item.textStyle, item.textAlign);
            }
        }
    }
    /**
    * @brief 绘制线框
    */
    static void    DrawLineSide(WDAbstractPainter2D& painter, ISOPaper& paper)
    {
        auto& size = paper.size();
        int width = size.width();
        int height = size.height();

        bool bDisplayBorderNum = paper.frame.bDisplayBorderNum;
        const double marginTop = static_cast<double>(paper.margins().top());
        const double marginBottom = static_cast<double>(paper.margins().bottom());
        const double marginLeft = static_cast<double>(paper.margins().left());
        const double marginRight = static_cast<double>(paper.margins().right());

        const auto innerTop = paper.paintArea().top();
        const auto innerBottom = paper.paintArea().bottom();
        const auto innerLeft = paper.paintArea().left();
        const auto innerRight = paper.paintArea().right();

        const auto outerTop = paper.paintArea().top() - paper.frame.top;
        const auto outerBottom = paper.paintArea().bottom() + paper.frame.bottom;
        const auto outerLeft = paper.paintArea().left() - paper.frame.left;
        const auto outerRight = paper.paintArea().right() + paper.frame.right;


        WDFontStyle fontStyle;
        fontStyle.family = "TrueType";
        // 背景使用白色填充
        painter.fillRect(DVec2(0), DVec2(width, height), WDShapeFillStyle(Color::white));

        // 内框
        painter.drawRect(DVec2(innerLeft, innerTop), DVec2(innerRight - innerLeft, innerBottom - innerTop), paper.frame.lineStyle);
        // 外框
        painter.drawRect(DVec2(outerLeft, outerTop), DVec2(outerRight - outerLeft, outerBottom - outerTop), paper.frame.lineStyle);

        // 线条间隔的毫米宽度
        int lineInterval = 70.08;
        // 线条间隔的毫米宽度
        int halfLineInterval = lineInterval / 2;
        // 计算边缘线条比其他线间隔宽出来的部分
        int margin = (width / 2) % lineInterval;
        margin = margin > halfLineInterval ? (margin - lineInterval) : margin;
        // 字体大小
        auto minLineSize = Min(Min(Min(marginTop, marginBottom), marginLeft), marginRight);
        fontStyle.fontSize = minLineSize * 0.8;
        // 画横向的线和数字,计算数字数量
        size_t numberCnt = (width / 2 - margin) / lineInterval * 2;
        std::vector<DVec2> lines;
        if (bDisplayBorderNum)
        {
            for (int numberIndex = 1; numberIndex < numberCnt; ++numberIndex)
            {
                int x = margin + numberIndex * lineInterval;
                // 顶面线和数据
                painter.drawText(ToString(numberIndex), DVec2(x - lineInterval, 0), DVec2(x, marginTop), fontStyle);
                lines.push_back(DVec2(x, 0));
                lines.push_back(DVec2(x, marginTop));
                // 底面线和数据
                painter.drawText(ToString(numberIndex), DVec2(x - lineInterval, height - marginBottom), DVec2(x, height), fontStyle);
                lines.push_back(DVec2(x, height - marginBottom));
                lines.push_back(DVec2(x, height));
            }
            // 这里画末尾的数字
            painter.drawText(ToString(numberCnt)
                , DVec2(width - margin - lineInterval, 0)
                , DVec2(width - margin, marginTop)
                , fontStyle);
            painter.drawText(ToString(numberCnt)
                , DVec2(width - margin - lineInterval, height - marginBottom)
                , DVec2(width - margin, height)
                , fontStyle);

            // 计算边缘线条比其他线间隔宽出来的部分
            margin = size_t(height / 2) % lineInterval;
            margin = margin > halfLineInterval ? (margin - lineInterval) : margin;
            StringVector words = { "A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z" };
            // 画纵向的线和字母,计算字母的数量
            size_t wordCnt = (height / 2 - margin) / lineInterval * 2;
            for (size_t index = 0; index < wordCnt - 1; ++index)
            {
                // 因为字母是从顶面向底面增大，这里从顶面向底面画线和字母
                auto y = margin + index * lineInterval;
                size_t wordIndex = (index) % words.size();
                // 左面线和数据
                painter.drawText(words[wordIndex], DVec2(0, y), DVec2(marginLeft, y + lineInterval), fontStyle);

                if (index != 0)
                {
                    lines.push_back(DVec2(0, y));
                    lines.push_back(DVec2(marginLeft, y));
                }
                // 右面线和数据
                painter.drawText(words[wordIndex], DVec2(width - marginRight, y), DVec2(width, y + lineInterval), fontStyle);
                if (index != 0)
                {
                    lines.push_back(DVec2(width - marginRight, y));
                    lines.push_back(DVec2(width, y));
                }
            }
            // 这里不画左边的末尾的字母
            size_t wordIndex = (wordCnt - 1) % words.size();
            //painter.drawText(words[wordIndex], DVec2(0, margin), DVec2(marginLeft, margin + lineInterval), fontStyle);
            lines.push_back(DVec2(0, height - margin - lineInterval));
            lines.push_back(DVec2(marginLeft - 10, height - margin - lineInterval));
            lines.push_back(DVec2(width - marginRight, height - margin - lineInterval));
            lines.push_back(DVec2(width, height - margin - lineInterval));
            painter.drawText(words[wordIndex], DVec2(width - marginRight, height - margin - lineInterval), DVec2(width, height - margin), fontStyle);
        }
        else
        {
            for (size_t numberIndex = 1; numberIndex < numberCnt; ++numberIndex)
            {
                auto x = margin + numberIndex * lineInterval;
                lines.push_back(DVec2(x, 0));
                lines.push_back(DVec2(x, marginTop));
                lines.push_back(DVec2(x, height - marginBottom));
                lines.push_back(DVec2(x, height));
            }
            // 计算边缘线条比其他线间隔宽出来的部分
            margin = float(size_t(height / 2) % lineInterval);
            margin = margin > halfLineInterval ? (margin - lineInterval) : margin;
            // 画纵向的线,计算线的数量
            size_t wordCnt = size_t((height / 2 - margin) / lineInterval) * 2;
            for (size_t index = 1; index < wordCnt; ++index)
            {
                auto y = height - (margin + index * lineInterval);
                lines.push_back(DVec2(0, y));
                lines.push_back(DVec2(marginLeft, y));
                lines.push_back(DVec2(width - marginRight, y));
                lines.push_back(DVec2(width, y));
            }
        }
        painter.drawLines(lines, paper.frame.lineStyle);
        //添加左下角甲级资质证书编号
        //包裹着这个文字的边框
        auto rectWidth = 10;//10毫米
        auto rectHeight = 90;//90毫米
        auto posi = DVec2(innerLeft - rectWidth/2, innerBottom - rectHeight);
        auto text2 = WD::WDTs("ISOIndexTable", "CertificateNumberXB");
        fontStyle.fontSize = 3.5;
        painter.drawText(text2, posi, fontStyle, { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center } ,-90);
        painter.drawRect(DVec2(innerLeft - rectWidth, innerBottom - rectHeight), DVec2(rectWidth, rectHeight), paper.frame.lineStyle);
    }
    /**
    * @brief 获取标题栏的数据
    */
    static void    GetTitleBarData(WDNode& branch, ISOPaper& paper, TitleBarItems& items, const TableStyleXB& style)
    {
        const auto& titleBar = paper.titleBar;
        if (!titleBar.bVisible)
            return;
        const DVec2 position = titleBar.position();
        // 标题栏暂时用写死的字体族和大小
        const DVec2 size = DVec2(titleBar.width, titleBar.height);
        const double xScale = size.x / StandardTitleBarSizeXB.x;
        const double yScale = size.y / StandardTitleBarSizeXB.y;
        const DVec2 scale = DVec2(xScale, yScale);


        items.push_back({});
        items.back().position = position + DVec2(0, 0) * scale;
        items.back().size = DVec2(40, 40) * scale;
        items.back().textStyle.fontSize = 9 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        items.push_back({});
        items.back().position = position + DVec2(40, 0) * scale;
        items.back().size = DVec2(40, 20) * scale;
        items.back().textStyle.fontSize = 9 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(40, 20) * scale;
        items.back().size = DVec2(40, 13) * scale;
        items.back().textStyle.fontSize = 9 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };


        // 项目名称
        items.push_back({});
        items.back().position = position + DVec2(80, 0) * scale;
        items.back().size = DVec2(75, 12) * scale;
        items.back().text = QString::fromUtf8(WD::WDTs("ISOIndexTable", "ProjectNameXB").c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(80, 0) * scale;
        items.back().size = DVec2(75, 12) * scale;
        items.back().text = QString::fromUtf8(WD::WDTs("ISOIndexTable", "ProjectXB").c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Right, WDAlign::VAlign::VA_Center };

        // 项目
        items.push_back({});
        items.back().position = position + DVec2(80, 0) * scale;
        items.back().size = DVec2(100, 33) * scale;
        items.back().textStyle.fontSize = 7 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        // 阶段
        items.push_back({});
        items.back().position = position + DVec2(155, 0) * scale;
        items.back().size = DVec2(25.0,12) * scale;
        items.back().text = QString::fromUtf8(WD::WDTs("ISOIndexTable", "StageXB").c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center };

        // 阶段
        items.push_back({});
        items.back().position = position + DVec2(155, 0) * scale;
        items.back().size = DVec2(25.0, 12) * scale;
        items.back().texts.emplace_back(QString::fromUtf8(WD::WDTs("ISOIndexTable", "StageXB").c_str()));
        items.back().texts.emplace_back(QString::fromUtf8(WD::WDTs("ISOIndexTable", "DesignStageXB").c_str()));
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Right, WDAlign::VAlign::VA_Center };

        //单位
        items.push_back({});
        items.back().position = position + DVec2(40, 33) * scale;
        items.back().size = DVec2(10, 7) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("ISOIndexTable", "UnitXB").c_str());
        items.back().text = QString::fromUtf8(WD::WDTs("ISOIndexTable", "UnitXB").c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        // 单位
        items.push_back({});
        items.back().position = position + DVec2(50, 33) * scale;
        items.back().size = DVec2(10, 7) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("ISOIndexTable", "UnitXB").c_str());
        items.back().text = QString::fromUtf8(WD::WDTs("ISOIndexTable", "UnitXB").c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        // 比例
        items.push_back({});
        items.back().position = position + DVec2(60, 33) * scale;
        items.back().size = DVec2(10, 7) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("ISOIndexTable", "PropertionXB").c_str());
        items.back().text = QString::fromUtf8(WD::WDTs("ISOIndexTable", "PropertionXB").c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        // 比例
        items.push_back({});
        items.back().position = position + DVec2(70, 33) * scale;
        items.back().size = DVec2(10, 7) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("ISOIndexTable", "PropertionXB").c_str());
        items.back().text = QString::fromUtf8(WD::WDTs("ISOIndexTable", "PropertionXB").c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        // 图号
        items.push_back({});
        items.back().position = position + DVec2(80, 33) * scale;
        items.back().size = DVec2(10, 7) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("ISOIndexTable", "DrawingNumberXB").c_str());
        items.back().text = QString::fromUtf8(WD::WDTs("ISOIndexTable", "DrawingNumberXB").c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        // 等轴测图纸编号
        items.push_back({});
        items.back().position = position + DVec2(90, 33) * scale;
        items.back().size = DVec2(50, 7) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("ISOIndexTable", "ISODrawingNumXb").c_str());
        items.back().text = QString::fromUtf8(branch.name().c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        // 第几张
        // 页码
        items.push_back({});
        items.back().position = position + DVec2(140, 33) * scale;
        items.back().size = DVec2(25, 7) * scale;
        {
            char text[64] = { 0 };
            sprintf_s(text, sizeof(text), WD::WDTs("ISOIndexTable", "PageNumberXB").c_str(), static_cast<int>(style.paperIndex));
            items.back().title = QString::fromUtf8(text);
            items.back().textStyle.fontSize = 3 * yScale;
        }
        {
            char text[64] = { 0 };
            sprintf_s(text, sizeof(text), WD::WDTs("ISOIndexTable", "PageSizeXB").c_str()
                , static_cast<int>(style.paperIndex), static_cast<int>(style.allPaperCnt));
            items.back().text = QString::fromUtf8(text);
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.back().textStyle.fontSize = 3 * yScale;
        }

        //items.back().textStyle.fontSize = 5 * yScale;
        //items.push_back({});
        //items.back().position = position + DVec2(95, 51) * scale;
        //items.back().size = DVec2(65, 15) * scale;
        //items.back().title = QString::fromUtf8("日期");
        //items.back().text = QDate::currentDate().toString("yyyy/MM/dd");
        //items.back().textStyle.fontSize = 5 * yScale;

        // 版本
        items.push_back({});
        items.back().position = position + DVec2(165, 33) * scale;
        items.back().size = DVec2(8, 7) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("TableStyle", "VersionB").c_str());
        items.back().text = QString::fromUtf8(WD::WDTs("TableStyle", "VersionB").c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        // 版本
        items.push_back({});
        items.back().position = position + DVec2(173, 33) * scale;
        items.back().size = DVec2(7, 7) * scale;
        items.back().title = QString::fromUtf8(WD::WDTs("TableStyle", "VersionB").c_str());
        items.back().text = QString::fromUtf8(WD::WDTs("TableStyle", "VersionB").c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

    }
    /**
    * @brief 绘制标题栏区
    */
    static void    DrawTitleBar(WDAbstractPainter2D& painter, ISOPaper& paper, const TitleBarItems& items)
    {
        auto& titleBar = paper.titleBar;
        if (items.empty() || !titleBar.bVisible)
            return;

        WDFontStyle fontStyle;
        fontStyle.fontSize = 3.5;

        // 这里外边框的起始位置取items的第一项的位置
        auto& position = items[0].position;
        if (titleBar.bVisibleGrid)
            painter.drawRect(position, DVec2(titleBar.width, titleBar.height), titleBar.outerBorderLine);

        for (auto& item : items)
        {
            if (!item.valid())
                continue;
            DVec2 titlePosS = item.position;
            auto& size = item.size;
            if (titleBar.bVisibleGrid)
                painter.drawRect(item.position, size, titleBar.innerBorderLine);
            IVec2 proportion = item.proportion;
            proportion.x = Max(proportion.x, 1);
            proportion.y = Max(proportion.y, 1);

            if (item.title.isEmpty())
            {
                DVec2 textPosS = item.position;
                DVec2 textPosE = item.position + size;
                painter.drawText(item.text.toUtf8().data(), textPosS, textPosE, item.textStyle, item.textAlign, true);
                if (item.isLogo)
                {
                    // 这里计算logo项的size(以短边为宽，做正方形)
                    auto minLeng = Min(size.x, size.y);

                    char logoPath[1024] = { 0 };
                    sprintf_s(logoPath, sizeof(logoPath), "%s/iso/logo/logo.png", WD::Core().dataDirPath());
                    WDImage image;
                    image.load(logoPath);
                    // -3 是因为图标的形状不是标准的正方形， -3让他看起来像正方形  + ((minLeng - size.x) / 2, (minLeng - size.y) / 2 + 1.5)是让图标偏移，使其在项的中心
                    painter.drawImage(item.position + DVec2((minLeng - size.x) / 2, (minLeng - size.y) / 2 + 1.5), image, DVec2(minLeng, minLeng - 3));
                }
            }
            else
            {
                auto titlePosE = titlePosS + DVec2(size.x, size.y / (proportion.x + proportion.y) * proportion.x);
                // 标题为左对齐, 这里加 2.0 是为了让标题的位置看起来更协调
                titlePosS.x += 2.0;
                painter.drawText(item.title.toUtf8().data(), titlePosS, titlePosE, fontStyle, { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center }, true);
                auto textPosS = DVec2(item.position.x, titlePosE.y);
                auto textPosE = item.position + size;
                painter.drawText(item.text.toUtf8().data(), textPosS, textPosE, item.textStyle, item.textAlign, true);
            }
        }
    }
    /**
    * @brief 绘制签署栏区
    */
    static void    DrawSignBar(WDAbstractPainter2D& painter, ISOPaper& paper, const TitleBarItems& items)
    {
        WDUnused(items);
        auto& signBar = paper.signBar;
        if (!signBar.bVisible)
        {
            assert(false);
            return;
        }

        const DVec2 position = paper.titleBar.position();
        const DVec2 size = DVec2(signBar.width, signBar.height);
        const double xScale = size.x / StandardSignBarSizeXB.x;
        const double yScale = size.y / StandardSignBarSizeXB.y;

        // 设置材料区需要绘制的列, 暂时写死要绘制的内容
        // 图纸说明
        ISOTableWidget signBarTable;
        signBarTable.bGridVisible = signBar.bVisibleGrid;
        signBarTable.setTableHeight(signBar.height * yScale);
        signBarTable.innerBorderLine = signBar.innerBorderLine;
        signBarTable.outerBorderLine = signBar.outerBorderLine;
        signBarTable.innerBorderLine.width = signBarTable.innerBorderLine.width;
        signBarTable.outerBorderLine.width = signBarTable.outerBorderLine.width;
        int rowCnt = 5;
        int colCnt = 8;
        signBarTable.setSizeMatrix(rowCnt, colCnt);
        signBarTable.setHorizontalTexts({ 
            WD::WDTs("TableStyle", "VersionB")
            , WD::WDTs("ISOIndexTable", "DateXB")
            , WD::WDTs("ISOIndexTable", "MappingXB")
            , WD::WDTs("ISOIndexTable", "DesignXB")
            , WD::WDTs("ISOIndexTable", "ProofreadXB")
            , WD::WDTs("ISOIndexTable", "ExamineXB")
            , WD::WDTs("ISOIndexTable", "ApproveXB")
            , WD::WDTs("ISOIndexTable", "NoteXB")}, 4);

        WDFontStyle font;
        font.fontSize = paper.signBar.textFont.fontSize;
        font.weight = WDFontStyle::Bold;
        signBarTable.setRowFont(font, rowCnt - 1);
        std::vector<double> vec = signBar.colWidths;
        for (auto& each : vec)
            each = each * xScale;
        signBarTable.tableDistribution.horizontal = ISOTableHorizontal::H_AllFixed;
        signBarTable.setColWidths(vec);
        signBarTable.position = signBar.position();

        //// 这里从材料表的数据中获取 版本/日期/设计/校验/审核的数据，理论上应该是从表格中获取，但因为现在暂时材料表不是真正的表格，这里暂时遍历其数据获取
        //for (auto& item : items)
        //{
        //    if (item.title == "版本")
        //        signBarTable.setText(2, 0, item.text.toUtf8().data());
        //    else if (item.title == "日期")
        //        signBarTable.setText(2, 1, item.text.toUtf8().data());
        //    else if (item.title == "设计")
        //        signBarTable.setText(2, 3, item.text.toUtf8().data());
        //    else if (item.title == "校核")
        //        signBarTable.setText(2, 4, item.text.toUtf8().data());
        //    else if (item.title == "审核")
        //        signBarTable.setText(2, 5, item.text.toUtf8().data());
        //}
        //// 这个暂时写死
        //signBarTable.setText(2, 2, "第一版设计");
        //signBarTable.setColAlign(WDAlign(), 0);

        font.fontSize = paper.signBar.textFont.fontSize * yScale;
        font.weight = WDFontStyle::Normal;

        signBarTable.update(painter, font, { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center });
        //审核中间补一条线,审核然后上面要分开两列两个人审核签名用
        painter.drawLine(DVec2(signBarTable.position.x + 105* xScale, signBarTable.position.y), DVec2(signBarTable.position.x + 105 * xScale, signBarTable.position.y + (rowCnt - 1)*7 * yScale), signBar.innerBorderLine);
    }
    static void    DrawAxisBar(WDAbstractPainter2D& painter, ISOPaper& paper, const TitleBarItems& items, DependJsonMgr* pJsonMgr)
    {
        WDUnused(items);
        if (pJsonMgr == nullptr)
        {
            return;
        }
        //这里按照A1尺寸来写，然后根据实际选择纸张大小做不同的缩放。
        auto scale = DVec2(paper.size().width() / 841.0, paper.size().height()/ 594.0);
        auto startPos = WD::DVec2( paper.paintArea().left(), paper.paintArea().bottom());
        //绘制表格边框
        painter.drawLine(startPos + DVec2(0,-26.63 * scale.y), startPos + DVec2(312.47 * scale.x,-26.63 * scale.y), paper.frame.lineStyle);
        //更新起点为字的水平线
        startPos.y = startPos.y - 10 * scale.y;
        WDFontStyle fontStyle;
        fontStyle.family = "TrueType";
        fontStyle.fontSize *= 0.2;
        auto text = std::string("注：");
        painter.drawText(text, startPos + DVec2(10.41*scale.x,0), fontStyle, { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center });
        auto textAxis = std::string("1.坐标系说明：");
        painter.drawText(textAxis, startPos + DVec2(22.39*scale.x,0), fontStyle, { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center });

        auto textAxisX = pJsonMgr->_pConfiguration->_axis._x;
        painter.drawText(textAxisX, startPos + DVec2(63.57*scale.x,0), fontStyle, { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center });
        auto textAxisY = pJsonMgr->_pConfiguration->_axis._y;
        painter.drawText(textAxisY, startPos + DVec2(111.04*scale.x, 0), fontStyle, { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center });
        auto textAxisZ = pJsonMgr->_pConfiguration->_axis._z;
        painter.drawText(textAxisZ, startPos + DVec2(162.03*scale.x, 0), fontStyle, { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center });
        auto textAxisStr = std::string("坐标系示意图：");
        painter.drawText(textAxisStr, startPos + DVec2(213.15*scale.x, 0), fontStyle, { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center });
        painter.drawText(std::string("a   零件件号"), startPos + DVec2(285.29 * scale.x, 0), fontStyle, { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center });
        //std::vector<DVec2> points;
        //points.emplace_back(startPos + DVec2(285.29 * scale.x, 0));
        //points.emplace_back(startPos + DVec2(285.29 * scale.x, -4.7*scale.y));
        //points.emplace_back(startPos + DVec2(288.89 * scale.x, -4.7 * scale.y));
        //points.emplace_back(startPos + DVec2(288.89 * scale.x, 0));
        //painter.drawPolygon(points, paper.frame.lineStyle);
        //绘制坐标系图例：
        //绘制箭头 ：
        // 线段的sPos起点ePos终点，计算方向用
        // arrowLen箭头长度
        auto lineStyle = paper.frame.lineStyle;
        lineStyle.width = 0.2;
        auto drawDirect = [&](const WD::DVec2& sPos, const WD::DVec2& ePos,const double& arrowLen,const double& angle) -> WD::DVec2
            {
                DVec2 dir = (ePos - sPos).normalized();
                //位置偏移矩阵
                WD::DMat4 offMatT = WD::DMat4::MakeTranslation(WD::DVec3(ePos));
                //位置偏移矩阵的逆
                WD::DMat4 offMatTInv = WD::DMat4::MakeTranslation(-WD::DVec3(ePos));
                WD::DMat4 leftMatR = WD::DMat4::MakeRotationZ(angle);
                auto leftMatrix = offMatT * leftMatR * offMatTInv;
                WD::DMat4 rightMatR = WD::DMat4::MakeRotationZ(-angle);
                auto rightMatrix = offMatT * rightMatR * offMatTInv;
                auto leftPos = leftMatrix * WD::DVec3(ePos - dir* arrowLen);
                auto rightPos = rightMatrix * WD::DVec3(ePos - dir * arrowLen);
                leftPos += WD::DVec3(dir * arrowLen);
                rightPos += WD::DVec3(dir * arrowLen);
                auto sP = WD::DVec3(ePos) + WD::DVec3(dir * arrowLen);
                painter.drawLines({ DVec2(sP.x,sP.y) ,DVec2(leftPos.x, leftPos.y) ,DVec2(leftPos.x, leftPos.y),DVec2(rightPos.x, rightPos.y),DVec2(rightPos.x, rightPos.y),DVec2(sP.x,sP.y) }, lineStyle);
                //求点在向量投影点就是线和三角形的交点作为线段的终点返回出去。                  
                auto lineA = WD::TLine3<double>(WD::DVec3(rightPos), (leftPos - rightPos).normalized());
                auto lineB = WD::TLine3<double>(WD::DVec3(sPos), WD::DVec3(dir));
                auto ret = WD::TLine3<double>::Intersect(lineA, lineB, 0.00001);
                if (ret)
                    sP = lineA.at(ret.value().first);
                return DVec2(sP.x, sP.y);
            };
        //图例原点
        auto orignPos = startPos + DVec2(255.28 * scale.x, ( - 4.93 + 10) * scale.y);
        //轴长
        double langth = 15.8 * scale.y;
        double langthText = 20.5 * scale.y;
        //绘制三个轴和箭头，并在箭头处写上字
        //z轴位置最好确定，xy分别绕原点左右旋转60度
        auto zPos = orignPos + DVec2(0, -langth);
        //位置偏移矩阵
        WD::DMat4 offMatT = WD::DMat4::MakeTranslation(WD::DVec3(orignPos.x, orignPos.y, 0));
        //位置偏移矩阵的逆
        WD::DMat4 offMatTInv = WD::DMat4::MakeTranslation(-WD::DVec3(orignPos.x, orignPos.y, 0));
        WD::DMat4 xMatR = WD::DMat4::MakeRotationZ(60);
        auto xMatrix = offMatT * xMatR * offMatTInv;
        WD::DMat4 yMatR = WD::DMat4::MakeRotationZ(-60);
        auto yMatrix = offMatT * yMatR * offMatTInv;
        auto xPos = xMatrix * WD::DVec3(zPos);
        auto yPos = yMatrix * WD::DVec3(zPos);
        //绘制箭头
        painter.drawLine(orignPos, drawDirect(orignPos, zPos, 2.5 * scale.y, 8), lineStyle);
        painter.drawLine(orignPos, drawDirect(orignPos, DVec2(xPos.x, xPos.y), 2.5 * scale.y, 8), lineStyle);
        painter.drawLine(orignPos, drawDirect(orignPos, DVec2(yPos.x, yPos.y), 2.5 * scale.y, 8), lineStyle);
        //绘制文字
        auto vec = (zPos - orignPos).normalized();
        painter.drawText(std::string("Z"), zPos + vec* (langthText - langth), fontStyle, {WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center});
        painter.drawText(std::string("X"), DVec2(xPos.x, xPos.y) + vec * (langthText - langth), fontStyle, {WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center});
        painter.drawText(std::string("Y"), DVec2(yPos.x, yPos.y) + vec * (langthText - langth), fontStyle, { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center });
    }
    static void    updateCharacteristicTableData(WDNode& branch, ISOPaper& paper, TitleBarItems& items,DependJsonMgr* pJsonMgr)
    {
        if (pJsonMgr == nullptr || pJsonMgr->_pPipeDataJson == nullptr)
        {
            return;
        }
        if (pJsonMgr->_pPipeDataJson->_data.empty())
        {
            return;
        }        
        auto name = QString::fromUtf8(branch.name().c_str());
        JsonPipeData data;
        for (auto& i : pJsonMgr->_pPipeDataJson->_data)
        {
            if (QString::fromUtf8(i._lineNumber.c_str()).compare(name, Qt::CaseInsensitive) == 0)
            {
                data = i;
            }
        }
        auto startPos = WD::DVec2(paper.paintArea().left(), paper.paintArea().bottom());
        //绘制表格边框
        //这里按照A1尺寸来写，然后根据实际选择纸张大小做不同的缩放。
        auto scale = DVec2(paper.size().width() / 841.0, paper.size().height() / 594.0);
        auto position = startPos + DVec2(312.47 * scale.x, -26.63 * scale.y);
        auto yScale = scale.y;

        items.push_back({});
        items.back().position = position;
        items.back().size = DVec2(9.47, 6.6) * scale;
        items.back().text = QString::fromUtf8("1");
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        items.push_back({});
        items.back().position = position + DVec2(9.47, 0) * scale;
        items.back().size = DVec2(24.82, 6.6) * scale;
        items.back().text = QString::fromUtf8(data._specification.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(34.29, 0) * scale;
        items.back().size = DVec2(25.46, 6.6) * scale;
        items.back().text = QString::fromUtf8(data._description.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(59.75, 0) * scale;
        items.back().size = DVec2(16.99, 6.6) * scale;
        items.back().text = QString::fromUtf8(data._designPressure.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(76.74, 0) * scale;
        items.back().size = DVec2(15.2, 6.6) * scale;
        items.back().text = QString::fromUtf8(data._designTemperature.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(91.94, 0) * scale;
        items.back().size = DVec2(18.78, 6.6) * scale;
        items.back().text = QString::fromUtf8("");
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(110.72, 0) * scale;
        items.back().size = DVec2(31.49, 6.6) * scale;
        items.back().text = QString::fromUtf8(data._material.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(142.21, 0) * scale;
        items.back().size = DVec2(40.63, 6.6) * scale;
        
        items.back().text = QString::fromUtf8(pJsonMgr->_pConfiguration->_table._reqWeld.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(182.84, 0) * scale;
        items.back().size = DVec2(39.24, 6.6) * scale;
        items.back().text = QString::fromUtf8(pJsonMgr->_pConfiguration->_table._reqThermal.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(222.08, 0) * scale;
        items.back().size = DVec2(12.76, 6.6) * scale;
        items.back().text = QString::fromUtf8(pJsonMgr->_pConfiguration->_table._standardsNDT.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(234.84, 0) * scale;
        items.back().size = DVec2(7.66, 6.6) * scale;
        items.back().text = QString::fromUtf8(pJsonMgr->_pConfiguration->_table._NDT.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(242.5, 0) * scale;
        items.back().size = DVec2(7.71, 6.6) * scale;
        items.back().text = QString::fromUtf8(pJsonMgr->_pConfiguration->_table._ratioNDT.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(250.21, 0) * scale;
        items.back().size = DVec2(13.16, 6.6) * scale;
        items.back().text = QString::fromUtf8(pJsonMgr->_pConfiguration->_table._levelNDT.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(263.37, 0) * scale;
        items.back().size = DVec2(11.7, 6.6) * scale;
        items.back().text = QString::fromUtf8(pJsonMgr->_pConfiguration->_table._acceptanceLevelNDT.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(275.07, 0) * scale;
        items.back().size = DVec2(19.02, 6.6) * scale;
        items.back().text = QString::fromUtf8(pJsonMgr->_pConfiguration->_table._construction.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(294.09, 0) * scale;
        items.back().size = DVec2(19.45, 6.6) * scale;
        items.back().text = QString::fromUtf8(pJsonMgr->_pConfiguration->_table._pressureClassification.c_str());
        items.back().textStyle.fontSize = 2 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        //第二排
        items.push_back({});
        items.back().position = position + DVec2(0, 6.6) * scale;
        items.back().size = DVec2(9.47, 10.0) * scale;
        items.back().text = QString::fromUtf8("序号");
        items.back().textStyle.fontSize = 4 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        items.push_back({});
        items.back().position = position + DVec2(9.47, 6.6) * scale;
        items.back().size = DVec2(24.82, 10.0) * scale;
        items.back().text = QString::fromUtf8("管道规格");
        items.back().textStyle.fontSize = 4 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(34.29, 6.6) * scale;
        items.back().size = DVec2(25.46, 10.0) * scale;
        items.back().text = QString::fromUtf8("管道名称");
        items.back().textStyle.fontSize = 4 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        //设计参数
        {
            //上栏
            items.push_back({});
            items.back().position = position + DVec2(59.75, 6.6) * scale;
            items.back().size = DVec2(16.99, 5) * scale;
            items.back().text = QString::fromUtf8("压力Pa(g)");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(76.74, 6.6) * scale;
            items.back().size = DVec2(15.2, 5) * scale;
            items.back().text = QString::fromUtf8("温度℃");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            //下栏
            items.push_back({});
            items.back().position = position + DVec2(59.75, 11.6) * scale;
            items.back().size = DVec2(32.19, 5) * scale;
            items.back().text = QString::fromUtf8("设计参数");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }

        //材料
        {
            //上栏
            items.push_back({});
            items.back().position = position + DVec2(91.94, 6.6) * scale;
            items.back().size = DVec2(18.78, 5) * scale;
            items.back().text = QString::fromUtf8("标准");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(110.72, 6.6) * scale;
            items.back().size = DVec2(31.49, 5) * scale;
            items.back().text = QString::fromUtf8("材质");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            //下栏
            items.push_back({});
            items.back().position = position + DVec2(91.94, 11.6) * scale;
            items.back().size = DVec2(50.27, 5) * scale;
            items.back().text = QString::fromUtf8("材料");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }

        items.push_back({});
        items.back().position = position + DVec2(142.21, 6.6) * scale;
        items.back().size = DVec2(40.63, 10) * scale;
        items.back().text = QString::fromUtf8("焊接要求");
        items.back().textStyle.fontSize = 4 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(182.84, 6.6) * scale;
        items.back().size = DVec2(39.24, 10) * scale;
        items.back().text = QString::fromUtf8("热处理要求");
        items.back().textStyle.fontSize = 4 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        //无损探伤
        {
            //上栏
            items.push_back({});
            items.back().position = position + DVec2(222.08, 6.6) * scale;
            items.back().size = DVec2(12.76, 5) * scale;
            items.back().text = QString::fromUtf8("标准");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(234.84, 6.6) * scale;
            items.back().size = DVec2(7.66, 5) * scale;
            items.back().text = QString::fromUtf8("方法");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(242.5, 6.6) * scale;
            items.back().size = DVec2(7.71, 5) * scale;
            items.back().text = QString::fromUtf8("比例");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(250.21, 6.6) * scale;
            items.back().size = DVec2(13.16, 5) * scale;
            items.back().text = QString::fromUtf8("检测等级");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(263.37, 6.6) * scale;
            items.back().size = DVec2(11.7, 5) * scale;
            items.back().text = QString::fromUtf8("合格等级");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

            //下栏
            items.push_back({});
            items.back().position = position + DVec2(222.08, 11.6) * scale;
            items.back().size = DVec2(52.98, 5) * scale;
            items.back().text = QString::fromUtf8("无损探伤");
            items.back().textStyle.fontSize = 2 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        items.push_back({});
        items.back().position = position + DVec2(275.07, 6.6) * scale;
        items.back().size = DVec2(19.02, 10) * scale;
        items.back().text = QString::fromUtf8("施工验收");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(294.09, 6.6) * scale;
        items.back().size = DVec2(19.45, 10) * scale;
        items.back().text = QString::fromUtf8("压力管道等级");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        //第三排
        items.push_back({});
        items.back().position = position + DVec2(0, 16.6) * scale;
        items.back().size = DVec2(313.53, 10.03) * scale;
        items.back().text = QString::fromUtf8("特性数据表");
        items.back().textStyle.fontSize = 5 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
    }
    static double  updateAngleTableData(double hOffset, const WD::TuLine* pLine, ISOPaper& paper, TitleBarItems& items, DependJsonMgr* pJsonMgr)
    {
        if (pJsonMgr == nullptr)
        {
            return hOffset;
        }
        //这里按照A1尺寸来写，然后根据实际选择纸张大小做不同的缩放。
        auto scale = DVec2(paper.size().width() / 841.0, paper.size().height() / 594.0);
        auto startPos = WD::DVec2(paper.paintArea().right(), paper.paintArea().top());
        auto position = startPos + WD::DVec2(-179.47,hOffset)* scale;
        auto yScale = scale.y;
        // 大标题
        items.push_back({});
        items.back().position = position;
        items.back().size = DVec2(179.47, 15) * scale;
        items.back().text = QString::fromUtf8("弯管角度一览表");
        items.back().textStyle.fontSize = 6 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        // 第二行
        items.push_back({});
        items.back().position = position + DVec2(0, 15) * scale;
        items.back().size = DVec2(16.34, 10) * scale;
        items.back().text = QString::fromUtf8("弯管角度编号");
        items.back().texts.emplace_back(QString::fromUtf8("弯管角"));
        items.back().texts.emplace_back(QString::fromUtf8("度编号"));
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(16.34, 15) * scale;
        items.back().size = DVec2(20.43, 10) * scale;
        items.back().text = QString::fromUtf8("弯头角度");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(36.77, 15) * scale;
        items.back().size = DVec2(18.78, 10) * scale;
        items.back().text = QString::fromUtf8("弯管角度编号");
        items.back().texts.emplace_back(QString::fromUtf8("弯管角"));
        items.back().texts.emplace_back(QString::fromUtf8("度编号"));
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(55.55, 15) * scale;
        items.back().size = DVec2(19.58, 10) * scale;
        items.back().text = QString::fromUtf8("弯头角度");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(75.13, 15) * scale;
        items.back().size = DVec2(17.16, 10) * scale;
        items.back().text = QString::fromUtf8("弯管角度编号");
        items.back().texts.emplace_back(QString::fromUtf8("弯管角"));
        items.back().texts.emplace_back(QString::fromUtf8("度编号"));
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(92.29, 15) * scale;
        items.back().size = DVec2(20.5, 10) * scale;
        items.back().text = QString::fromUtf8("弯头角度");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(112.79, 15) * scale;
        items.back().size = DVec2(18.31, 10) * scale;
        items.back().text = QString::fromUtf8("弯管角度编号");
        items.back().texts.emplace_back(QString::fromUtf8("弯管角"));
        items.back().texts.emplace_back(QString::fromUtf8("度编号"));
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(131.1, 15) * scale;
        items.back().size = DVec2(18.66, 10) * scale;
        items.back().text = QString::fromUtf8("弯头角度");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(149.76, 15) * scale;
        items.back().size = DVec2(14.4, 10) * scale;
        items.back().text = QString::fromUtf8("弯管角度编号");
        items.back().texts.emplace_back(QString::fromUtf8("弯管角"));
        items.back().texts.emplace_back(QString::fromUtf8("度编号"));
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(164.16, 15) * scale;
        items.back().size = DVec2(15.3, 10) * scale;
        items.back().text = QString::fromUtf8("弯头角度");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        //统计所有弯头
        //直接用vector，下标+1就是弯头编号比如s1 s2这样子
        std::vector<double> angleList;
        for (auto& i : pLine->nodes())
        {
            if (!i->isTypedComNode("ELBO"))
            {
                continue;
            }
            auto ang = WD::DVec3::Angle(i->sPtA().direction, i->sPtL().direction);
            //限制弯度在90度以内
            ang = abs(ang);
            if (ang > 90 )
            {
                ang = abs(180 - ang);
            }
            angleList.emplace_back(ang);
        }
#if 0
        //后面再考虑一次性遍历添加吧，先手动添加，因为他每个格子的规格都不一样，实际上应该一样才合理
        //for (auto& i : angleList)
        //{

        //}
#else
        char info[1024] = { 0 };
        //下面有个注意的点，就是出现每一行就得把每一行给画完线框，没有那么多弯头就内容空着。
        bool bContinue = false; 
        // 第三行
        if (angleList.size() >= 1)
        {
            bContinue = true;
            items.push_back({});
            items.back().position = position + DVec2(0, 25) * scale;
            items.back().size = DVec2(16.34, 7.45) * scale;
            items.back().text = QString::fromUtf8("a1");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(16.34, 25) * scale;
            items.back().size = DVec2(20.43, 7.45) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[0], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }

        if (angleList.size() >= 2)
        {
            items.push_back({});
            items.back().position = position + DVec2(36.77, 25) * scale;
            items.back().size = DVec2(18.78, 7.45) * scale;
            items.back().text = QString::fromUtf8("a2");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(55.55, 25) * scale;
            items.back().size = DVec2(19.58, 7.45) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[1], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(36.77, 25) * scale;
            items.back().size = DVec2(18.78, 7.45) * scale;
            items.push_back({});
            items.back().position = position + DVec2(55.55, 25) * scale;
            items.back().size = DVec2(19.58, 7.45) * scale;
        }

        if (angleList.size() >= 3)
        {
            items.push_back({});
            items.back().position = position + DVec2(75.13, 25) * scale;
            items.back().size = DVec2(17.16, 7.45) * scale;
            items.back().text = QString::fromUtf8("a3");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(92.29, 25) * scale;
            items.back().size = DVec2(20.5, 7.45) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[2], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(75.13, 25) * scale;
            items.back().size = DVec2(17.16, 7.45) * scale;
            items.push_back({});
            items.back().position = position + DVec2(92.29, 25) * scale;
            items.back().size = DVec2(20.5, 7.45) * scale;
        }
        if (angleList.size() >= 4)
        {
            items.push_back({});
            items.back().position = position + DVec2(112.79, 25) * scale;
            items.back().size = DVec2(18.31, 7.45) * scale;
            items.back().text = QString::fromUtf8("a4");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(131.1, 25) * scale;
            items.back().size = DVec2(18.66, 7.45) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[3], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(112.79, 25) * scale;
            items.back().size = DVec2(18.31, 7.45) * scale;
            items.push_back({});
            items.back().position = position + DVec2(131.1, 25) * scale;
            items.back().size = DVec2(18.66, 7.45) * scale;
        }
        if (angleList.size() >= 5)
        {
            items.push_back({});
            items.back().position = position + DVec2(149.76, 25) * scale;
            items.back().size = DVec2(14.4, 7.45) * scale;
            items.back().text = QString::fromUtf8("a5");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(164.16, 25) * scale;
            items.back().size = DVec2(15.3, 7.45) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[4], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(149.76, 25) * scale;
            items.back().size = DVec2(14.4, 7.45) * scale;
            items.push_back({});
            items.back().position = position + DVec2(164.16, 25) * scale;
            items.back().size = DVec2(15.3, 7.45) * scale;
        }
        bContinue = false;
        //第四行
        if (angleList.size() >= 6)
        {
            bContinue = true;
            items.push_back({});
            items.back().position = position + DVec2(0, 32.45) * scale;
            items.back().size = DVec2(16.34, 6.85) * scale;
            items.back().text = QString::fromUtf8("a6");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(16.34, 32.45) * scale;
            items.back().size = DVec2(20.43, 6.85) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[5], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }

        if (angleList.size() >= 7)
        {
            items.push_back({});
            items.back().position = position + DVec2(36.77, 32.45) * scale;
            items.back().size = DVec2(18.78, 6.85) * scale;
            items.back().text = QString::fromUtf8("a7");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(55.55, 32.45) * scale;
            items.back().size = DVec2(19.58, 6.85) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[6], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(36.77, 32.45) * scale;
            items.back().size = DVec2(18.78, 6.85) * scale;
            items.push_back({});
            items.back().position = position + DVec2(55.55, 32.45) * scale;
            items.back().size = DVec2(19.58, 6.85) * scale;
        }
        if (angleList.size() >= 8)
        {
            items.push_back({});
            items.back().position = position + DVec2(75.13, 32.45) * scale;
            items.back().size = DVec2(17.16, 6.85) * scale;
            items.back().text = QString::fromUtf8("a8");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(92.29, 32.45) * scale;
            items.back().size = DVec2(20.5, 6.85) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[7], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(75.13, 32.45) * scale;
            items.back().size = DVec2(17.16, 6.85) * scale;
            items.push_back({});
            items.back().position = position + DVec2(92.29, 32.45) * scale;
            items.back().size = DVec2(20.5, 6.85) * scale;
        }
        if (angleList.size() >= 9)
        {
            items.push_back({});
            items.back().position = position + DVec2(112.79, 32.45) * scale;
            items.back().size = DVec2(18.31, 6.85) * scale;
            items.back().text = QString::fromUtf8("a9");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(131.1, 32.45) * scale;
            items.back().size = DVec2(18.66, 6.85) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[8], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(112.79, 32.45) * scale;
            items.back().size = DVec2(18.31, 6.85) * scale;
            items.push_back({});
            items.back().position = position + DVec2(131.1, 32.45) * scale;
            items.back().size = DVec2(18.66, 6.85) * scale;
        }
        if (angleList.size() >= 10)
        {
            items.push_back({});
            items.back().position = position + DVec2(149.76, 32.45) * scale;
            items.back().size = DVec2(14.4, 6.85) * scale;
            items.back().text = QString::fromUtf8("a10");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(164.16, 32.45) * scale;
            items.back().size = DVec2(15.3, 6.85) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[9], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(149.76, 32.45) * scale;
            items.back().size = DVec2(14.4, 6.85) * scale;
            items.push_back({});
            items.back().position = position + DVec2(164.16, 32.45) * scale;
            items.back().size = DVec2(15.3, 6.85) * scale;
        }
        bContinue = false;
        //第五行
        if (angleList.size() >= 11)
        {
            bContinue = true;
            items.push_back({});
            items.back().position = position + DVec2(0, 39.3) * scale;
            items.back().size = DVec2(16.34, 7.45) * scale;
            items.back().text = QString::fromUtf8("a1");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(16.34, 39.3) * scale;
            items.back().size = DVec2(20.43, 7.45) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[10], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }

        if (angleList.size() >= 12)
        {
            items.push_back({});
            items.back().position = position + DVec2(36.77, 39.3) * scale;
            items.back().size = DVec2(18.78, 7.45) * scale;
            items.back().text = QString::fromUtf8("a2");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(55.55, 39.3) * scale;
            items.back().size = DVec2(19.58, 7.45) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[11], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(36.77, 39.3) * scale;
            items.back().size = DVec2(18.78, 7.45) * scale;
            items.push_back({});
            items.back().position = position + DVec2(55.55, 39.3) * scale;
            items.back().size = DVec2(19.58, 7.45) * scale;
        }
        if (angleList.size() >= 13)
        {
            items.push_back({});
            items.back().position = position + DVec2(75.13, 39.3) * scale;
            items.back().size = DVec2(17.16, 7.45) * scale;
            items.back().text = QString::fromUtf8("a3");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(92.29, 39.3) * scale;
            items.back().size = DVec2(20.5, 7.45) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[12], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(75.13, 39.3) * scale;
            items.back().size = DVec2(17.16, 7.45) * scale;
            items.push_back({});
            items.back().position = position + DVec2(92.29, 39.3) * scale;
            items.back().size = DVec2(20.5, 7.45) * scale;
        }
        if (angleList.size() >= 14)
        {
            items.push_back({});
            items.back().position = position + DVec2(112.79, 39.3) * scale;
            items.back().size = DVec2(18.31, 7.45) * scale;
            items.back().text = QString::fromUtf8("a4");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(131.1, 39.3) * scale;
            items.back().size = DVec2(18.66, 7.45) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[13], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(112.79, 39.3) * scale;
            items.back().size = DVec2(18.31, 7.45) * scale;
            items.push_back({});
            items.back().position = position + DVec2(131.1, 39.3) * scale;
            items.back().size = DVec2(18.66, 7.45) * scale;
        }
        if (angleList.size() >= 15)
        {
            items.push_back({});
            items.back().position = position + DVec2(149.76, 39.3) * scale;
            items.back().size = DVec2(14.4, 7.45) * scale;
            items.back().text = QString::fromUtf8("a5");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(164.16, 39.3) * scale;
            items.back().size = DVec2(15.3, 7.45) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[14], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(149.76, 39.3) * scale;
            items.back().size = DVec2(14.4, 7.45) * scale;
            items.push_back({});
            items.back().position = position + DVec2(164.16, 39.3) * scale;
            items.back().size = DVec2(15.3, 7.45) * scale;
        }
        bContinue = false;
        //第六行
        if (angleList.size() >= 16)
        {
            bContinue = true;
            items.push_back({});
            items.back().position = position + DVec2(0, 46.75) * scale;
            items.back().size = DVec2(16.34, 6.85) * scale;
            items.back().text = QString::fromUtf8("a6");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(16.34, 46.75) * scale;
            items.back().size = DVec2(20.43, 6.85) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[15], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        if (angleList.size() >= 17)
        {
            items.push_back({});
            items.back().position = position + DVec2(36.77, 46.75) * scale;
            items.back().size = DVec2(18.78, 6.85) * scale;
            items.back().text = QString::fromUtf8("a7");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(55.55, 46.75) * scale;
            items.back().size = DVec2(19.58, 6.85) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[16], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(36.77, 46.75) * scale;
            items.back().size = DVec2(18.78, 6.85) * scale;
            items.push_back({});
            items.back().position = position + DVec2(55.55, 46.75) * scale;
            items.back().size = DVec2(19.58, 6.85) * scale;
        }
        if (angleList.size() >= 18)
        {
            items.push_back({});
            items.back().position = position + DVec2(75.13, 46.75) * scale;
            items.back().size = DVec2(17.16, 6.85) * scale;
            items.back().text = QString::fromUtf8("a8");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(92.29, 46.75) * scale;
            items.back().size = DVec2(20.5, 6.85) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[17], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(75.13, 46.75) * scale;
            items.back().size = DVec2(17.16, 6.85) * scale;
            items.push_back({});
            items.back().position = position + DVec2(92.29, 46.75) * scale;
            items.back().size = DVec2(20.5, 6.85) * scale;
        }
        if (angleList.size() >= 19)
        {
            items.push_back({});
            items.back().position = position + DVec2(112.79, 46.75) * scale;
            items.back().size = DVec2(18.31, 6.85) * scale;
            items.back().text = QString::fromUtf8("a9");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(131.1, 46.75) * scale;
            items.back().size = DVec2(18.66, 6.85) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[18], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(112.79, 46.75) * scale;
            items.back().size = DVec2(18.31, 6.85) * scale;
            items.push_back({});
            items.back().position = position + DVec2(131.1, 46.75) * scale;
            items.back().size = DVec2(18.66, 6.85) * scale;
        }
        if (angleList.size() >= 20)
        {
            items.push_back({});
            items.back().position = position + DVec2(149.76, 46.75) * scale;
            items.back().size = DVec2(14.4, 6.85) * scale;
            items.back().text = QString::fromUtf8("a10");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(164.16, 46.75) * scale;
            items.back().size = DVec2(15.3, 6.85) * scale;
            sprintf_s(info, sizeof(info), "%.2f%s", angleList[19], WD::WDTs("ISOTiLine", "Du").c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        else if (bContinue)
        {
            items.push_back({});
            items.back().position = position + DVec2(149.76, 46.75) * scale;
            items.back().size = DVec2(14.4, 6.85) * scale;
            items.push_back({});
            items.back().position = position + DVec2(164.16, 46.75) * scale;
            items.back().size = DVec2(15.3, 6.85) * scale;
        }
#endif
        
        return items.back().position.y + items.back().size.y - startPos.y;
    }
    //更新最大应力表
    static double  updateMaxStressData(double hOffset, const WD::TuLine* pLine, ISOPaper& paper, TitleBarItems& items, DependJsonMgr* pJsonMgr)
    {
        if (pJsonMgr == nullptr 
            || pJsonMgr->_pMaxStressTable == nullptr
            || pJsonMgr->_pNodeCoordinateJson == nullptr
            || pJsonMgr->_pNodeCoordinateJson->nodesData_.empty())
        {
            return hOffset;
        }
        if (pLine == nullptr)
        {
            return hOffset;
        }
        //验算点编号先写死，没有数据对应pdms的节点编号
       //sus一行
        auto susItor = pJsonMgr->_pMaxStressTable->matDataMap_.find("SUS");
        auto expItor = pJsonMgr->_pMaxStressTable->matDataMap_.find("EXP");
        auto occItor = pJsonMgr->_pMaxStressTable->matDataMap_.find("OCC");
        StressData susData;
        StressData expData;
        StressData occData;
        //取最大计算值的一组
        if (susItor != pJsonMgr->_pMaxStressTable->matDataMap_.end())
        {
            for (auto& i : susItor->second)
            {
                if (i._cal > susData._cal)
                {
                    susData = i;
                }
            }
        }
        if (expItor != pJsonMgr->_pMaxStressTable->matDataMap_.end())
        {
            for (auto& i : expItor->second)
            {
                if (i._cal > expData._cal)
                {
                    expData = i;
                }
            }
        }
        if (occItor != pJsonMgr->_pMaxStressTable->matDataMap_.end())
        {
            for (auto& i : occItor->second)
            {
                if (i._cal > occData._cal)
                {
                    occData = i;
                }
            }
        }

        //map<节点表中的节点id，节点>
        std::map<double, TuNode*> elboMap;
        int num = 1;
        for (auto& i : pLine->nodes())
        {
            if (!i->isTypedComNode("ELBO"))
            {
                continue;
            }
            i->_elboNumber = num;
            double disOld = std::numeric_limits<double>::max();
            for (auto& data : pJsonMgr->_pNodeCoordinateJson->nodesData_)
            {
                WD::DVec3 tempPos = WD::DVec3(QString(data._x.c_str()).toDouble(), QString(data._y.c_str()).toDouble(), QString(data._z.c_str()).toDouble());
                auto dis = WD::DVec3::DistanceSq(tempPos,i->sPtL().position);

                if (dis < disOld)
                {
                    i->pidNumber = QString(data._nodeId.c_str()).toDouble();
                    disOld = dis;
                }
            }
            if (abs(susData._number - i->pidNumber) < 1.0)
            {
                i->_maxStressNumber = 1;
            } 
            if (abs(expData._number - i->pidNumber) < 1.0)
            {
                i->_maxStressNumber = 2;
            }
            if (abs(occData._number - i->pidNumber) < 1.0)
            {
                i->_maxStressNumber = 3;
            }
            num++;
        }
       

        //这里按照A1尺寸来写，然后根据实际选择纸张大小做不同的缩放。
        auto scale = DVec2(paper.size().width() / 841.0, paper.size().height() / 594.0);
        auto startPos = WD::DVec2(paper.paintArea().right(), paper.paintArea().top());
        auto position = startPos + WD::DVec2(-179.47, hOffset) * scale;
        auto yScale = scale.y;

        items.push_back({});
        items.back().position = position;
        items.back().size = DVec2(179.47, 9.89) * scale;
        items.back().text = QString::fromUtf8("最大应力表(KPa)");
        items.back().textStyle.fontSize = 6 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        items.push_back({});
        items.back().position = position + DVec2(0,9.89) * scale;
        items.back().size = DVec2(31.96, 14.49) * scale;
        items.back().text = QString::fromUtf8("验算点编号");
        items.back().textStyle.fontSize = 6 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(31.96, 9.89) * scale;
        items.back().size = DVec2(36.03, 7.49) * scale;
        items.back().text = QString::fromUtf8("持续(SUS)工况");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        {
            items.push_back({});
            items.back().position = position + DVec2(31.96, 17.38) * scale;
            items.back().size = DVec2(18.47, 7) * scale;
            items.back().text = QString::fromUtf8("计算");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(50.43, 17.38) * scale;
            items.back().size = DVec2(17.56, 7) * scale;
            items.back().text = QString::fromUtf8("许用");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        items.push_back({});
        items.back().position = position + DVec2(67.99, 9.89) * scale;
        items.back().size = DVec2(34.05, 7.49) * scale;
        items.back().text = QString::fromUtf8("热膨胀(EXP)工况");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        {
            items.push_back({});
            items.back().position = position + DVec2(67.99, 17.38) * scale;
            items.back().size = DVec2(16.79, 7) * scale;
            items.back().text = QString::fromUtf8("计算");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(84.78, 17.38) * scale;
            items.back().size = DVec2(17.26, 7) * scale;
            items.back().text = QString::fromUtf8("许用");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        items.push_back({});
        items.back().position = position + DVec2(102.04, 9.89) * scale;
        items.back().size = DVec2(37.44, 7.49) * scale;
        items.back().text = QString::fromUtf8("偶然(OCC)工况");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        {
            items.push_back({});
            items.back().position = position + DVec2(102.04, 17.38) * scale;
            items.back().size = DVec2(19.25, 7) * scale;
            items.back().text = QString::fromUtf8("计算");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(121.29, 17.38) * scale;
            items.back().size = DVec2(18.19, 7) * scale;
            items.back().text = QString::fromUtf8("许用");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        items.push_back({});
        items.back().position = position + DVec2(139.48, 9.89) * scale;
        items.back().size = DVec2(21.58, 7.49) * scale;
        items.back().text = QString::fromUtf8("百分比(%)");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        {
            items.push_back({});
            items.back().position = position + DVec2(139.48, 17.38) * scale;
            items.back().size = DVec2(21.58, 7) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        items.push_back({});
        items.back().position = position + DVec2(161.06, 9.89) * scale;
        items.back().size = DVec2(18.42, 14.49) * scale;
        items.back().text = QString::fromUtf8("结论");
        items.back().textStyle.fontSize = 6 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        char info[1024] = { 0 }; 
        items.push_back({});
        items.back().position = position + DVec2(0, 24.38) * scale;
        items.back().size = DVec2(31.96, 6) * scale;
        items.back().text = QString::fromUtf8("S1(弯头出口)");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        {
            items.push_back({});
            items.back().position = position + DVec2(31.96, 24.38) * scale;
            items.back().size = DVec2(18.47, 6) * scale;
            sprintf_s(info, sizeof(info), "%.1f", susData._cal);
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(50.43, 24.38) * scale;
            items.back().size = DVec2(17.56, 6) * scale;
            sprintf_s(info, sizeof(info), "%.1f", susData._allowable);
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(67.99, 24.38) * scale;
            items.back().size = DVec2(16.79, 6) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(84.78, 24.38) * scale;
            items.back().size = DVec2(17.26, 6) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(102.04, 24.38) * scale;
            items.back().size = DVec2(19.25, 6) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(121.29, 24.38) * scale;
            items.back().size = DVec2(18.19, 6) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(139.48, 24.38) * scale;
            sprintf_s(info, sizeof(info), "%.1f", susData._percents);
            items.back().text = QString::fromUtf8(info);
            items.back().size = DVec2(21.58, 6) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(161.06, 24.38) * scale;
            sprintf_s(info, sizeof(info), "%s", susData.result.c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().size = DVec2(18.42, 6) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }

        //验算点编号先写死，没有数据对应pdms的节点编号
        //exp一行
        items.push_back({});
        items.back().position = position + DVec2(0, 30.38) * scale;
        items.back().size = DVec2(31.96, 5.35) * scale;
        items.back().text = QString::fromUtf8("S2(弯头出口)");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        {
            items.push_back({});
            items.back().position = position + DVec2(31.96, 30.38) * scale;
            items.back().size = DVec2(18.47, 5.35) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(50.43, 30.38) * scale;
            items.back().size = DVec2(17.56, 5.35) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(67.99, 30.38) * scale;
            items.back().size = DVec2(16.79, 5.35) * scale;
            sprintf_s(info, sizeof(info), "%.1f", expData._cal);
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(84.78, 30.38) * scale;
            items.back().size = DVec2(17.26, 5.35) * scale;
            sprintf_s(info, sizeof(info), "%.1f", expData._allowable);
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(102.04, 30.38) * scale;
            items.back().size = DVec2(19.25, 5.35) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(121.29, 30.38) * scale;
            items.back().size = DVec2(18.19, 5.35) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(139.48, 30.38) * scale;
            items.back().size = DVec2(21.58, 5.35) * scale;
            sprintf_s(info, sizeof(info), "%.1f", expData._percents);
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(161.06, 30.38) * scale;
            items.back().size = DVec2(18.42, 5.35) * scale;
            sprintf_s(info, sizeof(info), "%s", expData.result.c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }


        //验算点编号先写死，没有数据对应pdms的节点编号
        //occ一行
        items.push_back({});
        items.back().position = position + DVec2(0, 35.73) * scale;
        items.back().size = DVec2(31.96, 6) * scale;
        items.back().text = QString::fromUtf8("S3(弯头出口)");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        {
            items.push_back({});
            items.back().position = position + DVec2(31.96, 35.73) * scale;
            items.back().size = DVec2(18.47, 6) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(50.43, 35.73) * scale;
            items.back().size = DVec2(17.56, 6) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(67.99, 35.73) * scale;
            items.back().size = DVec2(16.79, 6) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(84.78, 35.73) * scale;
            items.back().size = DVec2(17.26, 6) * scale;
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(102.04, 35.73) * scale;
            items.back().size = DVec2(19.25, 6) * scale;
            sprintf_s(info, sizeof(info), "%.1f", expData._cal);
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(121.29, 35.73) * scale;
            items.back().size = DVec2(18.19, 6) * scale;
            sprintf_s(info, sizeof(info), "%.1f", expData._allowable);
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(139.48, 35.73) * scale;
            items.back().size = DVec2(21.58, 6) * scale;
            sprintf_s(info, sizeof(info), "%.1f", expData._percents);
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(161.06, 35.73) * scale;
            items.back().size = DVec2(18.42, 6) * scale;
            sprintf_s(info, sizeof(info), "%s", expData.result.c_str());
            items.back().text = QString::fromUtf8(info);
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }

        return items.back().position.y + items.back().size.y - startPos.y;
    }
    //更新管材参数表
    static double  updateTubeParametersData(double hOffset, WDNode& branch, ISOPaper& paper, TitleBarItems& items, DependJsonMgr* pJsonMgr)
    {
        if (pJsonMgr == nullptr
            || pJsonMgr->_pMatDataTable == nullptr
            || pJsonMgr->_pPipeDataJson == nullptr)
        {
            return 0.0;
        }
        //这里按照A1尺寸来写，然后根据实际选择纸张大小做不同的缩放。
        auto scale = DVec2(paper.size().width() / 841.0, paper.size().height() / 594.0);
        auto startPos = WD::DVec2(paper.paintArea().right(), paper.paintArea().top());
        auto position = startPos + WD::DVec2(-179.47, hOffset) * scale;
        auto yScale = scale.y;

        items.push_back({});
        items.back().position = position;
        items.back().size = DVec2(179.47, 12.89) * scale;
        items.back().text = QString::fromUtf8("管材参数表");
        items.back().textStyle.fontSize = 6 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        //第一行
        items.push_back({});
        items.back().position = position + DVec2(0, 12.89) * scale;
        items.back().size = DVec2(9.47, 10) * scale;
        items.back().text = QString::fromUtf8("编号");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(9.47, 12.89) * scale;
        items.back().size = DVec2(20.4, 10) * scale;
        items.back().text = QString::fromUtf8("管子规格");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(29.87, 12.89) * scale;
        items.back().size = DVec2(40.32, 10) * scale;
        items.back().text = QString::fromUtf8("材料");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(70.19, 12.89) * scale;
        items.back().size = DVec2(33.74, 5) * scale;
        items.back().text = QString::fromUtf8("设计参数");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        {
            items.push_back({});
            items.back().position = position + DVec2(70.19, 17.89) * scale;
            items.back().size = DVec2(17.24, 5) * scale;
            items.back().text = QString::fromUtf8("压力MPa(g)");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(87.43, 17.89) * scale;
            items.back().size = DVec2(16.5, 5) * scale;
            items.back().text = QString::fromUtf8("温度℃");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        items.push_back({});
        items.back().position = position + DVec2(103.93, 12.89) * scale;
        items.back().size = DVec2(20, 10) * scale;
        items.back().text = QString::fromUtf8("KN/mm");
        items.back().texts.emplace_back(QString::fromUtf8("KN/mm"));
        items.back().texts.emplace_back(QString::fromUtf8("E"));
        items.back().textStyle.fontSize = 4 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(103.93, 12.89) * scale;
        items.back().size = DVec2(20, 10) * scale;
        items.back().text = QString::fromUtf8("    2");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Top };
        items.push_back({});
        items.back().position = position + DVec2(103.93, 12.89) * scale;
        items.back().size = DVec2(20, 10) * scale;
        items.back().text = QString::fromUtf8("t   ");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(123.93, 12.89) * scale;
        items.back().size = DVec2(20, 10) * scale;
        items.back().text = QString::fromUtf8("10 cm/m") + QString::fromUtf8("℃");
        items.back().texts.emplace_back(QString::fromUtf8("10 cm/m") + QString::fromUtf8("℃"));
        items.back().texts.emplace_back(QString::fromUtf8("a (工作)"));
        items.back().textStyle.fontSize = 4 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Right, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(123.93, 12.89) * scale;
        items.back().size = DVec2(20, 10) * scale;
        items.back().text = QString::fromUtf8("   t");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(123.93, 12.89) * scale;
        items.back().size = DVec2(20, 10) * scale;
        items.back().text = QString::fromUtf8("   -4");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Top };
        items.push_back({});
        items.back().position = position + DVec2(143.93, 12.89) * scale;
        items.back().size = DVec2(17.19, 10) * scale;
        items.back().text = QString::fromUtf8("MPa");
        items.back().texts.emplace_back(QString::fromUtf8("  MPa"));
        items.back().texts.emplace_back(QString::fromUtf8("[σ]") + "20");
        items.back().textStyle.fontSize = 4 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(161.12, 12.89) * scale;
        items.back().size = DVec2(18.35, 10) * scale;
        items.back().text = QString::fromUtf8("MPa");
        items.back().texts.emplace_back(QString::fromUtf8("  MPa"));
        items.back().texts.emplace_back(QString::fromUtf8("[σ]"));
        items.back().textStyle.fontSize = 4 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        
        //第二行
        char info[1024] = { 0 };
        auto name = QString::fromUtf8(branch.name().c_str());
        //name = "03MAP22WP001";
        JsonPipeData data;
        for (auto& i : pJsonMgr->_pPipeDataJson->_data)
        {
            if (QString::fromUtf8(i._lineNumber.c_str()).compare(name, Qt::CaseInsensitive) == 0)
            {
                data = i;
            }
        }
        //设计温度对应的数据
        MatDataTableItemDetail designDetail;
        //工作温度对应的数据
        MatDataTableItemDetail workDetail;
        //20度温度对应的数据
        MatDataTableItemDetail twentyDetail;
        for (auto& i : pJsonMgr->_pMatDataTable->matDataMap_)
        {
            if (i._name.compare(QString::fromUtf8(data._material.c_str()), Qt::CaseInsensitive) != 0)
            {
                continue;
            }
            for (auto& dat : i._details)
            {
                if (abs(dat._temperature - QString::fromUtf8(data._designTemperature.c_str()).toFloat()) < 0.1)
                {
                    designDetail = dat;
                }
                if ((abs(dat._temperature - QString::fromUtf8(data._oPE_Temperature.c_str()).toFloat()) < 0.1))
                {
                    workDetail = dat;
                }
                if ((abs(dat._temperature - 20) < 0.1))
                {
                    twentyDetail = dat;
                }
            }
        }

        items.push_back({});
        items.back().position = position + DVec2(0, 22.89) * scale;
        items.back().size = DVec2(9.47, 7.08) * scale;
        items.back().text = QString::fromUtf8("1");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(9.47, 22.89) * scale;
        items.back().size = DVec2(20.4, 7.08) * scale;
        items.back().text = QString::fromUtf8(data._specification.c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(29.87, 22.89) * scale;
        items.back().size = DVec2(40.32, 7.08) * scale;
        items.back().text = QString::fromUtf8(data._material.c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(70.19, 22.89) * scale;
        items.back().size = DVec2(17.24, 7.08) * scale;
        items.back().text = QString::fromUtf8(data._designPressure.c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(87.43, 22.89) * scale;
        items.back().size = DVec2(16.5, 7.08) * scale;
        items.back().text = QString::fromUtf8(data._designTemperature.c_str());
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(103.93, 22.89) * scale;
        items.back().size = DVec2(20, 7.08) * scale;
        sprintf_s(info, sizeof(info), "%.1f", designDetail._modulus);
        items.back().text = QString::fromUtf8(info);
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(123.93, 22.89) * scale;
        items.back().size = DVec2(20, 7.08) * scale;
        sprintf_s(info, sizeof(info), "%.1f", workDetail._expCoeff);
        items.back().text = QString::fromUtf8(info);
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(143.93, 22.89) * scale;
        items.back().size = DVec2(17.19, 7.08) * scale;
        sprintf_s(info, sizeof(info), "%.1f", twentyDetail._allowable);
        items.back().text = QString::fromUtf8(info);
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(161.12, 22.89) * scale;
        items.back().size = DVec2(18.35, 7.08) * scale;
        sprintf_s(info, sizeof(info), "%.1f", designDetail._allowable);
        items.back().text = QString::fromUtf8(info);
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        return (items.back().position.y + items.back().size.y - startPos.y);
    }
    //更新文字说明表格，只是没有显示边框
    static double  updateNoteTableData(double hOffset, ISOPaper& paper, TitleBarItems& items, DependJsonMgr* pJsonMgr)
    {
        if (pJsonMgr == nullptr
            || pJsonMgr->_pNoteExcel == nullptr
            || pJsonMgr->_pNoteExcel->_data.empty())
        {
            return hOffset;
        }
        auto startPos = WD::DVec2(paper.paintArea().right(), paper.paintArea().bottom());
        //绘制表格边框
        //这里按照A1尺寸来写，然后根据实际选择纸张大小做不同的缩放。
        auto scale = DVec2(paper.size().width() / 841.0, paper.size().height() / 594.0);
        auto position = startPos + DVec2(0, -hOffset * scale.y);
        //单独一行的高度
        double height = 6.5 * scale.y;
        //起点y等于起点y减去总高度
        //符号画在第八行，占三行高度，所以size()+3
        position = position - DVec2(230 * scale.x, height * (pJsonMgr->_pNoteExcel->_data.size() + 3));
        auto yScale = scale.y;

        items.push_back({});
        items.back().position = position - DVec2(0, height);
        items.back().size = DVec2(230, 6.5) * scale;
        items.back().text = QString::fromUtf8("说明：");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center };
        int num = 0;
        for (auto& i : pJsonMgr->_pNoteExcel->_data)
        {
            if (num == 7)
            {
                items.push_back({});
                items.back().position = position + DVec2(0, height* num);
                items.back().size = DVec2(36.45, 14.2) * scale;
                //items.back().text = QString::fromUtf8(i.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Right, WDAlign::VAlign::VA_Bottom };
                num += 3;
            }
            items.push_back({});
            items.back().position = position + DVec2(0, height* num);
            items.back().size = DVec2(230, 6.5) * scale;
            items.back().text = QString::fromUtf8(i.c_str());
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center };
            num++;
        }
        return position.y;
    }
    //更新端点推力表
    static double  updateEndThrustData(const WD::TuLine* pLine, ISOPaper& paper, TitleBarItems& items, DependJsonMgr* pJsonMgr)
    {
        if (pJsonMgr == nullptr
            || pJsonMgr->_pThrustAndMement->_tamDataMap.empty())
        {
            return 97.0;
        }
        const auto startPos = WD::DVec2(paper.paintArea().right(), paper.paintArea().bottom());
        //绘制表格边框
        //这里按照A1尺寸来写，然后根据实际选择纸张大小做不同的缩放。
        auto scale = DVec2(paper.size().width() / 841.0, paper.size().height() / 594.0);
        auto position = startPos + DVec2(-180 * scale.x, -97 * scale.y);
        auto yScale = scale.y;
        //扫一下头尾设备数量，每有一个设备就加一行设备数据，就多13.23毫米高度
        std::unordered_map<int, std::vector<TAMData>> dataMap;
        std::unordered_map<int, WDNode*> nodeMap; 
        for (auto& i : pLine->nodes())
        {
            if (!i->isBranchHPos() && !i->isBranchTPos())
            {
                continue;
            }
            auto node = i->getRefNode();
            if (node == nullptr)
            {
                continue;
            }
            int number = -1;
            double disOld = std::numeric_limits<double>::max();
            //设备出入口点
            {//入口匹配
                for (auto& data : pJsonMgr->_pNodeCoordinateJson->nodesData_)
                {
                    WD::DVec3 tempPos = WD::DVec3(QString(data._x.c_str()).toDouble(), QString(data._y.c_str()).toDouble(), QString(data._z.c_str()).toDouble());
                    auto dis = WD::DVec3::Distance(tempPos, i->sPtL().position);
                    auto dd = i->sPtL().position;
                    if (dis < disOld)
                    {
                        number = QString(data._nodeId.c_str()).toInt();
                        disOld = dis;
                        i->_applyNumberN = number;
                    }
                }
                if (number == -1)
                {
                    continue;
                }
                auto iter = pJsonMgr->_pThrustAndMement->_tamDataMap.find(number);
                if (iter == pJsonMgr->_pThrustAndMement->_tamDataMap.end())
                {
                    continue;
                }
                dataMap[number] = iter->second;
                WDNode* tempNode = node;
                while (tempNode != nullptr &&!tempNode->isType("EQUI"))
                {
                    tempNode = tempNode->parent().get();
                }
                nodeMap[number] = tempNode;
            }
            {
                //出口匹配
                for (auto& data : pJsonMgr->_pNodeCoordinateJson->nodesData_)
                {
                    WD::DVec3 tempPos = WD::DVec3(QString(data._x.c_str()).toDouble(), QString(data._y.c_str()).toDouble(), QString(data._z.c_str()).toDouble());
                    auto dis = WD::DVec3::Distance(tempPos, i->sPtL().position);
                    auto dd = i->sPtL().position;
                    if (dis < disOld)
                    {
                        number = QString(data._nodeId.c_str()).toInt();
                        i->_applyNumberN = number;
                        disOld = dis;
                    }
                }
                if (number == -1)
                {
                    continue;
                }
                auto iter = pJsonMgr->_pThrustAndMement->_tamDataMap.find(number);
                if (iter == pJsonMgr->_pThrustAndMement->_tamDataMap.end())
                {
                    continue;
                }
                dataMap[number] = iter->second;
                WDNode* tempNode = node;
                while (tempNode != nullptr && !tempNode->isType("EQUI"))
                {
                    tempNode = tempNode->parent().get();
                }
                nodeMap[number] = tempNode;
            }
        }


        //测试代码记得删
        //auto iter = pJsonMgr->_pThrustAndMement->_tamDataMap.find(102);
        //if (iter != pJsonMgr->_pThrustAndMement->_tamDataMap.end())
        //{
        //    dataMap[102] = iter->second;
        //    nodeMap[102] = newnode;
        //}
        //auto itedr = pJsonMgr->_pThrustAndMement->_tamDataMap.find(103);
        //if (itedr != pJsonMgr->_pThrustAndMement->_tamDataMap.end())
        //{
        //    dataMap[103] = itedr->second;
        //    nodeMap[103] = newnode2;
        //}


        position = position + DVec2(0, -13.23 * scale.y * dataMap.size());
        items.push_back({});
        items.back().position = position;
        items.back().size = DVec2(180, 8.83) * scale;
        items.back().text = QString::fromUtf8("端点推力F(N)和力矩(N.m)表    (接口力和力矩已由汽机厂确认)");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };

        items.push_back({});
        items.back().position = position + DVec2(0, 8.83) * scale;
        items.back().size = DVec2(32.85, 13.18) * scale;
        items.back().texts.emplace_back(QString::fromUtf8("应力计算"));
        items.back().texts.emplace_back(QString::fromUtf8("端点编号"));
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        items.push_back({});
        items.back().position = position + DVec2(32.85, 8.83) * scale;
        items.back().size = DVec2(72.62, 7.51) * scale;
        items.back().text = QString::fromUtf8("力(N)");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        {
            items.push_back({});
            items.back().position = position + DVec2(32.85, 16.34) * scale;
            items.back().size = DVec2(15.84, 5.67) * scale;
            items.back().text = QString::fromUtf8("X");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(48.69, 16.34) * scale;
            items.back().size = DVec2(19.78, 5.67) * scale;
            items.back().text = QString::fromUtf8("Y");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(68.47, 16.34) * scale;
            items.back().size = DVec2(20.08, 5.67) * scale;
            items.back().text = QString::fromUtf8("Z");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(88.55, 16.34) * scale;
            items.back().size = DVec2(16.93, 5.67) * scale;
            items.back().text = QString::fromUtf8("F(合)");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }
        items.push_back({});
        items.back().position = position + DVec2(105.47, 8.83) * scale;
        items.back().size = DVec2(74.53, 7.51) * scale;
        items.back().text = QString::fromUtf8("力矩(N.M)");
        items.back().textStyle.fontSize = 3 * yScale;
        items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        {
            items.push_back({});
            items.back().position = position + DVec2(105.47, 16.34) * scale;
            items.back().size = DVec2(19.35, 5.67) * scale;
            items.back().text = QString::fromUtf8("MX");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(124.82, 16.34) * scale;
            items.back().size = DVec2(19.92, 5.67) * scale;
            items.back().text = QString::fromUtf8("MY");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(144.74, 16.34) * scale;
            items.back().size = DVec2(19.02, 5.67) * scale;
            items.back().text = QString::fromUtf8("MZ");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            items.push_back({});
            items.back().position = position + DVec2(163.76, 16.34) * scale;
            items.back().size = DVec2(16.06, 5.67) * scale;
            items.back().text = QString::fromUtf8("M(合)");
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
        }

        char info[1024] = { 0 };
        auto offy = 0.0;
        for (auto& i : dataMap)
        {
            TAMData hotData;
            TAMData coldData;
            WDNode* node = nullptr;
            auto itor = nodeMap.find(i.first);
            if (!i.second.empty() && itor != nodeMap.end())
            {
                hotData = i.second.front();
                coldData = i.second.back();
                node = itor->second;
            }
            if (node == nullptr)
            {
                continue;
            }
            items.push_back({});
            items.back().position = position + DVec2(0, offy) * scale + DVec2(0, 22.01) * scale;
            items.back().size = DVec2(24.48, 13.23) * scale;
            items.back().texts.emplace_back(QString::fromUtf8(node->name().c_str()));
            items.back().texts.emplace_back(QString::fromUtf8("接口  ") + (QString::number(i.first)));
            items.back().textStyle.fontSize = 3 * yScale;
            items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            auto x = QString::fromUtf8(hotData._x.c_str()).toDouble();
            auto y = QString::fromUtf8(hotData._y.c_str()).toDouble();
            auto z = QString::fromUtf8(hotData._z.c_str()).toDouble();
            auto mx = QString::fromUtf8(hotData._mx.c_str()).toDouble();
            auto my = QString::fromUtf8(hotData._my.c_str()).toDouble();
            auto mz = QString::fromUtf8(hotData._mz.c_str()).toDouble();
            auto subF = std::sqrt(x*x + y*y + z*z);
            auto subMf = std::sqrt(mx * mx + my * my + mz * mz);
            {
                //热态
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(24.48, 22.01) * scale;
                items.back().size = DVec2(8.37, 6.62) * scale;
                items.back().text = QString::fromUtf8("热态");
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(32.85, 22.01) * scale;
                items.back().size = DVec2(15.84, 6.62) * scale;
                items.back().text = QString::fromUtf8(hotData._x.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(48.69, 22.01) * scale;
                items.back().size = DVec2(19.78, 6.62) * scale;
                items.back().text = QString::fromUtf8(hotData._y.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(68.47, 22.01) * scale;
                items.back().size = DVec2(20.08, 6.62) * scale;
                items.back().text = QString::fromUtf8(hotData._z.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(88.55, 22.01) * scale;
                items.back().size = DVec2(16.93, 6.62) * scale;
                sprintf_s(info, sizeof(info), "%.1f", subF);
                items.back().text = QString::fromUtf8(info);
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(105.47, 22.01) * scale;
                items.back().size = DVec2(19.35, 6.62) * scale;
                items.back().text = QString::fromUtf8(hotData._mx.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(124.82, 22.01) * scale;
                items.back().size = DVec2(19.92, 6.62) * scale;
                items.back().text = QString::fromUtf8(hotData._my.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(144.74, 22.01) * scale;
                items.back().size = DVec2(19.02, 6.62) * scale;
                items.back().text = QString::fromUtf8(hotData._mz.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(163.76, 22.01) * scale;
                items.back().size = DVec2(16.06, 6.62) * scale;

                sprintf_s(info, sizeof(info), "%.1f", subMf);
                items.back().text = QString::fromUtf8(info);
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            }
            {
                //冷态
                x = QString::fromUtf8(hotData._x.c_str()).toDouble();
                y = QString::fromUtf8(hotData._y.c_str()).toDouble();
                z = QString::fromUtf8(hotData._z.c_str()).toDouble();
                mx = QString::fromUtf8(hotData._mx.c_str()).toDouble();
                my = QString::fromUtf8(hotData._my.c_str()).toDouble();
                mz = QString::fromUtf8(hotData._mz.c_str()).toDouble();
                subF = std::sqrt(x * x + y * y + z * z);
                subMf = std::sqrt(mx * mx + my * my + mz * mz);
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(24.48, 28.63) * scale;
                items.back().size = DVec2(8.37, 6.62) * scale;
                items.back().text = QString::fromUtf8("冷态");
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(32.85, 28.63) * scale;
                items.back().size = DVec2(15.84, 6.62) * scale;
                items.back().text = QString::fromUtf8(coldData._x.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(48.69, 28.63) * scale;
                items.back().size = DVec2(19.78, 6.62) * scale;
                items.back().text = QString::fromUtf8(coldData._y.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(68.47, 28.63) * scale;
                items.back().size = DVec2(20.08, 6.62) * scale;
                items.back().text = QString::fromUtf8(coldData._z.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(88.55, 28.63) * scale;
                items.back().size = DVec2(16.93, 6.62) * scale;
                sprintf_s(info, sizeof(info), "%.1f", subF);
                items.back().text = QString::fromUtf8(info);
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(105.47, 28.63) * scale;
                items.back().size = DVec2(19.35, 6.62) * scale;
                items.back().text = QString::fromUtf8(coldData._mx.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(124.82, 28.63) * scale;
                items.back().size = DVec2(19.92, 6.62) * scale;
                items.back().text = QString::fromUtf8(coldData._my.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(144.74, 28.63) * scale;
                items.back().size = DVec2(19.02, 6.62) * scale;
                items.back().text = QString::fromUtf8(coldData._mz.c_str());
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
                items.push_back({});
                items.back().position = position + DVec2(0, offy) * scale + DVec2(163.76, 28.63) * scale;
                items.back().size = DVec2(16.06, 6.62) * scale;
                sprintf_s(info, sizeof(info), "%.1f", subMf);
                items.back().text = QString::fromUtf8(info);
                items.back().textStyle.fontSize = 3 * yScale;
                items.back().textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center };
            }
            offy += 13.23;
        }
        return        abs(position.y - startPos.y);
    }
};
TableStyleXB::TableStyleXB(WDCore& core) : TableStyleBase(core)
{
}

TableStyleXB::~TableStyleXB()
{
}

void TableStyleXB::init(ISOPaper& paper) const
{
    // 图纸大小
    ISOPaper::Size paperSize = ISOPaper::Size(841.0, 594.0);
    // 图纸边缘留白
    ISOPaper::Margins paperMargins = ISOPaper::Margins(25.0, 10.0, 10.0, 10.0);
    // 图纸边框大小
    ISOPaper::Margins frameMargins = ISOPaper::Margins(25.0, 10.0, 10.0, 10.0);

    paper.setSize(paperSize);
    paper.setMargin(paperMargins);
    paper.frame = ISOPaper::Frame(10.0, 10.0, 25.0, 10.0, 0.35, true);
    // 材料表区
    auto& materialTable = paper.materialTable;
    materialTable.innerBorderLine.width = 0.18f;
    materialTable.outerBorderLine.width = 0.18f;
    //这里每次都得清空一下已有的flag，不然出完西北院再出华东院会表不一样，
    for (auto& i : ISOMaterialArea::AllFlags)
    {
        if (!materialTable.flags.hasFlag(i))
        {
            continue;
        }
        materialTable.flags.removeFlag(i);
    }
    materialTable.flags.addFlags(ISOMaterialArea::MPF_Number
        , ISOMaterialArea::MPF_Descrip
        , ISOMaterialArea::MPF_Material
        , ISOMaterialArea::MPF_Diameter
        , ISOMaterialArea::MPF_MaterialCode
        , ISOMaterialArea::MPF_Count
        , ISOMaterialArea::MPF_TotalWeight
        , ISOMaterialArea::MPF_Note);

    materialTable.details = ISOMaterialArea::DT_RText;
    materialTable.texture = ISOMaterialArea::TT_Null;
    materialTable.align = ISOMaterialArea::MTA_RightTop;

    // 列宽和表头名称应该由界面上根据设置的flag及对应的信息决定，这里暂时写死
    materialTable.colWidths = { 15.86, 68.22, 40.26, 21.38, 38.57, 18.0, 28.29, 25.01 };
    materialTable.width = 0.0;
    for (auto& each : materialTable.colWidths)
        materialTable.width += each;
    materialTable.defaultRowHeight = 11.46;
    materialTable.bVisibleGrid = true;

    auto& titleBar = paper.titleBar;
    // 标题栏
    titleBar.innerBorderLine.width = 0.18f;
    titleBar.outerBorderLine.width = 0.18f;
    titleBar.width = 180.0;
    titleBar.height = 40.0;
    titleBar.align = ISOTitleBarArea::TBA_RightBottom;
    // 签名栏
    auto& signBar = paper.signBar;
    signBar.innerBorderLine.width = 0.18f;
    signBar.outerBorderLine.width = 0.18f;
    signBar.height = 35.0;
    signBar.textFont.fontSize = 3.5;
    signBar.align = ISOSignBarArea::SBA_Top;
    signBar.colWidths = { 8.0, 17.0, 20.0, 20.0, 20.0, 40.0, 20.0 ,35.0 };
    signBar.width = 0.0;
    signBar.setPosition(DVec2(paper.paintArea().right() - titleBar.width, paper.paintArea().bottom() - titleBar.height - signBar.height));
    for (auto& each : signBar.colWidths)
        signBar.width += each;
    // 图纸描述区
    auto& paperDescArea = paper.paperDescArea;
    paperDescArea.innerBorderLine.width = 0.18f;
    paperDescArea.outerBorderLine.width = 0.18f;
    paperDescArea.height = 33.0;
    paperDescArea.textFont.fontSize = 3.5;
    paperDescArea.colWidths = { 22.5, 22.5, 22.5, 22.5, 22.5, 22.5, 22.5, 22.5 };
    paperDescArea.width = 0.0;
    for (auto& each : paperDescArea.colWidths)
        paperDescArea.width += each;
    paperDescArea.setPosition(DVec2(paper.paintArea().left() + signBar.width, paper.paintArea().bottom() - paperDescArea.height));
    paper.update(std::nullopt);
}

void TableStyleXB::exec(WDISOPainterCmd& cmdPainter
    , WD::WDNode& branNode
    , ISOPaper& paper
    , const TuLine& line) const
{
    paper.update(branNode.aabb());
    
    cmdPainter.appendGroup("sideArea");
    // 绘制图纸的边框
    TableStyleXBPrivate::DrawLineSide(cmdPainter, paper);
    // 绘制标题栏区
    TableStyleXBPrivate::TitleBarItems items;
    TableStyleXBPrivate::GetTitleBarData(branNode, paper, items, *this);
    cmdPainter.appendGroup("titleBar");
    TableStyleXBPrivate::DrawTitleBar(cmdPainter, paper, items);

    //签署栏
    cmdPainter.appendGroup("signBar");
    TableStyleXBPrivate::DrawSignBar(cmdPainter, paper, items);

    //坐标系说明
    cmdPainter.appendGroup("AxisBar");
    TableStyleXBPrivate::DrawAxisBar(cmdPainter, paper, items, _pJsonMgr);
    // 统计并更新材料表
    cmdPainter.appendGroup("MaterialTable");
    auto heightOffset = UpdateMaterialXbTable(_pJsonMgr , cmdPainter, _core, &line, paper, this->style());
    //特性数据表
    TableStyleXBPrivate::TitleBarItems characteristicTableItems;
    TableStyleXBPrivate::updateCharacteristicTableData(branNode, paper, characteristicTableItems, _pJsonMgr);
    cmdPainter.appendGroup("CharacteristicTable");
    TableStyleXBPrivate::DrawTableItem(cmdPainter, paper, characteristicTableItems);

    // 统计并更新弯管角度一览表
    DVec2 position = paper.materialTable.position();

    cmdPainter.appendGroup("AngleTable");
    TableStyleXBPrivate::TitleBarItems angleTableItems;
    heightOffset = TableStyleXBPrivate::updateAngleTableData(heightOffset, &line, paper, angleTableItems,_pJsonMgr);
    TableStyleXBPrivate::DrawTableItem(cmdPainter, paper, angleTableItems);
    // 统计并更新最大应力表
    cmdPainter.appendGroup("MaxStressTable");
    TableStyleXBPrivate::TitleBarItems maxStressItems;
    heightOffset = TableStyleXBPrivate::updateMaxStressData(heightOffset, &line, paper, maxStressItems, _pJsonMgr);
    TableStyleXBPrivate::DrawTableItem(cmdPainter, paper, maxStressItems);
    // 统计并更新管材参数表
    cmdPainter.appendGroup("TubeParametersTable");
    TableStyleXBPrivate::TitleBarItems tubeParametersItems;
    heightOffset = TableStyleXBPrivate::updateTubeParametersData(heightOffset, branNode, paper, tubeParametersItems, _pJsonMgr);
    TableStyleXBPrivate::DrawTableItem(cmdPainter, paper, tubeParametersItems);
    // 统计并更新端点推力表
    cmdPainter.appendGroup("EndThrustTable");
    TableStyleXBPrivate::TitleBarItems endThrustItems;
    heightOffset = TableStyleXBPrivate::updateEndThrustData(&line, paper, endThrustItems, _pJsonMgr);
    TableStyleXBPrivate::DrawTableItem(cmdPainter, paper, endThrustItems);
    //统计并更新文字说明区
    cmdPainter.appendGroup("NotesTable");
    TableStyleXBPrivate::TitleBarItems noteItems;
    heightOffset = TableStyleXBPrivate::updateNoteTableData(heightOffset, paper, noteItems, _pJsonMgr);
    heightOffset += 18;
    TableStyleXBPrivate::DrawTableItem(cmdPainter, paper, noteItems, false);
    if (_pJsonMgr == nullptr || _pJsonMgr->_pNoteExcel->_data.empty())
    {
        return;
    }
    //画符号
    auto startPos = WD::DVec2(paper.paintArea().right(), paper.paintArea().bottom());
    //绘制表格边框
    //这里按照A1尺寸来写，然后根据实际选择纸张大小做不同的缩放。
    auto scale = DVec2(paper.size().width() / 841.0, paper.size().height() / 594.0);
    startPos.y = heightOffset;
    startPos = startPos - DVec2(187.1, 0) * scale;
    //符号画在第八行
    startPos = startPos+ DVec2(0, 6.5) * scale*7;
    WDLineStyle lineStyle;
    lineStyle.width = 0.1;
    cmdPainter.drawArc(startPos,14.58*scale.x, PI / 2, PI );
    cmdPainter.drawLine(startPos, startPos - DVec2( 14.58 * scale.x,0), lineStyle);
    cmdPainter.drawLine(startPos, startPos - DVec2(0,14.58 * scale.x), lineStyle);
    cmdPainter.drawLine(startPos - DVec2(1.88 * scale.x, 1.88 * scale.x), startPos - DVec2(1.88 * scale.x, 0), lineStyle);
    cmdPainter.drawLine(startPos - DVec2(1.88 * scale.x, 1.88 * scale.x), startPos - DVec2(0, 1.88 * scale.x), lineStyle);
}
/*
void TableStyleXB::exec(WDAbstractPainter2D* painter, const WD::WDNode::Nodes& pipeNodes, ISOPaper& paper, const std::vector<TuLine>& lines) const
{

    if (painter == nullptr)
    {
        return;
    }
    paper.update(branNode.aabb());
    auto pGroup = paper.polttingArea.pGroup;
    if (pGroup == nullptr)
    {
        assert(false);
        return;
    }

    auto svgPainter = dynamic_cast<WDISOSVGPainter*>(painter);
    auto tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "sideArea");
    bool dxf = false;
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    else if (dynamic_cast<WDISODxfPainter*>(painter) != nullptr)
    {
        dxf = true;
    }

    // 绘制图纸的边框
    TableStyleXBPrivate::DrawLineSide(*painter, paper, dxf);
    // 绘制标题栏区
    TableStyleXBPrivate::TitleBarItems items;
    TableStyleXBPrivate::GetTitleBarData(branNode, paper, items, *this);
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "titleBar");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleXBPrivate::DrawTitleBar(*painter, paper, items);

    //签署栏
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "signBar");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleXBPrivate::DrawSignBar(*painter, paper, items);

    //坐标系说明
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "AxisBar");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleXBPrivate::DrawAxisBar(*painter, paper, items, _pJsonMgr);
    // 统计并更新材料表
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "MaterialTable");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    auto heightOffset = UpdateMaterialXbTable(_pJsonMgr, *painter, _core, &line, paper, this->style());
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = nullptr;
    }
    //特性数据表
    TableStyleXBPrivate::TitleBarItems characteristicTableItems;
    TableStyleXBPrivate::updateCharacteristicTableData(branNode, paper, characteristicTableItems, _pJsonMgr);
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "CharacteristicTable");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleXBPrivate::DrawTableItem(*painter, paper, characteristicTableItems);

    // 统计并更新弯管角度一览表
    DVec2 position = paper.materialTable.position();

    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "AngleTable");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleXBPrivate::TitleBarItems angleTableItems;
    heightOffset = TableStyleXBPrivate::updateAngleTableData(heightOffset, &line, paper, angleTableItems, _pJsonMgr);
    TableStyleXBPrivate::DrawTableItem(*painter, paper, angleTableItems);
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = nullptr;
    }
    // 统计并更新最大应力表
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "MaxStressTable");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleXBPrivate::TitleBarItems maxStressItems;
    heightOffset = TableStyleXBPrivate::updateMaxStressData(heightOffset, &line, branNode, paper, maxStressItems, _pJsonMgr);
    TableStyleXBPrivate::DrawTableItem(*painter, paper, maxStressItems);
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = nullptr;
    }
    // 统计并更新管材参数表
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "TubeParametersTable");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleXBPrivate::TitleBarItems tubeParametersItems;
    heightOffset = TableStyleXBPrivate::updateTubeParametersData(heightOffset, branNode, paper, tubeParametersItems, _pJsonMgr);
    TableStyleXBPrivate::DrawTableItem(*painter, paper, tubeParametersItems);
    // 统计并更新端点推力表
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "EndThrustTable");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleXBPrivate::TitleBarItems endThrustItems;
    heightOffset = TableStyleXBPrivate::updateEndThrustData(&line, branNode, paper, endThrustItems, _pJsonMgr);
    TableStyleXBPrivate::DrawTableItem(*painter, paper, endThrustItems);
    //统计并更新文字说明区
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "NotesTable");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleXBPrivate::TitleBarItems noteItems;
    heightOffset = TableStyleXBPrivate::updateNoteTableData(heightOffset, branNode, paper, noteItems, _pJsonMgr);
    heightOffset += 18;
    TableStyleXBPrivate::DrawTableItem(*painter, paper, noteItems, false);
    if (svgPainter != nullptr)
    {
        //每次写完把pGroup置空
        svgPainter->pGroup = nullptr;
    }
    if (_pJsonMgr == nullptr || _pJsonMgr->_pNoteExcel->_data.empty())
    {
        return;
    }
    //画符号
    auto startPos = WD::DVec2(paper.paintArea().right(), paper.paintArea().bottom());
    //绘制表格边框
    //这里按照A1尺寸来写，然后根据实际选择纸张大小做不同的缩放。
    auto scale = DVec2(paper.size().width() / 841.0, paper.size().height() / 594.0);
    startPos.y = heightOffset;
    startPos = startPos - DVec2(187.1, 0) * scale;
    //符号画在第八行
    startPos = startPos + DVec2(0, 6.5) * scale * 7;
    WDLineStyle lineStyle;
    lineStyle.width = 0.1;
    painter->drawArc(startPos, 14.58 * scale.x, PI / 2, PI);
    painter->drawLine(startPos, startPos - DVec2(14.58 * scale.x, 0), lineStyle);
    painter->drawLine(startPos, startPos - DVec2(0, 14.58 * scale.x), lineStyle);
    painter->drawLine(startPos - DVec2(1.88 * scale.x, 1.88 * scale.x), startPos - DVec2(1.88 * scale.x, 0), lineStyle);
    painter->drawLine(startPos - DVec2(1.88 * scale.x, 1.88 * scale.x), startPos - DVec2(0, 1.88 * scale.x), lineStyle);
    if (svgPainter != nullptr)
    {
        //每次写完把pGroup置空
        svgPainter->pGroup = nullptr;
    }
   
}
 */
DependJsonMgr* TableStyleXB::dependJsonMgr()
{
    return _pJsonMgr;
}

void TableStyleXB::setDependJsonMgr(DependJsonMgr* pJsonMgr)
{
    _pJsonMgr = pJsonMgr;
}
