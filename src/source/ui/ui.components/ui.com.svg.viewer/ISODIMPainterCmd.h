#pragma once
#include "core/viewer/dimension/WDDIMPainter.h"
#include "WDRapidxml.h"
#include "ISOUnitConvert.h"
#include <QFont>
#include <QFontMetrics>

WD_NAMESPACE_BEGIN

class ISODIMPainterCmd : public WDDIMPainter
{
public:
    struct TextSizeCalculator
    {
        QFont font;
        // 宽高
        DVec2 size;
        DVec2 halfSize;
        // 紧裹着文字像素的宽高
        DVec2 tightSize;
        DVec2 halfTightSize;
        // 边框的宽高(如果没有边框，则边框宽高为0）
        DVec2 borderSize;
        DVec2 halfBorderSize;
        // 从宽高 和 边框宽高中取最大值
        DVec2 maxSize;
        // 从宽高 和 边框宽高中取最大值的一半
        DVec2 halfMaxSize;
        // 边框位置与文本位置的偏移量(这里认为文本位置坐标在文本的左下角)
        // 文字边框的左下角坐标 = textPos(左下角) + borderPosOffset
        DVec2 borderPosOffset;
    public:
        TextSizeCalculator(const ISOUnitConvert& uCvt
            , const std::string_view& text
            , const WDDIMFontStyle& style);
    public:
        /**
         * @brief 给定文字的屏幕坐标的方向, 计算文字的旋转角度
         *  以保证文字的上方向与屏幕的up方向夹角最小，即尽量避免文字倒着显示在屏幕上。
        */
        static double RotateAngle(const DVec2& vec);
    };
public:
    static constexpr double Epsilon = NumLimits<float>::Epsilon;
public:
    ISODIMPainterCmd(const ISOUnitConvert& cvt);
    virtual ~ISODIMPainterCmd();
public:
    inline const ISOUnitConvert& cvt() const 
    {
        return _cvt;
    }
public:
    /**
     * @brief 指定文字样式以及文本，测量文字尺寸
    */
    virtual DVec2 calcTextSize(const std::string& text
        , const WDDIMFontStyle& style = WDDIMFontStyle()) final;
public:
    /**
     * @brief 绘制点集
     * @param points
     * @param style
     */
    virtual void drawPoints(const std::vector<DVec3>& points
        , const WDDIMPointStyle& style = WDDIMPointStyle()) override;
    /**
     * @brief 绘制线段
     * @param sPos 起点
     * @param ePos 终点
     * @param style
     */
    virtual void drawLine(const DVec3& sPos, const DVec3& ePos
        , const WDDIMLineStyle& style = WDDIMLineStyle()) override;
    /**
     * @brief 绘制一组线段
     * @param points 点集，两两一组组成线段
     * @param style
     */
    virtual void drawLines(const std::vector<DVec3>& points
        , const WDDIMLineStyle& style = WDDIMLineStyle()) override;
    /**
     * @brief 绘制不闭合折线
     * @param points 点集
     * @param style
     * @param fillColor 填充颜色
     */
    virtual void drawBrokenLine(const std::vector<DVec3>& points
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , WD::Color fillColor = WD::Color(0, 0, 0, 0)) override;
    /**
     * @brief 绘制闭合折线（头尾坐标不一样自动闭合）
     * @param points
     * @param style
     * @param fillColor
     */
    virtual void drawLoopLine(const std::vector<DVec3>& points
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , WD::Color fillColor = WD::Color(0, 0, 0, 0)) override;
    /**
     * @brief 二维绘制闭合折线（一般开发自己画着调试看）
     * @param points
     * @param style
     * @param fillColor
     */
    virtual void drawLoopLine2D(const std::vector<DVec2>& points
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , WD::Color fillColor = WD::Color(0, 0, 0, 0)) override;
    /**
     * @brief 绘制文本
     * @param text 文本
     * @param position 文本位置 ,二维坐标
     * @param textAlign 文字对齐方式
    */
    virtual void drawText2D(const std::string& text
        , const DVec2& position
        , const WDDIMFontStyle& style = WDDIMFontStyle()
        , const WDDIMAlign& textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Center })override;
    /**
     * @brief 绘制圆弧
     * @param center
     * @param radius
     * @param sDirection
     * @param eDirection
     * @param style
     */
    virtual void drawArc(const DVec3& center
        , double radius
        , const DVec3& sDirection
        , const DVec3& eDirection
        , const WDDIMLineStyle& style = WDDIMLineStyle()) override;
    /**
     * @brief 绘制一个矩形线框
     * @param center 中心点
     * @param size x长y宽
     * @param xAxis 长边的方向，用来确定矩形朝向
     * @param planeNormal 平面法线
     * @param style
     */
    virtual void drawRect(const DVec3& center
        , const DVec2& size
        , const DVec3& xAxis
        , const DVec3& planeNormal
        , const WDDIMLineStyle& style = WDDIMLineStyle()) override;
    /**
     * @brief 绘制填充的矩形
     * @param center 中心点
     * @param size x长y宽
     * @param xAxis 长边的方向，用来确定矩形朝向
     * @param planeNormal 平面法线
     * @param style
     */
    virtual void fillRect(const DVec3& center
        , const DVec2& size
        , const DVec3& xAxis
        , const DVec3& planeNormal
        , const WDDIMShapeFillStyle& style = WDDIMShapeFillStyle()) override;
    /**
     * @brief 绘制圆
     * @param center 圆心
     * @param radius 半径
     * @param planeNormal 平面法线
     * @param style
     */
    virtual void drawCircle(const DVec3& center
        , double radius
        , const DVec3& planeNormal
        , const WDDIMLineStyle& style = WDDIMLineStyle()) override;
    /**
     * @brief 填充一个圆
     * @param center 圆心
     * @param radius 半径
     * @param planeNormal 平面法线
     * @param style
     * @param xAxis 圆的朝向，参考矩形的长边朝向
     */
    virtual void fillCircle(const DVec3& center
        , double radius
        , const DVec3& planeNormal
        , const WDDIMShapeFillStyle& style = WDDIMShapeFillStyle()
        , const std::optional<DVec3>& xAxis = std::nullopt) override;
    /**
     * @brief 绘制一个三角形线框
     * @param vertices
     * @param style
     */
    virtual void drawTriangle(const std::array<DVec3, 3>& vertices
        , const WDDIMLineStyle& style = WDDIMLineStyle()) override;
    /**
     * @brief 填充一个三角形线框
     * @param vertices
     * @param style
     */
    virtual void fillTriangle(const std::array<DVec3, 3>& vertices
        , const WDDIMShapeFillStyle& style = WDDIMShapeFillStyle()
        , const std::optional<DVec3>& xAxis = std::nullopt) override;
    /**
     * @brief
     * @param text
     * @param position
     * @param rightDir
     * @param upDir
     * @param style
     * @param textAlign
     * @return
     */
    virtual DVec2 drawText(const std::string& text
        , const DVec3& position
        , const DVec3& rightDir
        , const DVec3& upDir
        , const WDDIMFontStyle& style = WDDIMFontStyle()
        , const WDDIMAlign& textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Center }) override;
    /**
     * @brief
     * @param pos
     * @param rightDir
     * @param upDir
     * @param texts
     * @param align
     * @param rowAlign
     * @return
     */
    virtual DVec2 drawTexts(const DVec3& pos
        , const DVec3& rightDir
        , const DVec3& upDir
        , std::vector<std::pair<std::string, WDDIMFontStyle> > texts
        , WDDIMAlign align = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Center }
    , WDDIMAlign::HAlign rowAlign = WDDIMAlign::HAlign::HA_Left) override;
public:
    void repaintToPainter(WDDIMPainter& painter);
    inline void clearCmds() 
    {
        _cmds.clear();
    }
private:
    // 单位转换
    const ISOUnitConvert& _cvt;
    /**
     * @brief 命令类
     */
    class Cmd
    {
        // 
    public:
        using Func = std::function<void(WDDIMPainter& painter)>;
    public:
        Cmd(Func func) :_func(func)
        {

        }
    public:
        /**
         * @brief 将当前命令中的内容绘制到其他Painter中
         */
        void repaint(WDDIMPainter& painter) const 
        {
            _func(painter);
        }
    private:
        Func _func;
    };
    using Cmds = std::vector<Cmd>;
    Cmds _cmds;

};


WD_NAMESPACE_END


