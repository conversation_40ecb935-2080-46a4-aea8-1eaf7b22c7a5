#include    "GenInsDxfDialog.h"
#include    "core/nodeTree/WDNodeTree.h"
#include    "core/nodeTree/WDNodeList.h"
#include    "core/message/WDMessage.h"
#include    <QFileDialog>
#include    "ui.com.svg.viewer.h"
#include    <QPdfWriter>
#include    "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include    "core/WDBlockingTask.h"
#include    "IsoIndexTable.h"
#include    <QProcess>

enum CBBType
{
    CBBT_CE = 0,
    CBBT_List,
};

static QMarginsF SMarginPDF = QMarginsF(0, 0, 0, 0);

GenInsDxfDialog::GenInsDxfDialog(WD::WDCore& core
    , ISOPaperMgr& isoPaperMgr
    , UIComSvgViewer& viewer
    , QWidget *parent)
    : QDialog(parent)
    , _core(core)
    , _isoPaperMgr(isoPaperMgr)
    , _viewer(viewer)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    retranslateUi();
    
    connect(ui.pushButtonSelectPath, &QPushButton::clicked, this, [=]()
    {
        QString dirPath = QFileDialog::getExistingDirectory(
            this
            , QString::fromUtf8("生成目录")
            , "");

        if (dirPath.isEmpty())
            return;

        ui.lineEditPath->setText(dirPath);
    });

    connect(ui.pushButton, &QPushButton::clicked, this, [=]()
        {
            QString dirPath = QFileDialog::getExistingDirectory(
                this
                , QString::fromUtf8("数据来源目录")
                , "");

            if (dirPath.isEmpty())
                return;

            ui.lineEdit->setText(dirPath);
        });

    connect(ui.pushButtonGenerate
        , &QPushButton::clicked
        , this
        ,  &GenInsDxfDialog::slotPushButtonGenerateClicked);
}

GenInsDxfDialog::~GenInsDxfDialog()
{
}

void GenInsDxfDialog::slotOutput()
{
    //QByteArray data = process->read
}

void GenInsDxfDialog::slotPushButtonGenerateClicked()
{

    QString dataStr = ui.lineEditPath->text();
    QString dirStr = ui.lineEdit->text();
    if(dirStr.isEmpty()|| dataStr.isEmpty())
    {
        WD_ERROR_T("UiComSvgViewer", "CheckPath");
        return;
    }
    QDir inputDir(dirStr);
    QDir outputDir(dataStr);
    if (!inputDir.exists()|| !outputDir.exists())
    {
        WD_ERROR_T("UiComSvgViewer", "CheckPath");
        return ;
    }

    QDir dir(QApplication::applicationDirPath());
    dir.cdUp();
    auto folder = dir.absolutePath();
    auto path = folder + "/data/iso/CarserData/CSData.exe";
    auto dirPath = folder + "/data/iso/CarserData";
    std::string patht = path.toLocal8Bit().data();
    path.replace("/", "\\");
    std::string tempStr = path.toLocal8Bit().data();

    QProcess process(this);
    QStringList args;
    auto workingDir = process.workingDirectory();
    process.setWorkingDirectory(dirPath);
    args.push_back(dirStr);
    process.setProgram(path);
    process.setArguments(args);
    process.start();

    if (!process.waitForStarted(3000))
    {
    }
    process.waitForFinished();
    process.setWorkingDirectory(workingDir);

    // 获取分支节点
    auto pCurrentNode = _core.nodeTree().currentNode();
    if (pCurrentNode == nullptr)
        return;

    WD::WDNode::Nodes pBranNodes;
    if (pCurrentNode->isType("BRAN"))
    {
        pBranNodes.push_back(pCurrentNode);
    }
    else if (WDBMDPipeUtils::IsPipeComponent(*pCurrentNode) || pCurrentNode->isType("TUBI"))
    {
        pBranNodes.push_back(pCurrentNode->parent());
    }
    else if (pCurrentNode->isType("PIPE"))
    {
        for (auto& pChild : pCurrentNode->children())
        {
            if (pChild == nullptr)
                continue;
            if (pChild->isType("BRAN"))
                pBranNodes.push_back(pChild);
        }
    }
    if (pBranNodes.empty())
    {
        WD_ERROR_T("UiComSvgViewer", "CheckNodes");
        return;
    }
    // 图纸对象
    WD::ISOPaper* pPaper = _isoPaperMgr.getCurrentPaper();
    pPaper->setOutputSize(_paperSize);
    assert(pPaper != nullptr);
    if (pPaper == nullptr)
    {
        WD_ERROR_T("UiComSvgViewer", "CheckObject");
        return;
    }

    pPaper->_genType = GenPaperType::GPT_INS_DXF;
    {
        pPaper->setSize(ISOPaper::Size(841.0, 594.0));
        pPaper->info.pType = ISOPaper::PT_A1;
    }

    double width = pPaper->uCvt.paperToPixel(pPaper->size().width());
    double height = pPaper->uCvt.paperToPixel(pPaper->size().height());
    char szView[1024] = { 0 };
    sprintf(szView, "%d %d %d %d", 0, 0, static_cast<int>(width), static_cast<int>(height));

    BranchISODatas isoDatas;
    for (int i = 0; i < pBranNodes.size(); ++i)
    {
        auto& each = pBranNodes[i];
        if (each == nullptr)
        {
            assert(false);
            continue;
        }

        std::shared_ptr<WD::WDSvgXmlCreator> pSvg = std::make_shared<WD::WDSvgXmlCreator>("xml version='1.0' encoding='utf-8' standalone='no'");
        if (pSvg == nullptr)
        {
            assert(false);
            continue;
        }
        pSvg->root("svg").attr("viewBox", std::string(szView)).attr("xmlns", "http://www.w3.org/2000/svg").attr("xmlns:xlink", "http://www.w3.org/1999/xlink");

        pPaper->update(each->aabb());
        QString name = QString::fromUtf8(each->name().c_str());
        name.replace('/', '_');
        name.replace('\\', '_');
        dataStr = dataStr + "/" + name + ".dxf";
        //_viewer.genISODxfData(*pPaper, *each, *pSvg, dirStr, dataStr,"", static_cast<uint>(i), static_cast<uint>(pBranNodes.size()));
        isoDatas.push_back({ each, pSvg });
        if (!dataStr.isEmpty())
        {
            WD_INFO_T("Common", "GenSuccess");
        }
    }
    if (isoDatas.empty())
    {
        WD_ERROR_T("UiComSvgViewer", "CheckNodes");
        return;
    }
}

void GenInsDxfDialog::retranslateUi()
{
    Trs("GenInsDxfDialog"
        , static_cast<QDialog*>(this)
        , ui.labelPath
        , ui.label
        , ui.lineEdit
        , ui.pushButton
        , ui.pushButtonGenerate
        , ui.pushButtonSelectPath
    );
}