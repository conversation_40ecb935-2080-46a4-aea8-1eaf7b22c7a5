#include "PipeCreateDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/message/WDMessage.h"
#include "businessModule/design/WDBMDesign.h"
#include "core/businessModule/WDBMAuthorityMgr.h"
#include "core/undoRedo/WDUndoStack.h"

PipeCreateDialog::PipeCreateDialog(PipeSlopeModelingCommon& common, QWidget *parent)
    : QDialog(parent)
    , _common(common)
    , _nameHelpter(common.app().getBMDesign())
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    retranslateUi();

    connect(ui.pushButtonOk
        , SIGNAL(clicked())
        , this
        , SLOT(slotPushButtonOkClicked()));

    connect(ui.pushButtonCancel
        , SIGNAL(clicked())
        , this
        , SLOT(slotPushButtonCancelClicked()));

    _nameHelpter.setLineEdit(ui.lineEditName);
}
PipeCreateDialog::~PipeCreateDialog()
{

}

void PipeCreateDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);
    _nameHelpter.resetName();
    // 等级
    if (_common.pSpec() != nullptr)
        ui.lineEditPSpec->setText(QString::fromUtf8(_common.pSpec()->name().c_str()));
    else
        ui.lineEditPSpec->setText("");
    // 保温等级
    if (_common.iSpec() != nullptr)
    {
        ui.lineEditISpec->setText(QString::fromUtf8(_common.iSpec()->name().c_str()));
        ui.checkBoxInsu->setChecked(true);
    }
    else
    {
        ui.lineEditISpec->setText("");
        ui.checkBoxInsu->setChecked(false);
    }
    // 伴热等级
    if (_common.tSpec() != nullptr)
    {
        ui.lineEditTSpec->setText(QString::fromUtf8(_common.tSpec()->name().c_str()));
        ui.checkBoxTrac->setChecked(true);
    }
    else
    {
        ui.lineEditTSpec->setText("");
        ui.checkBoxTrac->setChecked(false);
    }

    _common.app().nodeTree().noticeCurrentNodeChanged() += {this, & PipeCreateDialog::onNodeTreeCurrentNodeChanged};
}
void PipeCreateDialog::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
    _common.app().nodeTree().noticeCurrentNodeChanged() -= {this, & PipeCreateDialog::onNodeTreeCurrentNodeChanged};
}

void PipeCreateDialog::slotPushButtonOkClicked()
{
    if (this->createPipeNode())
        this->accept();
}
void PipeCreateDialog::slotPushButtonCancelClicked()
{
    this->reject();
}

WD::WDNode::SharedPtr PipeCreateDialog::getParentNode() const
{
    auto pCurrNode = _common.app().nodeTree().currentNode();
    if (pCurrNode == nullptr)
        return nullptr;
    // 使用当前选中节点以及挂载规则获取管嘴可挂载的父节点
    return  _common.app().getBMDesign().findParentWithType(*pCurrNode, "PIPE");
}

bool PipeCreateDialog::createPipeNode() const
{
    WD::WDCore& app = _common.app();
    // 获取父节点
    auto pParentNode = this->getParentNode();
    if (pParentNode == nullptr)
    {
        WD_WARN_T("PipeCreateDialog", "ParentNodeInvalid");
        return false;
    }
    // 申领对象
    if (!WD::Core().getBMDesign().authorityMgr().checkAdd({ pParentNode }))
        return false;
    // 校验是否存在相同名称
    if (_nameHelpter.exists())
    {
        WD_WARN_T("PipeCreateDialog", "SameNameExists");
        return false;
    }
    std::string name = _nameHelpter.name();
    // 获取管线等级
    if (_common.pSpec() == nullptr)
    {
        //!TODO 提示管线等级无效
        assert(false);
        return false;
    }

    WD::WDNode::SharedPtr pNode = app.getBMDesign().create(pParentNode, "PIPE", name);
    if (pNode == nullptr)
    {
        WD_WARN_T("PipeCreateDialog", "CreateNodeFailed");
        return false;
    }

    // 准备管线业务数据
    if (!pNode->isType("PIPE"))
    {
        WD_WARN_T("PipeCreateDialog", "CreateNodeFailed");
        return false;
    }
    // 等级
    pNode->setAttribute("Pspec", WD::WDBMNodeRef(_common.pSpec()));
    // 保温等级
    if (ui.checkBoxInsu->isChecked())
        pNode->setAttribute("Ispec", WD::WDBMNodeRef(_common.iSpec()));
    // 伴热等级
    if (ui.checkBoxTrac->isChecked())
        pNode->setAttribute("Tspec", WD::WDBMNodeRef(_common.tSpec()));

    pNode->update();
    // 创建管线节点后默认设置为当前节点
    app.nodeTree().setCurrentNode(pNode);

    auto cmdNodeCreated = WD::WDBMBase::MakeCreatedCommand({ pNode });
    cmdNodeCreated->setNoticeAfterRedo([this, pNode](const WD::WDUndoCommand&)
    {
        // 创建管线节点后默认设置为当前节点
        _common.app().nodeTree().setCurrentNode(pNode);
    });
    auto cmdAddToScene = WD::WDBMBase::MakeSceneAddCommand({ pNode });
    cmdAddToScene->setNoticeAfterRedo([this, pNode](const WD::WDUndoCommand&)
    {
        // 需要重绘
        _common.app().needRepaint();
    });
    cmdAddToScene->setNoticeAfterUndo([this, pNode](const WD::WDUndoCommand&)
    {
        // 需要重绘
        _common.app().needRepaint();
    });

    app.undoStack().beginMarco("");
    app.undoStack().push(cmdNodeCreated);
    app.undoStack().push(cmdAddToScene);
    app.undoStack().endMarco();
    
    return true;
}

void PipeCreateDialog::onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
    , WD::WDNode::SharedPtr pPrevNode
    , WD::WDNodeTree& sender)
{
    WDUnused(pCurrNode);
    WDUnused(pPrevNode);
    WDUnused(sender);
    _nameHelpter.resetName();
}
void PipeCreateDialog::retranslateUi()
{
    WD::WDCxtTsBg("PipeCreateDialog");

    auto tr = [](const char* str) -> QString
        {
            return QString::fromUtf8(WD::WDCxtTs(str).c_str());
        };

    this->setWindowTitle(tr("Create Pipe"));
    ui.labelName->setText(tr("Name"));
    ui.labelPSpec->setText(tr("PSpec"));
    ui.labelISpec->setText(tr("ISpec"));
    ui.labelTSpec->setText(tr("TSpec"));

    ui.pushButtonOk->setText(tr("Ok"));
    ui.pushButtonCancel->setText(tr("Cancel"));

    WD::WDCxtTsEd();
}