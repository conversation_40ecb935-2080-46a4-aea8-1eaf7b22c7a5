#pragma once

#include <QDialog>
#include "ui_UiComNodePipeLine.h"
#include "core/WDCore.h"

#include "UiComCommonParamsWidget.h"
#include "../../ui.commonLibrary//ui.commonLib.custom/UiNodeNameHelpter.h"


class UiComNodePipeLine : public QDialog
{
    Q_OBJECT

public:
    UiComNodePipeLine(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~UiComNodePipeLine();
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;
public:
    /**
    * @brief 设置节点名称
    */
    inline void setName(const QString& nodeName) const
    {
        ui.lineEdit->setText(nodeName);
    }
    /**
    * @brief 清除界面输入的节点名称
    */
    inline void clearName() const
    {
        ui.lineEdit->clear();
    }
    /**
    * @brief 清除警告提示
    */
    inline void clearLabelContent() const
    {
        ui.warningLabel->clear();
    }
signals:
    /**
    * @brief 管线创建成功通知
    */
    void sigConfirmCreatePipeLineSucc();

private slots:
    /**
    * @brief 确定按钮 通知响应
    */
    void slotOkButtonClicked();
    /**
    * @brief 取消按钮 通知响应
    */
    void slotCancelButtonClicked();

private:
    // 根据模型树当前选中节点获取管线可挂载的父节点
    WD::WDNode::SharedPtr getParentNode() const;
    //模型树当前节点改变
    void onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
        , WD::WDNode::SharedPtr pPrevNode
        , WD::WDNodeTree& sender);
    // 创建管线节点
    void createPipeLineNode();

private:
    // 界面翻译
    void retranslateUi();

private:
    Ui::UiComNodePipeLine       ui;
    
    // 等级公用参数窗口
    UiComCommonParamsWidget*    _pLevelDataWidget;
    WD::WDCore&                 _core;
    // 节点名称助手
    UiNodeNameHelpter           _nameHelpter;
};
