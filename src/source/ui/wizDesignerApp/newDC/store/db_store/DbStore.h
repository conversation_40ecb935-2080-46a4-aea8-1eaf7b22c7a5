#pragma once

#include <string>
#include <memory>
#include <vector>
#include "../IStore.h"
#include "proto/node.pb.h"

namespace WD::store
{
    /**
     * @brief Database storage interface based on sqlite_orm
     * Implements the StoreInterface interface, providing storage functionality for node attributes and tree structures
     * Uses table sharding based on node ID modulo tableSize for better performance
     */
    class DbStore final : public IStore
    {
    public:
        /**
         * @brief Constructor
         * @param path Database file directory path
         * @param prefix Project identifier
         * @param tableSize Number of shards to create for node attribute tables
         */
        explicit DbStore(const std::string& path, const std::string& prefix, int tableSize);

        /**
         * @brief Destructor
         */
        ~DbStore() override;

        /**
         * @brief Update or insert a node attribute record
         * @param record Node attribute record to update or insert
         * @param uuid uuid of Node
         * @param type Node type (DESIGN or CATALOG)
         * @param from Where the record from
         * @return Whether the operation was successful
         */
        bool upsetNodeAttrsRecord(const design::NodeAttrsRecord& record, const std::string& uuid,
                                  NodeType type, StoreFrom from) override;

        /**
         * @brief Batch update or insert multiple node attribute records
         * @param recordsWithUuids Vector of pairs containing node attribute records and their UUIDs
         * @param type Node type (DESIGN or CATALOG)
         * @param from Where the records from
         * @return Number of successfully processed records
         */
        size_t batchUpsetNodeAttrsRecords(
            const std::vector<std::pair<design::NodeAttrsRecord, std::string>>& recordsWithUuids,
            NodeType type,
            StoreFrom from) override;

        /**
         * @brief Batch update or insert multiple node attribute records with options
         * @param recordsWithUuids Vector of pairs containing node attribute records and their UUIDs
         * @param type Node type (DESIGN or CATALOG)
         * @param from Where the records from
         * @param batchSize Size of each batch for processing (0 means process all at once)
         * @param stopOnError Whether to stop processing on first error
         * @return Pair of (successfully processed count, total processed count)
         */
        std::pair<size_t, size_t> batchUpsetNodeAttrsRecordsWithOptions(
            const std::vector<std::pair<design::NodeAttrsRecord, std::string>>& recordsWithUuids,
            NodeType type,
            StoreFrom from,
            size_t batchSize = 0,
            bool stopOnError = false);

        /**
         * @brief Delete a node
         * @param id Node ID
         * @param type Node type (DESIGN or CATALOG)
         * @return Whether the deletion was successful
         */
        bool deleteNode(int64_t id, NodeType type) override;

        /**
         * @brief Get a node attribute record
         * @param id Node ID
         * @param type Node type (DESIGN or CATALOG)
         * @return Node attribute record
         */
        design::NodeAttrsRecord getNodeAttrsRecord(int64_t id, NodeType type) override;

        /**
         * @brief Update or insert a node tree record
         * @param record Node tree record to update or insert
         * @param type Node type (DESIGN or CATALOG)
         * @param from Where the record from
         * @return Whether the operation was successful
         */
        bool upsetNodeTreeRecord(const design::NodeTreeRecord& record, NodeType type, StoreFrom from) override;

        /**
         * @brief Batch update or insert multiple node tree records
         * @param records Vector of node tree records to update or insert
         * @param type Node type (DESIGN or CATALOG)
         * @param from Where the records from
         * @return Number of successfully processed records
         */
        size_t batchUpsetNodeTreeRecords(const std::vector<design::NodeTreeRecord>& records, NodeType type, StoreFrom from) override;

        /**
         * @brief Batch update or insert multiple node tree records with options
         * @param records Vector of node tree records to update or insert
         * @param type Node type (DESIGN or CATALOG)
         * @param from Where the records from
         * @param batchSize Size of each batch for processing (0 means process all at once)
         * @param stopOnError Whether to stop processing on first error
         * @return Pair of (successfully processed count, total processed count)
         */
        std::pair<size_t, size_t> batchUpsetNodeTreeRecordsWithOptions(
            const std::vector<design::NodeTreeRecord>& records,
            NodeType type,
            StoreFrom from,
            size_t batchSize = 0,
            bool stopOnError = false);

        /**
         * @brief Delete a tree structure
         * @param parent Parent node ID
         * @param type Node type (DESIGN or CATALOG)
         * @return Whether the deletion was successful
         */
        bool deleteTree(int64_t parent, NodeType type) override;

        /**
         * @brief Get a node tree record
         * @param parent Parent node ID
         * @param type Node type (DESIGN or CATALOG)
         * @return Node tree record
         */
        design::NodeTreeRecord getNodeTreeRecord(int64_t parent, NodeType type) override;
        std::vector<int64_t> getNodeChildIds(int64_t parent, NodeType type) override;
        bool updateNodeCheckOutStatus(int64_t id, NodeType type, int checkOutStatus, const std::string& user) override;

        int64_t fetchMaxUpdateTime(NodeType type) override;
        int64_t fetchTraceId(int64_t id, NodeType type, bool isTree) override;

    private:
        class Impl;
        std::unique_ptr<Impl> pImpl;
    };
} // namespace WD::store
