//
// Created by everpan on 25-4-27.
//

#ifndef NODE_STORAGE_H
#define NODE_STORAGE_H

#include <string>
#include <vector>
#include "store/IStore.h"
#include "NodeMakeStorage.h"

namespace WD::store
{
    /**
     * @brief Storage manager for node attributes and tree structures
     *
     * This class manages the storage of node attributes in sharded tables based on node ID,
     * and tree structures in a single table. It provides methods to insert, update, delete,
     * and retrieve nodes and trees from the appropriate storage.
     */
    class NodeStorage
    {
    public:
        /**
         * @brief Constructor that initializes sharded storage for node attributes
         * @param path Base directory path for database files
         * @param moduleName Module name ("design" or "catalog")
         * @param projectId Project identifier
         * @param tableSize Number of shards to create for node attribute tables
         */
        NodeStorage(const std::string& path, const std::string& moduleName, const std::string& prefix, int tableSize);

        /**
         * @brief Get the storage for a specific node ID
         * @param id Node ID
         * @return Reference to the storage
         */
        NodeAttrStorage& getNodeAttrStorage(int64_t id);

        /**
         * @brief Get the tree storage
         * @return Reference to the tree storage
         */
        NodeTreeStorage& getTreeStorage() const;
        ProjectInfoStorage& getProjectInfoStorage() const;
        ProjectInfoRow getProjectInfo(const std::string& project) const;
        void replaceProjectInfo(const ProjectInfoRow& info) const;

        /**
         * @brief Insert a node into the appropriate sharded storage
         * @param node Node attributes to insert
         * @return true if successful, false otherwise
         */
        bool insertNode(const NodeAttrsRow& node);
        bool replaceNode(const NodeAttrsRow& node);

        /**
         * @brief Update a node in the appropriate sharded storage
         * @param node Node attributes to update
         * @return true if successful, false otherwise
         */
        bool updateNode(const NodeAttrsRow& node);

        /**
         * @brief Delete a node from the appropriate sharded storage
         * @param id Node ID to delete
         * @return true if successful, false otherwise
         */
        bool deleteNode(int64_t id);

        /**
         * @brief Get a node from the appropriate sharded storage
         * @param id Node ID to retrieve
         * @return Node attributes
         */
        NodeAttrsRow getNode(int64_t id);

        /**
         * @brief Insert a tree node
         * @param tree Tree node to insert
         * @return true if successful, false otherwise
         */
        bool insertTree(const NodeTreeRow& tree) const;
        bool replaceTree(const NodeTreeRow& tree) const;

        /**
         * @brief Update a tree node
         * @param tree Tree node to update
         * @return true if successful, false otherwise
         */
        bool updateTree(const NodeTreeRow& tree) const;

        /**
         * @brief Delete a tree node
         * @param id Tree node ID to delete
         * @return true if successful, false otherwise
         */
        bool deleteTree(int64_t id) const;

        /**
         * @brief Get a tree node
         * @param id Tree node ID to retrieve
         * @return Tree node
         */
        NodeTreeRow getTree(int64_t id) const;

        int64_t fetchMaxUpdateTime() const;

        int getTableCount() const
        {
            return _tableCount;
        }

    private:
        std::map<int, NodeAttrStorageUPtr> _nodeAttrStorages;
        NodeTreeStorageUPtr _treeStorage;
        ProjectInfoStorageUPtr _projectInfoStorage;
        int _tableCount = 10;
    };
}


#endif //NODE_STORAGE_H
