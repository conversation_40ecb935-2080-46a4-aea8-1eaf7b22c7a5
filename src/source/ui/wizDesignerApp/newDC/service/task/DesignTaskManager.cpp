//
// Created by everpan on 2025/6/13.
// 设计任务管理器实现
//

#include "DesignTaskManager.h"
#include <algorithm>
#include <sstream>
#include <iomanip>

namespace wiz
{
    DesignTaskManager::TaskGroupId DesignTaskManager::_nextGroupId{1};

    DesignTaskManager::DesignTaskManager()
        : _defaultThreadCount(std::thread::hardware_concurrency())
        , _maxQueueSize(0)
        , _shutdownTimeout(std::chrono::seconds(30))
        , _isStarted(false)
        , _lastStatsUpdate(std::chrono::steady_clock::now())
    {
    }

    DesignTaskManager::~DesignTaskManager()
    {
        DesignTaskManager::shutdown();
    }

    bool DesignTaskManager::start()
    {
        if (_isStarted.load()) {
            return false; // 已经启动
        }

        // 创建任务池
        _taskPool = std::make_unique<DesignTaskPool>(_defaultThreadCount, _maxQueueSize, _shutdownTimeout);
        
        // 设置任务状态回调
        _taskPool->setStatusCallback([this](DesignTask::TaskId taskId, TaskStatus status, TaskTerminationReason reason) {
            onTaskStatusChanged(taskId, status, reason);
        });

        // 启动任务池
        if (!_taskPool->start()) {
            _taskPool.reset();
            return false;
        }

        _isStarted.store(true);
        
        // 启动服务线程
        _serviceThread = std::thread(&DesignTaskManager::serviceThreadFunction, this);

        return true;
    }

    void DesignTaskManager::stop()
    {
        if (!_isStarted.load()) {
            return;
        }

        if (_taskPool) {
            _taskPool->pause();
        }
    }

    void DesignTaskManager::shutdown()
    {
        if (!_isStarted.load()) {
            return;
        }

        _isStarted.store(false);

        if (_taskPool) {
            _taskPool->stop(false); // 不等待任务完成
            _taskPool.reset();
        }

        if (_serviceThread.joinable()) {
            _serviceThread.join();
        }

        // 清理任务组
        std::lock_guard<std::mutex> lock(_groupsMutex);
        _taskGroups.clear();
    }

    void DesignTaskManager::setThreadCount(size_t threadCount)
    {
        _defaultThreadCount = threadCount > 0 ? threadCount : 1;
        
        if (_taskPool && _isStarted.load()) {
            // 重启任务池以应用新的线程数
            _taskPool->stop(true);
            _taskPool = std::make_unique<DesignTaskPool>(_defaultThreadCount, _maxQueueSize, _shutdownTimeout);
            _taskPool->setStatusCallback([this](DesignTask::TaskId taskId, TaskStatus status, TaskTerminationReason reason) {
                onTaskStatusChanged(taskId, status, reason);
            });
            _taskPool->start();
        }
    }

    void DesignTaskManager::setMaxQueueSize(size_t maxSize)
    {
        _maxQueueSize = maxSize;
        if (_taskPool) {
            _taskPool->setMaxQueueSize(maxSize);
        }
    }

    void DesignTaskManager::setShutdownTimeout(std::chrono::milliseconds timeout)
    {
        _shutdownTimeout = timeout;
        if (_taskPool) {
            _taskPool->setShutdownTimeout(timeout);
        }
    }

    DesignTask::TaskId DesignTaskManager::submitTask(const std::string& name,
                                                    DesignTask::TaskFunction function,
                                                    TaskPriority priority,
                                                    std::chrono::milliseconds timeout)
    {
        if (!_taskPool) {
            throw std::runtime_error("任务管理器未启动");
        }

        return _taskPool->submitTask(name, std::move(function), priority, timeout);
    }

    DesignTask::TaskId DesignTaskManager::submitTask(DesignTaskPtr task)
    {
        if (!_taskPool) {
            throw std::runtime_error("任务管理器未启动");
        }

        return _taskPool->submitTask(task);
    }

    bool DesignTaskManager::cancelTask(DesignTask::TaskId taskId, TaskTerminationReason reason)
    {
        if (!_taskPool) {
            return false;
        }

        return _taskPool->cancelTask(taskId, reason);
    }

    bool DesignTaskManager::pauseTask(DesignTask::TaskId taskId)
    {
        if (!_taskPool) {
            return false;
        }

        return _taskPool->pauseTask(taskId);
    }

    bool DesignTaskManager::resumeTask(DesignTask::TaskId taskId)
    {
        if (!_taskPool) {
            return false;
        }

        return _taskPool->resumeTask(taskId);
    }

    DesignTaskManager::TaskGroupId DesignTaskManager::createTaskGroup(const std::string& name, TaskPriority priority)
    {
        std::lock_guard<std::mutex> lock(_groupsMutex);
        
        TaskGroupId groupId = generateGroupId();
        auto group = std::make_unique<TaskGroup>(name, priority);
        _taskGroups[groupId] = std::move(group);
        
        return groupId;
    }

    bool DesignTaskManager::removeTaskGroup(TaskGroupId groupId, bool cancelTasks)
    {
        std::lock_guard<std::mutex> lock(_groupsMutex);
        
        auto it = _taskGroups.find(groupId);
        if (it == _taskGroups.end()) {
            return false;
        }

        if (cancelTasks && _taskPool) {
            // 取消组中的所有任务
            for (auto taskId : it->second->taskIds) {
                _taskPool->cancelTask(taskId, TaskTerminationReason::USER_CANCELLED);
            }
        }

        _taskGroups.erase(it);
        return true;
    }

    DesignTask::TaskId DesignTaskManager::submitTaskToGroup(TaskGroupId groupId,
                                                           const std::string& name,
                                                           DesignTask::TaskFunction function,
                                                           TaskPriority priority,
                                                           std::chrono::milliseconds timeout)
    {
        if (!_taskPool) {
            throw std::runtime_error("任务管理器未启动");
        }

        // 提交任务
        auto taskId = _taskPool->submitTask(name, std::move(function), priority, timeout);

        // 将任务添加到组中
        {
            std::lock_guard<std::mutex> lock(_groupsMutex);
            auto it = _taskGroups.find(groupId);
            if (it != _taskGroups.end()) {
                it->second->taskIds.push_back(taskId);
            }
        }

        return taskId;
    }

    bool DesignTaskManager::cancelTaskGroup(TaskGroupId groupId, TaskTerminationReason reason)
    {
        std::lock_guard<std::mutex> lock(_groupsMutex);
        
        auto it = _taskGroups.find(groupId);
        if (it == _taskGroups.end() || !_taskPool) {
            return false;
        }

        bool allCancelled = true;
        for (auto taskId : it->second->taskIds) {
            if (!_taskPool->cancelTask(taskId, reason)) {
                allCancelled = false;
            }
        }

        return allCancelled;
    }

    bool DesignTaskManager::pauseTaskGroup(TaskGroupId groupId)
    {
        std::lock_guard<std::mutex> lock(_groupsMutex);
        
        auto it = _taskGroups.find(groupId);
        if (it == _taskGroups.end() || !_taskPool) {
            return false;
        }

        bool allPaused = true;
        for (auto taskId : it->second->taskIds) {
            if (!_taskPool->pauseTask(taskId)) {
                allPaused = false;
            }
        }

        return allPaused;
    }

    bool DesignTaskManager::resumeTaskGroup(TaskGroupId groupId)
    {
        std::lock_guard<std::mutex> lock(_groupsMutex);
        
        auto it = _taskGroups.find(groupId);
        if (it == _taskGroups.end() || !_taskPool) {
            return false;
        }

        bool allResumed = true;
        for (auto taskId : it->second->taskIds) {
            if (!_taskPool->resumeTask(taskId)) {
                allResumed = false;
            }
        }

        return allResumed;
    }

    std::vector<DesignTask::TaskId> DesignTaskManager::submitBatchTasks(
        const std::vector<std::pair<std::string, DesignTask::TaskFunction>>& tasks,
        TaskPriority priority)
    {
        if (!_taskPool) {
            throw std::runtime_error("任务管理器未启动");
        }

        std::vector<DesignTask::TaskId> taskIds;
        taskIds.reserve(tasks.size());

        for (const auto& taskPair : tasks) {
            try {
                auto taskId = _taskPool->submitTask(taskPair.first, taskPair.second, priority);
                taskIds.push_back(taskId);
            }
            catch (const std::exception&) {
                // 如果某个任务提交失败，继续提交其他任务
                taskIds.push_back(0); // 使用0表示失败
            }
        }

        return taskIds;
    }

    bool DesignTaskManager::cancelAllTasks(TaskTerminationReason reason)
    {
        if (!_taskPool) {
            return false;
        }

        _taskPool->clearAllTasks();
        return true;
    }

    bool DesignTaskManager::pauseAllTasks()
    {
        if (!_taskPool) {
            return false;
        }

        _taskPool->pause();
        return true;
    }

    bool DesignTaskManager::resumeAllTasks()
    {
        if (!_taskPool) {
            return false;
        }

        _taskPool->resume();
        return true;
    }

    DesignTaskPtr DesignTaskManager::getTask(DesignTask::TaskId taskId) const
    {
        if (!_taskPool) {
            return nullptr;
        }

        return _taskPool->getTask(taskId);
    }

    std::vector<DesignTaskPtr> DesignTaskManager::getTasksByGroup(TaskGroupId groupId) const
    {
        std::vector<DesignTaskPtr> tasks;

        std::lock_guard<std::mutex> lock(_groupsMutex);
        auto it = _taskGroups.find(groupId);
        if (it == _taskGroups.end() || !_taskPool) {
            return tasks;
        }

        for (auto taskId : it->second->taskIds) {
            auto task = _taskPool->getTask(taskId);
            if (task) {
                tasks.push_back(task);
            }
        }

        return tasks;
    }

    std::vector<DesignTaskPtr> DesignTaskManager::getTasksByStatus(TaskStatus status) const
    {
        if (!_taskPool) {
            return {};
        }

        return _taskPool->getTasksByStatus(status);
    }

    std::vector<DesignTaskPtr> DesignTaskManager::getTasksByPriority(TaskPriority priority) const
    {
        if (!_taskPool) {
            return {};
        }

        auto allTasks = _taskPool->getAllTasks();
        std::vector<DesignTaskPtr> filteredTasks;

        for (const auto& task : allTasks) {
            if (task->getPriority() == priority) {
                filteredTasks.push_back(task);
            }
        }

        return filteredTasks;
    }

    TaskGroup* DesignTaskManager::getTaskGroup(TaskGroupId groupId) const
    {
        std::lock_guard<std::mutex> lock(_groupsMutex);
        auto it = _taskGroups.find(groupId);
        return (it != _taskGroups.end()) ? it->second.get() : nullptr;
    }

    std::vector<TaskGroup*> DesignTaskManager::getAllTaskGroups() const
    {
        std::lock_guard<std::mutex> lock(_groupsMutex);
        std::vector<TaskGroup*> groups;
        groups.reserve(_taskGroups.size());

        for (const auto& pair : _taskGroups) {
            groups.push_back(pair.second.get());
        }

        return groups;
    }

    bool DesignTaskManager::waitForTask(DesignTask::TaskId taskId, std::chrono::milliseconds timeout) const
    {
        if (!_taskPool) {
            return false;
        }

        return _taskPool->waitForTask(taskId, timeout);
    }

    bool DesignTaskManager::waitForTaskGroup(TaskGroupId groupId, std::chrono::milliseconds timeout) const
    {
        std::vector<DesignTask::TaskId> taskIds;

        {
            std::lock_guard<std::mutex> lock(_groupsMutex);
            auto it = _taskGroups.find(groupId);
            if (it == _taskGroups.end()) {
                return false;
            }
            taskIds = it->second->taskIds;
        }

        auto startTime = std::chrono::steady_clock::now();

        for (auto taskId : taskIds) {
            auto elapsed = std::chrono::steady_clock::now() - startTime;
            auto remaining = (timeout.count() > 0) ? timeout - std::chrono::duration_cast<std::chrono::milliseconds>(elapsed) : std::chrono::milliseconds(0);

            if (timeout.count() > 0 && remaining.count() <= 0) {
                return false;
            }

            if (!waitForTask(taskId, remaining)) {
                return false;
            }
        }

        return true;
    }

    void DesignTaskManager::waitForAllTasks() const
    {
        if (_taskPool) {
            _taskPool->waitForAllTasks();
        }
    }

    TaskStatistics DesignTaskManager::getStatistics() const
    {
        std::lock_guard<std::mutex> lock(_statsMutex);
        return _statistics;
    }

    void DesignTaskManager::resetStatistics()
    {
        std::lock_guard<std::mutex> lock(_statsMutex);
        _statistics = TaskStatistics{};
        _lastStatsUpdate = std::chrono::steady_clock::now();
    }

    size_t DesignTaskManager::getQueueSize() const
    {
        return _taskPool ? _taskPool->getQueueSize() : 0;
    }

    size_t DesignTaskManager::getActiveTaskCount() const
    {
        return _taskPool ? _taskPool->getActiveTaskCount() : 0;
    }

    size_t DesignTaskManager::getThreadCount() const
    {
        return _taskPool ? _taskPool->getThreadCount() : 0;
    }

    size_t DesignTaskManager::getTotalTaskGroups() const
    {
        std::lock_guard<std::mutex> lock(_groupsMutex);
        return _taskGroups.size();
    }

    bool DesignTaskManager::isRunning() const
    {
        return _taskPool ? _taskPool->isRunning() : false;
    }

    bool DesignTaskManager::isPaused() const
    {
        return _taskPool ? _taskPool->isPaused() : false;
    }

    bool DesignTaskManager::isStopped() const
    {
        return _taskPool ? _taskPool->isStopped() : true;
    }

    void DesignTaskManager::clearCompletedTasks()
    {
        if (_taskPool) {
            _taskPool->clearCompletedTasks();
        }
    }

    void DesignTaskManager::clearAllTasks()
    {
        if (_taskPool) {
            _taskPool->clearAllTasks();
        }
    }

    std::string DesignTaskManager::getTaskStatusReport() const
    {
        std::ostringstream oss;

        oss << "=== 任务状态报告 ===\n";
        oss << "队列大小: " << getQueueSize() << "\n";
        oss << "活跃任务: " << getActiveTaskCount() << "\n";
        oss << "线程数量: " << getThreadCount() << "\n";
        oss << "任务组数: " << getTotalTaskGroups() << "\n";

        if (_taskPool) {
            oss << "总提交任务: " << _taskPool->getTotalTasksSubmitted() << "\n";
            oss << "总完成任务: " << _taskPool->getTotalTasksCompleted() << "\n";
            oss << "总失败任务: " << _taskPool->getTotalTasksFailed() << "\n";
            oss << "总取消任务: " << _taskPool->getTotalTasksCancelled() << "\n";
        }

        auto stats = getStatistics();
        oss << "成功率: " << std::fixed << std::setprecision(2) << (stats.successRate * 100) << "%\n";
        oss << "平均执行时间: " << std::fixed << std::setprecision(3) << stats.averageExecutionTime << "秒\n";
        oss << "吞吐量: " << stats.throughput << " 任务/分钟\n";

        return oss.str();
    }

    std::string DesignTaskManager::getPerformanceReport() const
    {
        std::ostringstream oss;

        oss << "=== 性能报告 ===\n";

        auto stats = getStatistics();
        oss << "总任务数: " << stats.totalTasks << "\n";
        oss << "待处理: " << stats.pendingTasks << "\n";
        oss << "运行中: " << stats.runningTasks << "\n";
        oss << "已暂停: " << stats.pausedTasks << "\n";
        oss << "已完成: " << stats.completedTasks << "\n";
        oss << "已失败: " << stats.failedTasks << "\n";
        oss << "已取消: " << stats.cancelledTasks << "\n";

        oss << "\n性能指标:\n";
        oss << "成功率: " << std::fixed << std::setprecision(2) << (stats.successRate * 100) << "%\n";
        oss << "平均执行时间: " << std::fixed << std::setprecision(3) << stats.averageExecutionTime << "秒\n";
        oss << "吞吐量: " << stats.throughput << " 任务/分钟\n";

        return oss.str();
    }

    void DesignTaskManager::updateStatistics()
    {
        if (!_taskPool) {
            return;
        }

        std::lock_guard<std::mutex> lock(_statsMutex);

        // 获取各状态任务数量
        auto pendingTasks = getTasksByStatus(TaskStatus::PENDING);
        auto runningTasks = getTasksByStatus(TaskStatus::RUNNING);
        auto pausedTasks = getTasksByStatus(TaskStatus::PAUSED);
        auto completedTasks = getTasksByStatus(TaskStatus::COMPLETED);
        auto failedTasks = getTasksByStatus(TaskStatus::FAILED);
        auto cancelledTasks = getTasksByStatus(TaskStatus::CANCELLED);

        _statistics.pendingTasks = pendingTasks.size();
        _statistics.runningTasks = runningTasks.size();
        _statistics.pausedTasks = pausedTasks.size();
        _statistics.completedTasks = completedTasks.size();
        _statistics.failedTasks = failedTasks.size();
        _statistics.cancelledTasks = cancelledTasks.size();

        _statistics.totalTasks = _statistics.pendingTasks + _statistics.runningTasks +
                                _statistics.pausedTasks + _statistics.completedTasks +
                                _statistics.failedTasks + _statistics.cancelledTasks;

        // 计算性能指标
        _statistics.averageExecutionTime = calculateAverageExecutionTime();
        _statistics.successRate = calculateSuccessRate();
        _statistics.throughput = calculateThroughput();

        _lastStatsUpdate = std::chrono::steady_clock::now();
    }

    void DesignTaskManager::onTaskStatusChanged(DesignTask::TaskId taskId, TaskStatus status, TaskTerminationReason reason)
    {
        // 更新统计信息
        updateStatistics();

        // 调用外部回调
        if (_taskEventCallback) {
            auto task = getTask(taskId);
            std::string taskName = task ? task->getName() : "未知任务";
            _taskEventCallback(taskId, status, reason, taskName);
        }
    }

    DesignTaskManager::TaskGroupId DesignTaskManager::generateGroupId()
    {
        return _nextGroupId.fetch_add(1);
    }

    void DesignTaskManager::serviceThreadFunction()
    {
        while (_isStarted.load()) {
            // 定期更新统计信息
            updateStatistics();

            // 休眠一段时间
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    double DesignTaskManager::calculateAverageExecutionTime() const
    {
        if (!_taskPool) {
            return 0.0;
        }

        auto completedTasks = _taskPool->getTasksByStatus(TaskStatus::COMPLETED);
        if (completedTasks.empty()) {
            return 0.0;
        }

        double totalTime = 0.0;
        for (const auto& task : completedTasks) {
            auto elapsed = task->getElapsedTime();
            totalTime += elapsed.count() / 1000.0; // 转换为秒
        }

        return totalTime / completedTasks.size();
    }

    double DesignTaskManager::calculateSuccessRate() const
    {
        if (!_taskPool) {
            return 0.0;
        }

        size_t totalCompleted = _taskPool->getTotalTasksCompleted();
        size_t totalFailed = _taskPool->getTotalTasksFailed();
        size_t totalCancelled = _taskPool->getTotalTasksCancelled();

        size_t totalFinished = totalCompleted + totalFailed + totalCancelled;
        if (totalFinished == 0) {
            return 0.0;
        }

        return static_cast<double>(totalCompleted) / totalFinished;
    }

    size_t DesignTaskManager::calculateThroughput() const
    {
        if (!_taskPool) {
            return 0;
        }

        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(now - _lastStatsUpdate);

        if (elapsed.count() == 0) {
            return 0;
        }

        size_t completedSinceLastUpdate = _taskPool->getTotalTasksCompleted();
        return completedSinceLastUpdate / elapsed.count();
    }
}
