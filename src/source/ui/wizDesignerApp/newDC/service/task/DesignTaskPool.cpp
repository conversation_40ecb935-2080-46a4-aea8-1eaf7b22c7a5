//
// Created by everpan on 2025/6/13.
// 多线程任务池实现
//

#include "DesignTaskPool.h"
#include <algorithm>
#include <stdexcept>

namespace wiz
{
    DesignTaskPool::DesignTaskPool(size_t threadCount, 
                                   size_t maxQueueSize,
                                   std::chrono::milliseconds shutdownTimeout)
        : _threadCount(threadCount > 0 ? threadCount : 1)
        , _status(TaskPoolStatus::STOPPED)
        , _stopRequested(false)
        , _pauseRequested(false)
        , _totalTasksSubmitted(0)
        , _totalTasksCompleted(0)
        , _totalTasksFailed(0)
        , _totalTasksCancelled(0)
        , _maxQueueSize(maxQueueSize)
        , _shutdownTimeout(shutdownTimeout)
        , _maxCompletedTasksHistory(1000)
    {
    }

    DesignTaskPool::~DesignTaskPool()
    {
        stop(false); // 强制停止，不等待任务完成
    }

    bool DesignTaskPool::start()
    {
        std::lock_guard<std::mutex> lock(_queueMutex);
        
        if (_status.load() != TaskPoolStatus::STOPPED) {
            return false; // 已经在运行或正在关闭
        }
        
        _stopRequested.store(false);
        _pauseRequested.store(false);
        _status.store(TaskPoolStatus::RUNNING);
        
        // 创建工作线程
        _workers.reserve(_threadCount);
        for (size_t i = 0; i < _threadCount; ++i) {
            _workers.emplace_back(&DesignTaskPool::workerThread, this);
        }
        
        return true;
    }

    void DesignTaskPool::stop(bool waitForCompletion)
    {
        // 设置停止标志
        _stopRequested.store(true);
        _status.store(TaskPoolStatus::SHUTTING_DOWN);
        
        if (!waitForCompletion) {
            // 取消所有待处理的任务
            std::lock_guard<std::mutex> lock(_queueMutex);
            while (!_taskQueue.empty()) {
                auto task = _taskQueue.top();
                _taskQueue.pop();
                task->requestCancel(TaskTerminationReason::SYSTEM_SHUTDOWN);
                moveTaskToCompleted(task);
            }
            
            // 取消所有正在执行的任务
            std::lock_guard<std::mutex> activeLock(_activeTasksMutex);
            for (auto& pair : _activeTasks) {
                pair.second->requestCancel(TaskTerminationReason::SYSTEM_SHUTDOWN);
            }
        }
        
        // 唤醒所有工作线程
        _condition.notify_all();
        
        // 等待工作线程结束
        auto startTime = std::chrono::steady_clock::now();
        for (auto& worker : _workers) {
            if (worker.joinable()) {
                if (waitForCompletion) {
                    worker.join();
                } else {
                    // 带超时的等待
                    auto elapsed = std::chrono::steady_clock::now() - startTime;
                    if (elapsed < _shutdownTimeout) {
                        auto remaining = _shutdownTimeout - elapsed;
                        // 注意：std::thread::join() 不支持超时，这里简化处理
                        worker.join();
                    } else {
                        worker.detach(); // 超时后分离线程
                    }
                }
            }
        }
        
        _workers.clear();
        _status.store(TaskPoolStatus::STOPPED);
    }

    void DesignTaskPool::pause()
    {
        if (_status.load() == TaskPoolStatus::RUNNING) {
            _pauseRequested.store(true);
            _status.store(TaskPoolStatus::PAUSED);
        }
    }

    void DesignTaskPool::resume()
    {
        if (_status.load() == TaskPoolStatus::PAUSED) {
            _pauseRequested.store(false);
            _status.store(TaskPoolStatus::RUNNING);
            _condition.notify_all();
        }
    }

    DesignTask::TaskId DesignTaskPool::submitTask(DesignTaskPtr task)
    {
        if (!task) {
            throw std::invalid_argument("任务不能为空");
        }
        
        if (!canAcceptNewTask()) {
            throw std::runtime_error("任务池无法接受新任务");
        }
        
        // 设置任务状态回调
        task->setStatusCallback([this, task](TaskStatus status, TaskTerminationReason reason) {
            onTaskStatusChanged(task, status, reason);
        });
        
        {
            std::lock_guard<std::mutex> lock(_queueMutex);
            
            // 检查队列大小限制
            if (_maxQueueSize > 0 && _taskQueue.size() >= _maxQueueSize) {
                throw std::runtime_error("任务队列已满");
            }
            
            _taskQueue.push(task);
            _totalTasksSubmitted.fetch_add(1);
        }
        
        _condition.notify_one();
        return task->getId();
    }

    DesignTask::TaskId DesignTaskPool::submitTask(const std::string& name,
                                                 DesignTask::TaskFunction function,
                                                 TaskPriority priority,
                                                 std::chrono::milliseconds timeout)
    {
        auto task = std::make_shared<DesignTask>(name, std::move(function), priority, timeout);
        return submitTask(task);
    }

    bool DesignTaskPool::cancelTask(DesignTask::TaskId taskId, TaskTerminationReason reason)
    {
        // 首先检查待处理队列
        {
            std::lock_guard<std::mutex> lock(_queueMutex);
            TaskQueue tempQueue;
            bool found = false;
            
            while (!_taskQueue.empty()) {
                auto task = _taskQueue.top();
                _taskQueue.pop();
                
                if (task->getId() == taskId) {
                    task->requestCancel(reason);
                    moveTaskToCompleted(task);
                    found = true;
                } else {
                    tempQueue.push(task);
                }
            }
            
            _taskQueue = std::move(tempQueue);
            if (found) return true;
        }
        
        // 检查正在执行的任务
        {
            std::lock_guard<std::mutex> lock(_activeTasksMutex);
            auto it = _activeTasks.find(taskId);
            if (it != _activeTasks.end()) {
                it->second->requestCancel(reason);
                return true;
            }
        }
        
        return false;
    }

    bool DesignTaskPool::pauseTask(DesignTask::TaskId taskId)
    {
        std::lock_guard<std::mutex> lock(_activeTasksMutex);
        auto it = _activeTasks.find(taskId);
        if (it != _activeTasks.end()) {
            it->second->requestPause();
            return true;
        }
        return false;
    }

    bool DesignTaskPool::resumeTask(DesignTask::TaskId taskId)
    {
        std::lock_guard<std::mutex> lock(_activeTasksMutex);
        auto it = _activeTasks.find(taskId);
        if (it != _activeTasks.end()) {
            it->second->requestResume();
            return true;
        }
        return false;
    }

    DesignTaskPtr DesignTaskPool::getTask(DesignTask::TaskId taskId) const
    {
        // 检查活跃任务
        {
            std::lock_guard<std::mutex> lock(_activeTasksMutex);
            auto it = _activeTasks.find(taskId);
            if (it != _activeTasks.end()) {
                return it->second;
            }
        }
        
        // 检查已完成任务
        {
            std::lock_guard<std::mutex> lock(_completedTasksMutex);
            auto it = _completedTasks.find(taskId);
            if (it != _completedTasks.end()) {
                return it->second;
            }
        }
        
        // 检查待处理队列
        {
            std::lock_guard<std::mutex> lock(_queueMutex);
            TaskQueue tempQueue = _taskQueue;
            while (!tempQueue.empty()) {
                auto task = tempQueue.top();
                tempQueue.pop();
                if (task->getId() == taskId) {
                    return task;
                }
            }
        }
        
        return nullptr;
    }

    std::vector<DesignTaskPtr> DesignTaskPool::getActiveTasks() const
    {
        std::lock_guard<std::mutex> lock(_activeTasksMutex);
        std::vector<DesignTaskPtr> tasks;
        tasks.reserve(_activeTasks.size());
        
        for (const auto& pair : _activeTasks) {
            tasks.push_back(pair.second);
        }
        
        return tasks;
    }

    std::vector<DesignTaskPtr> DesignTaskPool::getPendingTasks() const
    {
        std::lock_guard<std::mutex> lock(_queueMutex);
        std::vector<DesignTaskPtr> tasks;
        
        TaskQueue tempQueue = _taskQueue;
        while (!tempQueue.empty()) {
            tasks.push_back(tempQueue.top());
            tempQueue.pop();
        }
        
        return tasks;
    }

    size_t DesignTaskPool::getQueueSize() const
    {
        std::lock_guard<std::mutex> lock(_queueMutex);
        return _taskQueue.size();
    }

    size_t DesignTaskPool::getActiveTaskCount() const
    {
        std::lock_guard<std::mutex> lock(_activeTasksMutex);
        return _activeTasks.size();
    }

    bool DesignTaskPool::waitForTask(DesignTask::TaskId taskId, std::chrono::milliseconds timeout) const
    {
        auto startTime = std::chrono::steady_clock::now();

        while (true) {
            auto task = getTask(taskId);
            if (!task || task->isCompleted()) {
                return true;
            }

            if (timeout.count() > 0) {
                auto elapsed = std::chrono::steady_clock::now() - startTime;
                if (elapsed >= timeout) {
                    return false;
                }
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }

    void DesignTaskPool::waitForAllTasks() const
    {
        while (true) {
            bool hasActiveTasks = false;
            bool hasPendingTasks = false;

            {
                std::lock_guard<std::mutex> activeLock(_activeTasksMutex);
                hasActiveTasks = !_activeTasks.empty();
            }

            {
                std::lock_guard<std::mutex> queueLock(_queueMutex);
                hasPendingTasks = !_taskQueue.empty();
            }

            if (!hasActiveTasks && !hasPendingTasks) {
                break;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }

    std::vector<DesignTaskPtr> DesignTaskPool::getAllTasks() const
    {
        std::vector<DesignTaskPtr> allTasks;

        // 获取活跃任务
        auto activeTasks = getActiveTasks();
        allTasks.insert(allTasks.end(), activeTasks.begin(), activeTasks.end());

        // 获取待处理任务
        auto pendingTasks = getPendingTasks();
        allTasks.insert(allTasks.end(), pendingTasks.begin(), pendingTasks.end());

        // 获取已完成任务
        {
            std::lock_guard<std::mutex> lock(_completedTasksMutex);
            for (const auto& pair : _completedTasks) {
                allTasks.push_back(pair.second);
            }
        }

        return allTasks;
    }

    std::vector<DesignTaskPtr> DesignTaskPool::getTasksByStatus(TaskStatus status) const
    {
        auto allTasks = getAllTasks();
        std::vector<DesignTaskPtr> filteredTasks;

        for (const auto& task : allTasks) {
            if (task->getStatus() == status) {
                filteredTasks.push_back(task);
            }
        }

        return filteredTasks;
    }

    void DesignTaskPool::clearCompletedTasks()
    {
        std::lock_guard<std::mutex> lock(_completedTasksMutex);
        _completedTasks.clear();
    }

    void DesignTaskPool::clearAllTasks()
    {
        // 清空待处理队列
        {
            std::lock_guard<std::mutex> lock(_queueMutex);
            while (!_taskQueue.empty()) {
                auto task = _taskQueue.top();
                _taskQueue.pop();
                task->requestCancel(TaskTerminationReason::SYSTEM_SHUTDOWN);
            }
        }

        // 取消所有活跃任务
        {
            std::lock_guard<std::mutex> lock(_activeTasksMutex);
            for (auto& pair : _activeTasks) {
                pair.second->requestCancel(TaskTerminationReason::SYSTEM_SHUTDOWN);
            }
        }

        // 清空已完成任务
        clearCompletedTasks();
    }

    void DesignTaskPool::workerThread()
    {
        while (!shouldWorkerStop()) {
            DesignTaskPtr task;

            // 获取任务
            {
                std::unique_lock<std::mutex> lock(_queueMutex);

                // 等待任务或停止信号
                _condition.wait(lock, [this] {
                    return shouldWorkerStop() || hasTasksToProcess();
                });

                if (shouldWorkerStop()) {
                    break;
                }

                if (!_taskQueue.empty()) {
                    task = _taskQueue.top();
                    _taskQueue.pop();
                }
            }

            if (task) {
                executeTask(task);
            }
        }
    }

    void DesignTaskPool::executeTask(DesignTaskPtr task)
    {
        if (!task) return;

        // 将任务添加到活跃任务列表
        {
            std::lock_guard<std::mutex> lock(_activeTasksMutex);
            _activeTasks[task->getId()] = task;
        }

        try {
            // 执行任务
            task->execute();
        }
        catch (const std::exception& e) {
            task->setLastError(e.what());
        }
        catch (...) {
            task->setLastError("未知异常");
        }

        // 从活跃任务列表中移除
        {
            std::lock_guard<std::mutex> lock(_activeTasksMutex);
            _activeTasks.erase(task->getId());
        }

        // 移动到已完成任务列表
        moveTaskToCompleted(task);
    }

    void DesignTaskPool::onTaskStatusChanged(DesignTaskPtr task, TaskStatus status, TaskTerminationReason reason)
    {
        // 更新统计信息
        switch (status) {
            case TaskStatus::COMPLETED:
                _totalTasksCompleted.fetch_add(1);
                break;
            case TaskStatus::FAILED:
                _totalTasksFailed.fetch_add(1);
                break;
            case TaskStatus::CANCELLED:
                _totalTasksCancelled.fetch_add(1);
                break;
            default:
                break;
        }

        // 调用外部状态回调
        if (_statusCallback) {
            _statusCallback(task->getId(), status, reason);
        }
    }

    bool DesignTaskPool::canAcceptNewTask() const
    {
        TaskPoolStatus status = _status.load();
        return status == TaskPoolStatus::RUNNING || status == TaskPoolStatus::PAUSED;
    }

    void DesignTaskPool::moveTaskToCompleted(DesignTaskPtr task)
    {
        std::lock_guard<std::mutex> lock(_completedTasksMutex);
        _completedTasks[task->getId()] = task;

        // 清理过多的已完成任务历史
        cleanupCompletedTasks();
    }

    void DesignTaskPool::cleanupCompletedTasks()
    {
        if (_completedTasks.size() > _maxCompletedTasksHistory) {
            // 找到最旧的任务并删除
            auto oldestTime = std::chrono::steady_clock::now();
            DesignTask::TaskId oldestTaskId = 0;

            for (const auto& pair : _completedTasks) {
                auto taskEndTime = pair.second->getEndTime();
                if (taskEndTime < oldestTime) {
                    oldestTime = taskEndTime;
                    oldestTaskId = pair.first;
                }
            }

            if (oldestTaskId != 0) {
                _completedTasks.erase(oldestTaskId);
            }
        }
    }

    bool DesignTaskPool::shouldWorkerStop() const
    {
        return _stopRequested.load();
    }

    bool DesignTaskPool::hasTasksToProcess() const
    {
        if (_pauseRequested.load()) {
            return false;
        }
        return !_taskQueue.empty();
    }
}
}
