//
// Created by AI Assistant on 2025-06-15.
// DbStore 批量树结构插入基准测试
//

#include <benchmark/benchmark.h>
#include <glog/logging.h>
#include <filesystem>
#include <memory>
#include <random>
#include "store/db_store/DbStore.h"
#include "proto/def.h"
#include "proto/node.pb.h"

using namespace WD::store;
using namespace design;

class DbStoreBatchTreeBenchmarkFixture : public benchmark::Fixture
{
public:
    void SetUp(const ::benchmark::State& state) override
    {
        // 创建临时测试目录
        testPath = "/tmp/dbstore_batch_tree_benchmark_" + std::to_string(std::time(nullptr));
        std::filesystem::create_directories(testPath);
        
        // 初始化 DbStore
        dbStore = std::make_unique<DbStore>(testPath, "batch_tree_benchmark", 10);
        
        // 初始化随机数生成器
        rng.seed(42);
        
        LOG(INFO) << "DbStore 批量树结构基准测试环境初始化完成，路径: " << testPath;
    }

    void TearDown(const ::benchmark::State& state) override
    {
        // 清理测试数据
        dbStore.reset();
        std::filesystem::remove_all(testPath);
        
        LOG(INFO) << "DbStore 批量树结构基准测试环境清理完成";
    }

    // 创建测试用的树结构记录
    NodeTreeRecord createBenchmarkNodeTreeRecord(int64_t parentId, const std::vector<int64_t>& children)
    {
        NodeTreeRecord record;
        record.set_id(parentId);
        record.set_traceid(parentId * 200 + rng() % 1000);
        record.set_additionaljson("{\"benchmark\": \"tree_data\", \"id\": " + std::to_string(parentId) + "}");
        
        // 设置子节点列表
        auto* childrenVec = record.mutable_children();
        for (int64_t childId : children)
        {
            childrenVec->add_vec_int64(childId);
        }
        
        // 设置附加信息
        auto* additionalInfo = record.mutable_additionalinfo();
        additionalInfo->set_user("benchmark_user_" + std::to_string(rng() % 100));
        additionalInfo->set_time(std::time(nullptr) + rng() % 86400);
        additionalInfo->set_checkout(rng() % 2);
        additionalInfo->set_status(rng() % 5);
        
        return record;
    }

    // 创建测试树结构数据
    std::vector<NodeTreeRecord> createBenchmarkTreeData(size_t count, int64_t startId = 100000)
    {
        std::vector<NodeTreeRecord> records;
        records.reserve(count);
        
        for (size_t i = 0; i < count; ++i)
        {
            int64_t parentId = startId + i;
            std::vector<int64_t> children;
            
            // 为每个父节点创建随机数量的子节点（1-10个）
            size_t childCount = 1 + (rng() % 10);
            for (size_t j = 0; j < childCount; ++j)
            {
                children.push_back(parentId * 100 + j + 1);
            }
            
            records.push_back(createBenchmarkNodeTreeRecord(parentId, children));
        }
        
        return records;
    }

protected:
    std::string testPath;
    std::unique_ptr<DbStore> dbStore;
    std::mt19937 rng;
};

// 基准测试：单条插入 vs 批量插入性能对比
BENCHMARK_DEFINE_F(DbStoreBatchTreeBenchmarkFixture, 单条插入性能)(benchmark::State& state)
{
    const int64_t recordCount = state.range(0);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        auto testRecords = createBenchmarkTreeData(recordCount);
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "single_insert_benchmark", 10);
        state.ResumeTiming();
        
        size_t successCount = 0;
        for (const auto& record : testRecords)
        {
            if (dbStore->upsetNodeTreeRecord(record, DESIGN, IStore::FromServer))
            {
                successCount++;
            }
        }
        
        if (successCount != testRecords.size())
        {
            state.SkipWithError("单条插入操作失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * recordCount);
    state.SetBytesProcessed(state.iterations() * recordCount * sizeof(NodeTreeRecord));
}

BENCHMARK_REGISTER_F(DbStoreBatchTreeBenchmarkFixture, 单条插入性能)
    ->Range(100, 5000)
    ->Unit(benchmark::kMillisecond)
    ->Complexity();

// 基准测试：批量插入性能
BENCHMARK_DEFINE_F(DbStoreBatchTreeBenchmarkFixture, 批量插入性能)(benchmark::State& state)
{
    const int64_t recordCount = state.range(0);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        auto testRecords = createBenchmarkTreeData(recordCount);
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "batch_insert_benchmark", 10);
        state.ResumeTiming();
        
        size_t successCount = dbStore->batchUpsetNodeTreeRecords(testRecords, DESIGN, IStore::FromServer);
        
        if (successCount != testRecords.size())
        {
            state.SkipWithError("批量插入操作失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * recordCount);
    state.SetBytesProcessed(state.iterations() * recordCount * sizeof(NodeTreeRecord));
}

BENCHMARK_REGISTER_F(DbStoreBatchTreeBenchmarkFixture, 批量插入性能)
    ->Range(100, 5000)
    ->Unit(benchmark::kMillisecond)
    ->Complexity();

// 基准测试：不同批量大小的性能影响
BENCHMARK_DEFINE_F(DbStoreBatchTreeBenchmarkFixture, 不同批量大小性能)(benchmark::State& state)
{
    const int64_t totalRecords = state.range(0);
    const int64_t batchSize = state.range(1);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        auto testRecords = createBenchmarkTreeData(totalRecords);
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "batch_size_benchmark", 10);
        state.ResumeTiming();
        
        auto [successCount, processedCount] = dbStore->batchUpsetNodeTreeRecordsWithOptions(
            testRecords, DESIGN, IStore::FromServer, batchSize, false);
        
        if (successCount != testRecords.size())
        {
            state.SkipWithError("批量插入操作失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * totalRecords);
    state.SetBytesProcessed(state.iterations() * totalRecords * sizeof(NodeTreeRecord));
}

BENCHMARK_REGISTER_F(DbStoreBatchTreeBenchmarkFixture, 不同批量大小性能)
    ->Args({2000, 50})   // 2000条记录，批量大小50
    ->Args({2000, 100})  // 2000条记录，批量大小100
    ->Args({2000, 200})  // 2000条记录，批量大小200
    ->Args({2000, 500})  // 2000条记录，批量大小500
    ->Args({2000, 0})    // 2000条记录，一次性处理
    ->Unit(benchmark::kMillisecond);

// 基准测试：大数据量批量插入
BENCHMARK_DEFINE_F(DbStoreBatchTreeBenchmarkFixture, 大数据量批量插入)(benchmark::State& state)
{
    const int64_t recordCount = state.range(0);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        // 创建包含更多子节点的复杂树结构
        std::vector<NodeTreeRecord> testRecords;
        testRecords.reserve(recordCount);
        
        for (int64_t i = 0; i < recordCount; ++i)
        {
            int64_t parentId = 200000 + i;
            std::vector<int64_t> children;
            
            // 创建更多子节点（10-50个）
            size_t childCount = 10 + (rng() % 41);
            for (size_t j = 0; j < childCount; ++j)
            {
                children.push_back(parentId * 1000 + j + 1);
            }
            
            testRecords.push_back(createBenchmarkNodeTreeRecord(parentId, children));
        }
        
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "large_data_benchmark", 10);
        state.ResumeTiming();
        
        size_t successCount = dbStore->batchUpsetNodeTreeRecords(testRecords, DESIGN, IStore::FromServer);
        
        if (successCount != testRecords.size())
        {
            state.SkipWithError("大数据量批量插入失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * recordCount);
    state.SetBytesProcessed(state.iterations() * recordCount * sizeof(NodeTreeRecord) * 30); // 估算大小
}

BENCHMARK_REGISTER_F(DbStoreBatchTreeBenchmarkFixture, 大数据量批量插入)
    ->Range(100, 2000)
    ->Unit(benchmark::kMillisecond)
    ->Complexity();

// 基准测试：混合节点类型批量插入
BENCHMARK_DEFINE_F(DbStoreBatchTreeBenchmarkFixture, 混合节点类型批量插入)(benchmark::State& state)
{
    const int64_t recordCount = state.range(0);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        auto designRecords = createBenchmarkTreeData(recordCount / 2, 300000);
        auto catalogRecords = createBenchmarkTreeData(recordCount / 2, 400000);
        
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "mixed_type_benchmark", 10);
        state.ResumeTiming();
        
        size_t designSuccess = dbStore->batchUpsetNodeTreeRecords(designRecords, DESIGN, IStore::FromServer);
        size_t catalogSuccess = dbStore->batchUpsetNodeTreeRecords(catalogRecords, CATALOG, IStore::FromServer);
        
        if (designSuccess != designRecords.size() || catalogSuccess != catalogRecords.size())
        {
            state.SkipWithError("混合类型批量插入失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * recordCount);
    state.SetBytesProcessed(state.iterations() * recordCount * sizeof(NodeTreeRecord));
}

BENCHMARK_REGISTER_F(DbStoreBatchTreeBenchmarkFixture, 混合节点类型批量插入)
    ->Range(200, 2000)
    ->Unit(benchmark::kMillisecond)
    ->Complexity();

// 基准测试：内存使用压力测试
BENCHMARK_DEFINE_F(DbStoreBatchTreeBenchmarkFixture, 内存使用压力测试)(benchmark::State& state)
{
    const int64_t recordCount = state.range(0);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        // 创建包含大量子节点的记录
        std::vector<NodeTreeRecord> testRecords;
        testRecords.reserve(recordCount);
        
        for (int64_t i = 0; i < recordCount; ++i)
        {
            int64_t parentId = 500000 + i;
            std::vector<int64_t> children;
            
            // 每个节点100个子节点
            for (size_t j = 0; j < 100; ++j)
            {
                children.push_back(parentId * 10000 + j + 1);
            }
            
            testRecords.push_back(createBenchmarkNodeTreeRecord(parentId, children));
        }
        
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "memory_pressure_benchmark", 10);
        state.ResumeTiming();
        
        // 使用分批处理来控制内存使用
        auto [successCount, processedCount] = dbStore->batchUpsetNodeTreeRecordsWithOptions(
            testRecords, DESIGN, IStore::FromServer, 50, false);
        
        if (successCount != testRecords.size())
        {
            state.SkipWithError("内存压力测试失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * recordCount);
    state.SetBytesProcessed(state.iterations() * recordCount * 100 * sizeof(int64_t)); // 估算内存使用
}

BENCHMARK_REGISTER_F(DbStoreBatchTreeBenchmarkFixture, 内存使用压力测试)
    ->Range(50, 500)
    ->Unit(benchmark::kMillisecond)
    ->Complexity();

