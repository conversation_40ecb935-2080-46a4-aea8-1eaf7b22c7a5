//
// Created by AI Assistant on 2025-06-15.
// DbStore 批量节点属性插入基准测试
//

#include <benchmark/benchmark.h>
#include <glog/logging.h>
#include <filesystem>
#include <memory>
#include <random>
#include "store/db_store/DbStore.h"
#include "proto/def.h"
#include "proto/node.pb.h"

using namespace WD::store;
using namespace design;

class DbStoreBatchAttrsBenchmarkFixture : public benchmark::Fixture
{
public:
    void SetUp(const ::benchmark::State& state) override
    {
        // 创建临时测试目录
        testPath = "/tmp/dbstore_batch_attrs_benchmark_" + std::to_string(std::time(nullptr));
        std::filesystem::create_directories(testPath);
        
        // 初始化 DbStore
        dbStore = std::make_unique<DbStore>(testPath, "batch_attrs_benchmark", 10);
        
        // 初始化随机数生成器
        rng.seed(42);
        
        LOG(INFO) << "DbStore 批量节点属性基准测试环境初始化完成，路径: " << testPath;
    }

    void TearDown(const ::benchmark::State& state) override
    {
        // 清理测试数据
        dbStore.reset();
        std::filesystem::remove_all(testPath);
        
        LOG(INFO) << "DbStore 批量节点属性基准测试环境清理完成";
    }

    // 创建测试用的节点属性记录
    std::pair<NodeAttrsRecord, std::string> createBenchmarkNodeAttrsRecord(int64_t id, const std::string& name, NodeType type)
    {
        NodeAttrsRecord record;
        record.set_id(id);
        record.set_name(name);
        record.set_type(type);
        record.set_traceid(id * 100 + rng() % 1000);
        record.set_additionaljson("{\"benchmark\": \"attrs_data\", \"id\": " + std::to_string(id) + "}");
        
        // 设置附加信息
        auto* additionalInfo = record.mutable_additionalinfo();
        additionalInfo->set_user("benchmark_attrs_user_" + std::to_string(rng() % 100));
        additionalInfo->set_time(std::time(nullptr) + rng() % 86400);
        additionalInfo->set_checkout(rng() % 2);
        additionalInfo->set_status(rng() % 5);
        
        // 设置节点属性 - 创建更复杂的属性数据
        auto* attrs = record.mutable_attrs();
        auto& attrMap = *attrs->mutable_attr_map();
        
        // 添加多种类型的属性
        for (int i = 0; i < 20; ++i)
        {
            int64_t attrKey = 1000 + i;
            NodeBaseValue value;
            
            switch (i % 4)
            {
                case 0:
                    value.set_val_string("基准测试属性字符串_" + std::to_string(id) + "_" + std::to_string(i));
                    break;
                case 1:
                    value.set_val_int(rng() % 1000000);
                    break;
                case 2:
                    value.set_val_double(static_cast<double>(rng()) / RAND_MAX * 1000.0);
                    break;
                case 3:
                    value.set_val_bool(rng() % 2 == 0);
                    break;
            }
            
            attrMap[attrKey] = value;
        }
        
        // 生成UUID
        std::string uuid = "benchmark-attrs-uuid-" + std::to_string(id) + "-" + std::to_string(rng() % 100000);
        
        return {record, uuid};
    }

    // 创建测试节点属性数据
    std::vector<std::pair<NodeAttrsRecord, std::string>> createBenchmarkAttrsData(size_t count, int64_t startId = 100000)
    {
        std::vector<std::pair<NodeAttrsRecord, std::string>> records;
        records.reserve(count);
        
        for (size_t i = 0; i < count; ++i)
        {
            int64_t nodeId = startId + i;
            std::string nodeName = "基准测试节点_" + std::to_string(nodeId);
            records.push_back(createBenchmarkNodeAttrsRecord(nodeId, nodeName, DESIGN));
        }
        
        return records;
    }

protected:
    std::string testPath;
    std::unique_ptr<DbStore> dbStore;
    std::mt19937 rng;
};

// 基准测试：单条插入 vs 批量插入性能对比
BENCHMARK_DEFINE_F(DbStoreBatchAttrsBenchmarkFixture, 单条插入性能)(benchmark::State& state)
{
    const int64_t recordCount = state.range(0);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        auto testRecords = createBenchmarkAttrsData(recordCount);
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "single_attrs_benchmark", 10);
        state.ResumeTiming();
        
        size_t successCount = 0;
        for (const auto& [record, uuid] : testRecords)
        {
            if (dbStore->upsetNodeAttrsRecord(record, uuid, DESIGN, IStore::FromServer))
            {
                successCount++;
            }
        }
        
        if (successCount != testRecords.size())
        {
            state.SkipWithError("单条插入操作失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * recordCount);
    state.SetBytesProcessed(state.iterations() * recordCount * sizeof(NodeAttrsRecord));
}

BENCHMARK_REGISTER_F(DbStoreBatchAttrsBenchmarkFixture, 单条插入性能)
    ->Range(100, 5000)
    ->Unit(benchmark::kMillisecond)
    ->Complexity();

// 基准测试：批量插入性能
BENCHMARK_DEFINE_F(DbStoreBatchAttrsBenchmarkFixture, 批量插入性能)(benchmark::State& state)
{
    const int64_t recordCount = state.range(0);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        auto testRecords = createBenchmarkAttrsData(recordCount);
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "batch_attrs_benchmark", 10);
        state.ResumeTiming();
        
        size_t successCount = dbStore->batchUpsetNodeAttrsRecords(testRecords, DESIGN, IStore::FromServer);
        
        if (successCount != testRecords.size())
        {
            state.SkipWithError("批量插入操作失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * recordCount);
    state.SetBytesProcessed(state.iterations() * recordCount * sizeof(NodeAttrsRecord));
}

BENCHMARK_REGISTER_F(DbStoreBatchAttrsBenchmarkFixture, 批量插入性能)
    ->Range(100, 5000)
    ->Unit(benchmark::kMillisecond)
    ->Complexity();

// 基准测试：不同批量大小的性能影响
BENCHMARK_DEFINE_F(DbStoreBatchAttrsBenchmarkFixture, 不同批量大小性能)(benchmark::State& state)
{
    const int64_t totalRecords = state.range(0);
    const int64_t batchSize = state.range(1);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        auto testRecords = createBenchmarkAttrsData(totalRecords);
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "batch_size_attrs_benchmark", 10);
        state.ResumeTiming();
        
        auto [successCount, processedCount] = dbStore->batchUpsetNodeAttrsRecordsWithOptions(
            testRecords, DESIGN, IStore::FromServer, batchSize, false);
        
        if (successCount != testRecords.size())
        {
            state.SkipWithError("批量插入操作失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * totalRecords);
    state.SetBytesProcessed(state.iterations() * totalRecords * sizeof(NodeAttrsRecord));
}

BENCHMARK_REGISTER_F(DbStoreBatchAttrsBenchmarkFixture, 不同批量大小性能)
    ->Args({2000, 50})   // 2000条记录，批量大小50
    ->Args({2000, 100})  // 2000条记录，批量大小100
    ->Args({2000, 200})  // 2000条记录，批量大小200
    ->Args({2000, 500})  // 2000条记录，批量大小500
    ->Args({2000, 0})    // 2000条记录，一次性处理
    ->Unit(benchmark::kMillisecond);

// 基准测试：分片性能影响
BENCHMARK_DEFINE_F(DbStoreBatchAttrsBenchmarkFixture, 分片性能影响)(benchmark::State& state)
{
    const int64_t recordCount = state.range(0);
    const int shardCount = state.range(1);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        // 创建跨多个分片的数据
        std::vector<std::pair<NodeAttrsRecord, std::string>> testRecords;
        testRecords.reserve(recordCount);
        
        for (int64_t i = 0; i < recordCount; ++i)
        {
            // 确保数据分布在不同分片中
            int64_t nodeId = 200000 + i * shardCount + (i % shardCount);
            std::string nodeName = "分片测试节点_" + std::to_string(nodeId);
            testRecords.push_back(createBenchmarkNodeAttrsRecord(nodeId, nodeName, DESIGN));
        }
        
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "shard_attrs_benchmark", shardCount);
        state.ResumeTiming();
        
        size_t successCount = dbStore->batchUpsetNodeAttrsRecords(testRecords, DESIGN, IStore::FromServer);
        
        if (successCount != testRecords.size())
        {
            state.SkipWithError("分片测试插入失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * recordCount);
    state.SetBytesProcessed(state.iterations() * recordCount * sizeof(NodeAttrsRecord));
}

BENCHMARK_REGISTER_F(DbStoreBatchAttrsBenchmarkFixture, 分片性能影响)
    ->Args({1000, 1})   // 1个分片
    ->Args({1000, 5})   // 5个分片
    ->Args({1000, 10})  // 10个分片
    ->Args({1000, 20})  // 20个分片
    ->Unit(benchmark::kMillisecond);

// 基准测试：大数据量属性记录
BENCHMARK_DEFINE_F(DbStoreBatchAttrsBenchmarkFixture, 大数据量属性记录)(benchmark::State& state)
{
    const int64_t recordCount = state.range(0);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        // 创建包含大量属性的记录
        std::vector<std::pair<NodeAttrsRecord, std::string>> testRecords;
        testRecords.reserve(recordCount);
        
        for (int64_t i = 0; i < recordCount; ++i)
        {
            int64_t nodeId = 300000 + i;
            NodeAttrsRecord record;
            record.set_id(nodeId);
            record.set_name("大数据量节点_" + std::to_string(nodeId));
            record.set_type(DESIGN);
            record.set_traceid(nodeId * 100);
            
            // 创建大量属性数据
            auto* attrs = record.mutable_attrs();
            auto& attrMap = *attrs->mutable_attr_map();
            
            for (int j = 0; j < 100; ++j) // 100个属性
            {
                NodeBaseValue value;
                value.set_val_string("大数据量属性值_" + std::to_string(nodeId) + "_" + std::to_string(j) + "_" + std::string(100, 'x'));
                attrMap[j] = value;
            }
            
            std::string uuid = "large-attrs-uuid-" + std::to_string(nodeId);
            testRecords.emplace_back(std::move(record), std::move(uuid));
        }
        
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "large_attrs_benchmark", 10);
        state.ResumeTiming();
        
        size_t successCount = dbStore->batchUpsetNodeAttrsRecords(testRecords, DESIGN, IStore::FromServer);
        
        if (successCount != testRecords.size())
        {
            state.SkipWithError("大数据量插入失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * recordCount);
    state.SetBytesProcessed(state.iterations() * recordCount * sizeof(NodeAttrsRecord) * 100); // 估算大小
}

BENCHMARK_REGISTER_F(DbStoreBatchAttrsBenchmarkFixture, 大数据量属性记录)
    ->Range(10, 1000)
    ->Unit(benchmark::kMillisecond)
    ->Complexity();

// 基准测试：混合节点类型批量插入
BENCHMARK_DEFINE_F(DbStoreBatchAttrsBenchmarkFixture, 混合节点类型批量插入)(benchmark::State& state)
{
    const int64_t recordCount = state.range(0);
    
    for (auto _ : state)
    {
        state.PauseTiming();
        auto designRecords = createBenchmarkAttrsData(recordCount / 2, 400000);
        auto catalogRecords = createBenchmarkAttrsData(recordCount / 2, 500000);
        
        // 修改 catalog 记录的类型
        for (auto& [record, uuid] : catalogRecords)
        {
            record.set_type(CATALOG);
        }
        
        dbStore.reset();
        dbStore = std::make_unique<DbStore>(testPath, "mixed_type_attrs_benchmark", 10);
        state.ResumeTiming();
        
        size_t designSuccess = dbStore->batchUpsetNodeAttrsRecords(designRecords, DESIGN, IStore::FromServer);
        size_t catalogSuccess = dbStore->batchUpsetNodeAttrsRecords(catalogRecords, CATALOG, IStore::FromServer);
        
        if (designSuccess != designRecords.size() || catalogSuccess != catalogRecords.size())
        {
            state.SkipWithError("混合类型批量插入失败");
            return;
        }
    }
    
    state.SetItemsProcessed(state.iterations() * recordCount);
    state.SetBytesProcessed(state.iterations() * recordCount * sizeof(NodeAttrsRecord));
}

BENCHMARK_REGISTER_F(DbStoreBatchAttrsBenchmarkFixture, 混合节点类型批量插入)
    ->Range(200, 2000)
    ->Unit(benchmark::kMillisecond)
    ->Complexity();

// 主函数
BENCHMARK_MAIN();
