//
// Created by everpan on 25-5-14.
//

#include <gtest/gtest.h>
#include <glog/logging.h>
#include "rpc/client/DesignServiceClient.h"
#include "rpc/client/DesignServiceConnectionPool.h"
#include "service/ConfigParser.h"
#include "store/db_store/DbStore.h"
#include "proto/def.h"
#include <rapidjson/document.h>
#include <string>
#include <vector>

#include "rpc/minio/MinioDownloader.h"
#include "rpc/minio/MinioTaskState.h"
#include "service/DesignKafkaService.h"
#include "service/DesignTLVFile.h"

using namespace wiz;


TEST(BootstrapTest, login)
{
    auto& pool = wiz::DesignServiceConnectionPool::getInstance();
    // pool.initialize("192.168.66.116:9090");
    // pool.initialize("192.168.66.116:9090");
    pool.initialize("dc3d-v2.wiz.top:9092");


    auto user = design::UserInfo();
    user.set_user("16666666661");
    user.set_pwd("Aa123456");
    auto& service = DesignServiceClient::getInstance();
    // 用户登录，链封装接口
    auto userConfig = service.LoginChain(user);
    if (!userConfig)
    {
        LOG(INFO) << "登录失败";
        return;
    }
    LOG(INFO) << userConfig.value();

    // 获取配置信息
    auto projectInfo = design::ProjectInfo();
    projectInfo.set_projectcode("e-test-004");
    projectInfo.set_projectcode("sam");
    // projectInfo.set_classification("catalog");
    projectInfo.set_user(user.user());

    auto configJson = service.getConfig(projectInfo);
    LOG(INFO) << "Raw config from service: " << configJson;

    auto permJson = service.getPermissions(projectInfo);
    LOG(INFO) << "Raw perm from service: " << permJson.size();

    auto serviceConfig = ConfigParser::parseFromJson(configJson);
    auto mv = serviceConfig.minio->getLatestSnapshotTime();
    LOG(INFO) << "verify json:" << serviceConfig.toJson() << "|\n" << mv.first << " - " << mv.second;
    // todo 对比本地时间与远程时间

    // 下载minio文件并导入本地store
    auto progressCallback = [](const wiz::TaskProgress& progress)
    {
        static int i = 0;

        LOG_IF(
            INFO, i%10==0
            || progress.state == wiz::MinioTaskState::COMPLETED
            || progress.state == wiz::MinioTaskState::FAILED
        ) << "\rDownloading " << progress.objectName
            << ": " << std::fixed << std::setprecision(2) << progress.getPercentage() << "% "
            << "(" << progress.bytesDownloaded << "/" << progress.totalBytes << " bytes) "
            << std::setprecision(2) << (progress.speed / 1024.0 / 1024.0) << " MB/s";
        ++i;
    };

    std::vector<std::tuple<std::string, std::string, std::string>> tasks;
    for (const auto& sn : serviceConfig.minio->nodeSnapshotBasePath)
    {
        tasks.push_back(std::make_tuple(
            serviceConfig.minio->bucketName,
            sn.fileName,
            "/tmp/" + sn.fileName
        ));
    }
    MinioDownloader downloader(
        serviceConfig.minio->endpoint,
        serviceConfig.minio->accessKey,
        serviceConfig.minio->secretKey,
        false,
        4
    );
    auto taskIds = downloader.addTasks(tasks, progressCallback);
    downloader.waitForCompletion(5000);
    // 导入本地
    auto dataPath = "/tmp/data";
    auto dbPrefix = serviceConfig.kafka.value().topic.substr(12);
    auto store = std::make_unique<WD::store::DbStore>(dataPath, dbPrefix, 10); // 10个表，这里的参数不要改动
    for (const auto& t : tasks)
    {
        auto tvf = DesignTLVFile(std::get<2>(t));
        tvf.updateStore(*store);
    }
}

TEST(BootstrapTest, 获取kafka的分区信息)
{
    auto& kafka = DesignKafkaService::getInstance();
    kafka.initialize("192.168.66.112:9092,192.168.66.113:9092,192.168.66.114:9092",
                     "design-node-1916794522764193792",
                     "", 0);
    auto partitionInfos = kafka.getTopicPartitionInfo();
    for (const auto& p : partitionInfos)
    {
        LOG(INFO) << p.toJson();
    }
    LOG(INFO) << "partition num:" << partitionInfos.size();
}

int64_t timeStr2stamp(const std::string& time_str)
{
    //std::string time_str = "2025-05-15 17:50:14.281";

    // 解析日期和时间部分
    std::tm tm = {};
    std::istringstream ss(time_str);
    ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
    // 转换为时间戳（秒）
    std::cout << "tm:" << tm.tm_year << " " << tm.tm_mon << " " << tm.tm_mday << " " << tm.tm_hour << " " << tm.tm_min
        << " " << tm.tm_sec << std::endl;
    std::time_t tt = std::mktime(&tm);
    std::chrono::system_clock::time_point tp = std::chrono::system_clock::from_time_t(tt);
    // 计算 Unix 时间戳（毫秒）
    auto duration = tp.time_since_epoch();
    long long timestamp_ms = duration_cast<std::chrono::milliseconds>(duration).count();

    std::cout << "Timestamp (microseconds): " << timestamp_ms << std::endl;
    return timestamp_ms;
}

TEST(BootstrapTest, 通过时间获取对应的offset)
{
    auto& kafka = DesignKafkaService::getInstance();
    kafka.initialize("192.168.66.112:9092,192.168.66.113:9092,192.168.66.114:9092",
                     "design-node-1916794522764193792",
                     "test", 0);
    std::vector<int> partitions;
    for (int i = 0; i < 12; i++)
    {
        partitions.push_back(i);
    }
    auto now = timeStr2stamp("2025-05-15 16:30:35.280");

    auto partitionOffset = kafka.getOffsetByTimestamp(now, partitions);
    for (const auto& p : partitionOffset)
    {
        LOG(INFO) << std::get<0>(p) << " " << std::get<1>(p) << " " << std::get<2>(p);
    }
    LOG(INFO) << "partition num:" << partitionOffset.size();
    // 开启消费
    // ** 要留心 offset == -1 的准确性
    // kafka.startFromTopicPartitions(partitionOffset);
    // sleep(10);
    // kafka.self().detach();
}
