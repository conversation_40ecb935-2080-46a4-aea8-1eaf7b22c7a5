#pragma once

#include "common/WDUtils.h"

WD_NAMESPACE_BEGIN

class WDBlockingTaskPrivate;
class WDCore;

/**
 * @brief 阻塞式任务对象
 *  支持任务在另一个线程运行, 提供任务进度; 
 *  在此期间会阻塞主窗口, 且主窗口不能被操作(disabled), 直到任务完成或手动退出
*/
class WD_API WDBlockingTask
{
public:
    /**
     * @brief 配置
    */
    struct Config
    {
        // 是否可关闭
        bool closable = false;
        // 是否有进度
        // 有进度时，需要自己设置进度值以保证进度条正常进行
        // 没有进度时，不用设置进度值，进度条没有进度，但将以一直滚动的方式呈现
        bool progress = false;
        // 如果有进度值，进度值显示的小数点保留位数
        // 这个是一个默认值，当有多个进度条，并且没有专门为某个进度条指定保留位数时，将统一使用该值
        int decimals = 2;
        // 进度条个数(小于1时该值无效), 如果有进度值, 可以通过该属性指定更多个数的进度条
        // 进度条将依次从上往下排列，并且每个进度条都将自动分配一个Id(从上往下, 从0开始，依次累加，即最上面进度条的Id是0，最下面进度条的Id是progressBarCount-1)
        // 当设置进度或者进度文本时，可以指定对应的Id来设置
        int progressBarCount = 1;
        // 如果有进度值，这里指定某个Id的进度条的小数点保留位数
        //  first: 进度条的Id
        //  second: 对应Id进度条的小数点保留位数
        std::map<int, int> idDecimals;
    };
    /**
     * @brief 任务回调
    */
    using CallBack = std::function<void(WDBlockingTask& task)>;
private:
    WDBlockingTaskPrivate* _p;
public:
    WDBlockingTask(WDCore& core);
    ~WDBlockingTask();
public:
    /**
     * @brief 任务进度条设置
     * @param config 设置对象
    */
    void setConfig(const Config& config);
    /**
     * @brief 获取任务进度条设置
     * @return 设置对象
    */
    const Config& config() const;
    /**
     * @brief 开始任务
     *  必须在主线程调用，其他线程调用时，将直接返回失败
     * @param callback 任务回调
     * @return 是否正常开始任务，如果有任务正在执行，则任务开始失败
    */
    virtual bool start(const CallBack& callback);
    /**
     * @brief 开始任务
     *  必须在主线程调用，其他线程调用时，将直接返回失败
     * @param callback 任务回调
     * @param onEndCallBack 任务完成后的回调
     * @return 是否正常开始任务，如果有任务正在执行，则任务开始失败
    */
    virtual bool start(const CallBack& callback
        , const CallBack& endCallBack);
public:
    /**
     * @brief 设置当前进度
     * @param progress 进度值，取值范围[0, 1]
     * @param id 进度条Id，当有多个进度条时，可以指定某一个进度条的进度值
    */
    void setProgress(double progress, int id = 0);
    /**
     * @brief 设置进度提示文本
     * @param text 文本，utf8编码
     * @param progressOpt 进度值，取值范围[0, 1], 可以选择指定，如果指定了进度值，则刷新，否则使用前一次设置的进度值
     * @param id 进度条Id，当有多个进度条时，可以指定某一个进度条的进度值
    */
    void setProgressText(const std::string& text, std::optional<double> progressOpt = std::nullopt, int id = 0);
    /**
     * @brief 获取退出标志
    */
    bool existFlag() const;
    /**
     * @brief 获取是否正在有任务进行
    */
    bool isRunning() const;
public:
    /**
     * @brief 获取Core
     */
    WDCore& core();
protected:
    /**
     * @brief 开始任务
    */
    virtual void onStart(const CallBack& callBack) = 0;
    /**
     * @brief 开始进度条
    */
    virtual void onShowProgressBar() = 0;
    /**
     * @brief 结束进度条
    */
    virtual void onHideProgressBar() = 0;
    /**
     * @brief 结束任务
    */
    virtual void onEnd(const CallBack& callBack) = 0;
    /**
     * @brief 设置进度
     * @param 进度条Id
    */
    virtual void onProgress(int id) = 0;
protected:
    /**
     * @brief 设置退出标志，由子类调用
    */
    void setExistFlag();
    /**
     * @brief 获取进度值，由子类调用
    */
    double progress(int id = 0) const;
    /**
     * @brief 获取进度条进度值和文本，由子类调用
    */
    const std::string& text(int id = 0) const;
};



WD_NAMESPACE_END
