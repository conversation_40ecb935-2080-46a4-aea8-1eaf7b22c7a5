#include "WDEditAxisMoveRotate.h"
#include "../viewer/primitiveRender/WDLineRender.h"
#include "../viewer/primitiveRender/WDStdPriRender.h"
#include "../viewer/primitiveRender/WDTextRender.h"
#include "../viewer/WDViewer.h"
#include "../math/DirectionParser.h"


WD_NAMESPACE_BEGIN;

//计算射线是否与某个四边形面有交点
extern bool GetRayRectaceInsPt(const DRay& ray, DVec3 rect[4], DVec3& retPt); 

class WDEditAxisMoveRotatePrivate
{
public:

    using EAxis = WDEditAxisMoveRotate::Axis;
    using EHandleType = WDEditAxisMoveRotate::HandleType;
    using EAxisArcPart = WDEditAxisMoveRotate::AxisArcPart;
    //
    WDEditAxisMoveRotate& _d;
    friend class WDEditAxisMoveRotate;

    // 移动轴的X,Y,Z轴线数据, 每两个点一个线段
    std::array<DVec3, 6> _mAxes;
    // 移动轴的XOY,YOZ,ZOX面的轴线数据, 每三个点一条折线
    std::array<DVec3, 9> _mPlaneAxes;
    // 旋转轴的弧线数据, 点的个数根据分段数而定
    std::array<DVec3Vector, 6> _rAxesArc;
    //包围盒
    DAabb3 _aabb;

    // 线绘制对象
    WDLineRender _lineRender;
    // 坐标轴线常规样式
    WDLineRender::Param _normalLineParam;
    // 坐标轴线高亮样式
    WDLineRender::Param _hoverLineParam;
    // 箭头绘制对象
    WDStdPriRender _coneRender;
    // 文字绘制对象
    WDText3DRender _textRender;

    // 高亮的轴
    EAxis _hoveredAxis;
    // 选中的轴
    EAxis _selectedAxis;
    // 当前正在进行的编辑类型
    EHandleType _handleType;
    // 选中的弧线部分
    EAxisArcPart _axisArcPart;

    // 预览高亮的轴
    EAxis _previewHoveredAxis;
    // 预览正在进行的编辑类型
    EHandleType _previewHandleType;
    // 预览选中的弧线部分
    EAxisArcPart _previewAxisArcPart;
    // 预览开始时的原始位置和轴向
    DVec3 _previewBaseOri;
    DVec3 _previewBaseX;
    DVec3 _previewBaseY;
    DVec3 _previewBaseZ;

    // 鼠标按钮按下
    bool _bMouseDown;
    // 开始时的鼠标位置
    IVec2 _startMousePos;
    // 当前鼠标的位置
    IVec2 _currentMousePos;

    // 鼠标右键按下
    IVec2 _rDownPos;
    WDEditAxisMoveRotate::Axis _rDownAxis = WDEditAxisMoveRotate::Axis::A_Null;

    // 鼠标按下到当前的移动偏移
    DVec3 _mOffset;
    // 鼠标按下到当前的角度偏移
    double _rAngle;

    // 移动通知
    WDEditAxisMoveRotate::MDelegate _delegate;
    // 右键菜单通知
    WDEditAxisMoveRotate::MContextMenuDelegate _cxtMenuDelegate;
    // 鼠标单击通知
    WDEditAxisMoveRotate::MClickedDelegate _clickedDelegate;
    // 鼠标双击通知
    WDEditAxisMoveRotate::MDBClickedDelegate _dbClickedDelegate;

    // 移动步长，默认值:1
    real _stepLength = 1.0;
    // 每次旋转的角度步长
    real _stepAngle = 1.0;

public:
    WDEditAxisMoveRotatePrivate(WDEditAxisMoveRotate& d) :_d(d)
    {
        _hoveredAxis    = EAxis::A_Null;
        _selectedAxis   = EAxis::A_Null;
        _handleType     = EHandleType::HT_None;
        _axisArcPart    = EAxisArcPart::AAP_None;

        _previewHoveredAxis     = EAxis::A_Null;
        _previewHandleType      = EHandleType::HT_None;
        _previewAxisArcPart     = EAxisArcPart::AAP_None;


        _aabb = DAabb3::Null();

        _bMouseDown = false;
        _startMousePos = IVec2(0);
        _currentMousePos = IVec2(0);
        
        _normalLineParam.lineWidth  = 2.0f;
        _normalLineParam.color      = Color(220, 0, 220, 255);

        _hoverLineParam.lineWidth   = 2.0f;
        _hoverLineParam.color       = Color(240, 160, 240, 255);

        _mOffset = DVec3::Zero();
        _rAngle = 0.0;
    }
protected:
    void update(WDContext& context)
    {
        _aabb = DAabb3::Null();
        _lineRender.reset();
        _coneRender.reset();
        _textRender.reset();

        const WDCamera& camera = context.camera();
        const auto unitF = camera.pixelU(_d.position(), camera.viewSizeT<int>());
        // 移动轴线长度
        const auto size = unitF * 150.0;
        // 移动面线框长度
        const auto planeSize = size * 0.3;

        /**** 轴心点 *****/
        const auto center = _d.position();
        _aabb.expandByPoint(center);
        /**** 移动轴 *****/
        // X
        const auto axisX = _d.axisX() * size;
        const auto planeX = _d.axisX() * planeSize;
        _mAxes[0] = center;
        _mAxes[1] = center + axisX;
        _aabb.expandByPoint(axisX);
        // Y
        const auto axisY = _d.axisY() * size;
        const auto planeY = _d.axisY() * planeSize;
        _mAxes[2] = center;
        _mAxes[3] = center + axisY;
        _aabb.expandByPoint(axisY);
        // Z
        const auto axisZ = _d.axisZ() * size;
        const auto planeZ = _d.axisZ() * planeSize;
        _mAxes[4] = center;
        _mAxes[5] = center + axisZ;
        _aabb.expandByPoint(axisZ);
        // XY
        const auto planeXY = planeX + planeY;
        _mPlaneAxes[0] = center + planeX;
        _mPlaneAxes[1] = center + planeXY;
        _mPlaneAxes[2] = center + planeY;
        // YZ
        const auto planeYZ = planeY + planeZ;
        _mPlaneAxes[3] = center + planeY;
        _mPlaneAxes[4] = center + planeYZ;
        _mPlaneAxes[5] = center + planeZ;
        // ZX
        const auto planeZX = planeZ + planeX;
        _mPlaneAxes[6] = center + planeZ;
        _mPlaneAxes[7] = center + planeZX;
        _mPlaneAxes[8] = center + planeX;

        /**** 旋转轴 *****/

        // 旋转基向量和旋转轴
        std::array<std::pair<DVec3, DVec3>, 3> brAxes = { std::make_pair(axisX, axisZ)
            , std::make_pair(axisY, axisX)
            , std::make_pair(axisZ, axisY) };
        // 起始角度和角度范围, X表示起始的旋转角度，Y表示角度的范围
        DVec2 angleSRA = DVec2(-10.0, 60);
        DVec2 angleSRB = DVec2(angleSRA.x + angleSRA.y, angleSRA.y);
        // 弧线分段数
        const auto segments = static_cast<uint>(angleSRA.y / 5.0);
        // 使用旋转法计算旋转轴的弧线
        for (size_t i = 0; i < 3; ++i)
        {
            const auto& baseAxis = brAxes[i].first;
            const auto& rotAxis = brAxes[i].second;

            auto& rotAxisArcPartA = _rAxesArc[i * 2 + 0];
            auto& rotAxisArcPartB = _rAxesArc[i * 2 + 1];
            rotAxisArcPartA.resize(segments + 1);
            rotAxisArcPartB.resize(segments + 1);

            for (uint j = 0; j <= segments; ++j)
            {
                const auto step = static_cast<double>(j) / static_cast<double>(segments);
                const auto angleA = angleSRA.x + angleSRA.y * step;
                const auto quatA = DQuat::Rotate(angleA, rotAxis);
                rotAxisArcPartA[j] = center + quatA * baseAxis;

                const auto angleB = angleSRB.x + angleSRB.y * step;
                const auto quatB = DQuat::Rotate(angleB, rotAxis);
                rotAxisArcPartB[j] = center + quatB * baseAxis;
            } 
            _aabb.expandByPoint(rotAxisArcPartA.front());
            _aabb.expandByPoint(rotAxisArcPartB.back());
        }

        /**** 箭头参数 *****/
        const float arrowDiameter   = static_cast<float>(size * 0.12);
        const float arrowLength     = static_cast<float>(size * 0.22);

        /**** 添加到绘制 *****/

        // 移动轴
        std::array<MAxisDFs, 3> mDF = this->mAxisDF(this->getHType()
            , this->getHoveredAxis()
            , this->getAxisArcPart());
        std::array<std::wstring, 3> axisLabels = { L"X", L"Y", L"Z" };  
        for (size_t i = 0; i < 3; ++i) 
        {
            const auto& ptA = _mAxes[i * 2 + 0];
            const auto& ptB = _mAxes[i * 2 + 1];
            const auto dir  = (ptB - ptA).normalize();
            // 轴线
            if (mDF[i].hasFlag(MDF_AxisShown)) 
            {
                const auto& param = mDF[i].hasFlag(MDF_AxisHeighlight) ? _hoverLineParam : _normalLineParam;
                _lineRender.addLineSeg(FVec3(ptA), FVec3(ptB), param);
            }
            // 箭头
            if (mDF[i].hasFlag(MDF_AxisArrowShown))
            {
                const auto& color = mDF[i].hasFlag(MDF_AxisArrowHeighlight) ? _hoverLineParam.color : _normalLineParam.color;
                DMat4 tsfm = DMat4::Compose(ptB, DQuat::FromVectors(DVec3::AxisZ(), dir));
                _coneRender.addCone(arrowDiameter, arrowLength, color, FMat4(tsfm));
            }
            // 轴标签
            if (mDF[i].hasFlag(MDF_AxisLabelShown)) 
            {
                _textRender.addFixedDirection(axisLabels[i]
                    , FVec3(center + dir * size * 0.6)
                    , Color::white
                    , 16
                    , WDTextRender::HA_Left
                    , WDTextRender::VA_Bottom
                    , FVec2(0.0f)
                    , true);
            }
            // 轴向文字
            if (mDF[i].hasFlag(MDF_AxisDirTextShown)) 
            {
                std::string rStr = DirectionParserENU::OutputStringByDirection(dir, 4);
                _textRender.addFixedDirection(stringToWString(rStr)
                    , FVec3(center + dir * size * 1.1)
                    , Color::white
                    , 14
                    , WDTextRender::HA_Left
                    , WDTextRender::VA_Bottom
                    , FVec2(0.0f)
                    , true);
            }
            // 轴末端小球
            if (mDF[i].hasFlag(MDF_AxisSphereShown)) 
            {
                const auto color = _normalLineParam.color;
                DMat4 tsfm = DMat4::MakeTranslation(ptB);
                _coneRender.addSphere(arrowDiameter, color, FMat4(tsfm));
            }
        }
        // 面移动轴
        std::array<MPleneAxisDFs, 3> mPlaneDF = this->mPlaneAxisDF(this->getHType()
            , this->getHoveredAxis()
            , this->getAxisArcPart());
        for (size_t i = 0; i < 3; ++i) 
        {
            if (!mPlaneDF[i].hasFlag(MPDF_AxisShown))
                continue;
            const auto& param = mPlaneDF[i].hasFlag(MPDF_AxisHeighlight) ? _hoverLineParam : _normalLineParam;
            _lineRender.addLineStrip({ FVec3(_mPlaneAxes[i * 3 + 0])
                , FVec3(_mPlaneAxes[i * 3 + 1])
                , FVec3(_mPlaneAxes[i * 3 + 2]) }, param);
        }

        // 旋转轴
        std::array<RAxisDFs, 6> rDF = this->rAxisDF(this->getHType()
            , this->getHoveredAxis()
            , this->getAxisArcPart());
        FVec3Vector tArcVs;
        for (size_t i = 0; i < 6; ++i) 
        {
            const auto& arcVs = _rAxesArc[i];
            tArcVs.clear();
            tArcVs.reserve(arcVs.size());
            for (const auto& v : arcVs)
                tArcVs.push_back(FVec3(v));

            // 显示轴
            if (rDF[i].hasFlag(RDF_AxisShown))
            {
                const auto& param = rDF[i].hasFlag(RDF_AxisHeighlight) ? _hoverLineParam : _normalLineParam;
                _lineRender.addLineStrip(tArcVs, param);
            }
            // 显示箭头
            if (rDF[i].hasFlag(RDF_AxisArrowShown)) 
            {
                const auto& color = rDF[i].hasFlag(RDF_AxisArrowHeighlight) ? _hoverLineParam.color : _normalLineParam.color;

                FMat4 tsfm0 = FMat4::Compose(FVec3(tArcVs.front())
                    , FQuat::FromVectors(FVec3::AxisZ(), (tArcVs[0] - tArcVs[1]).normalize()));
                _coneRender.addCone(arrowDiameter, arrowLength, color, tsfm0);
                
                FMat4 tsfm1 = FMat4::Compose(FVec3(tArcVs.back())
                    , FQuat::FromVectors(FVec3::AxisZ(), (tArcVs[tArcVs.size() - 1] - tArcVs[tArcVs.size() - 2]).normalize()));
                _coneRender.addCone(arrowDiameter, arrowLength, color, tsfm1);

            }
        }

        // 辅助线样式
        auto lineParam = _normalLineParam;
        lineParam.style = WDLineRender::Style::DashLine;
        // 处理当前正在移动 / 旋转时 显示的辅助线 和值
        switch (this->getHType())
        {
        case EHandleType::HT_Move:
            {
                // 根据移动偏移量计算按下时的轴心位置
                DVec3 pt0 = _d.position() - _mOffset;
                // 当前轴心位置
                DVec3 pt1 = _d.position();
                // 移动偏移长度
                double len = _mOffset.length();
                if (len >= NumLimits<float>::Epsilon)
                {
                    // 鼠标按下时轴心点到当前轴心点的连线
                    _lineRender.addLineSeg(FVec3(pt0), FVec3(pt1), lineParam);
                    // 当前轴心点和按下轴心点的小方块
                    std::array<std::vector<FVec3>, 2> rects = { 
                          ScreenRect(camera, pt0, DVec2(size * 0.02)) 
                        , ScreenRect(camera, pt1, DVec2(size * 0.02)) };
                    for (const auto& rect : rects) 
                    {
                        for (size_t i = 0; i < rect.size(); i += 2) 
                        {
                            _lineRender.addLineLoop(rect, _normalLineParam);
                        }
                    }

                    std::array<DVec3, 3> nAxes = { _d.axisX().normalized() , _d.axisY().normalized() , _d.axisZ().normalized() };
                    wchar_t tText[1024] = { 0 };

                    std::optional<DVec3> midPos = std::nullopt;
                    for (size_t i = 0; i < nAxes.size(); ++i) 
                    {
                        const auto dt = DVec3::Dot(nAxes[i], _mOffset);
                        if (Abs(dt) <= NumLimits<float>::Epsilon)
                            continue;
                        // 距离文本
                        swprintf_s(tText, 1024, L"%s %.2lf", axisLabels[i].data(), dt);

                        DVec3 ptS = pt0;
                        DVec3 ptE = pt0;
                        if (midPos) 
                        {
                            ptS = midPos.value();
                            ptE = pt1;
                        }
                        else
                        {
                            ptE = pt0 + nAxes[i] * dt;
                            midPos = ptE;
                        }
                        // 连线
                        _lineRender.addLineSeg(FVec3(ptS), FVec3(ptE), lineParam);
                        // 距离文本
                        _textRender.addFixedDirection(tText
                            , FVec3((ptS + ptE) * 0.5)
                            , Color::white
                            , 14
                            , WDTextRender::HA_Left
                            , WDTextRender::VA_Bottom
                            , FVec2(0.0f)
                            , true);
                    }
                }
            }
            break;
        case EHandleType::HT_Rotate:
            {
                // 当前轴心位置
                DVec3 pt1 = _d.position();
                if (Abs(_rAngle) >= NumLimits<float>::Epsilon)
                {
                    int axisIdx = 0;
                    DVec3 axis = _d.axisX().normalized();
                    switch (getAxisArcPart())
                    {
                    case EAxisArcPart::AAP_XYA:
                        {
                            axisIdx = 0;
                            axis = _d.axisZ().normalized();
                        }
                        break;
                    case EAxisArcPart::AAP_XYB:
                        {
                            axisIdx = 1;
                            axis = _d.axisZ().normalized();
                        }
                        break;
                    case EAxisArcPart::AAP_YZA:
                        {
                            axisIdx = 1;
                            axis = _d.axisX().normalized();
                        }
                        break;
                    case EAxisArcPart::AAP_YZB:
                        {
                            axisIdx = 2;
                            axis = _d.axisX().normalized();
                        }
                        break;
                    case EAxisArcPart::AAP_ZXA:
                        {
                            axisIdx = 2;
                            axis = _d.axisY().normalized();
                        }
                        break;
                    case EAxisArcPart::AAP_ZXB:
                        {
                            axisIdx = 0;
                            axis = _d.axisY().normalized();
                        }
                        break;
                    default:
                        break;
                    }
                    const auto& p0  = _mAxes[axisIdx * 2 + 0];
                    const auto& p1  = _mAxes[axisIdx * 2 + 1];
                    const auto v    = p1 - p0;
                    // 旋转法计算旋转弧线
                    uint segment    = static_cast<uint>(Abs(_rAngle / 5.0));
                    const auto tV1  = v * 0.75;
                    auto quat       = DQuat::Rotate(-_rAngle, axis);
                    const auto tV0  = quat * tV1;
                    std::vector<FVec3> tRotArc;
                    tRotArc.reserve(segment + 1);
                    tRotArc.push_back(FVec3(p0 + tV0));
                    for (size_t i = 1; i < segment; ++i)
                    {
                        const auto angle = static_cast<double>(i) / static_cast<double>(segment) * _rAngle;
                        quat = DQuat::Rotate(angle, axis);
                        tRotArc.push_back(FVec3(p0 + quat * tV0));
                    }
                    tRotArc.push_back(FVec3(p0 + tV1));
                    // 鼠标按下时轴心点到当前轴心点的连线
                    _lineRender.addLineStrip(tRotArc, lineParam);
                    // 旋转起点处画一个小方块
                    const auto rect = ScreenRect(camera, DVec3(tRotArc.front()), DVec2(size * 0.02));
                    _lineRender.addLineLoop(rect, _normalLineParam);
                    // 显示旋转角度
                    wchar_t tBuf[1024] = { 0 };
                    swprintf_s(tBuf, 1024, L"%.2lf", _rAngle);
                    _textRender.addFixedDirection(tBuf
                        , tRotArc[tRotArc.size() / 2]
                        , Color::white
                        , 14
                        , WDTextRender::HA_Left
                        , WDTextRender::VA_Bottom
                        , FVec2(0.0f)
                        , true);
                }
            }
            break;
        default:
            break;
        }
    }
    void render(WDContext& context)
    {
        _coneRender.render(context, false);
        _lineRender.render(context, false);
        _textRender.render(context, false);
    }
public:
    // 计算移动/旋转
    bool calc(WDContext& context, DVec4& outRelativeOffset
        , DVec4& outAbsoluteOffset
        , WDEditAxisMoveRotate::HandleType& outHType)
    {
        outHType = WDEditAxisMoveRotate::HandleType::HT_None;

        auto oldMOffset = _mOffset;
        auto oldRAngle = _rAngle;
        auto rotAxis = DVec3::Zero();

        switch (_selectedAxis)
        {
        case WD::WDEditAxisMoveRotate::A_MX:
            {
                const auto axis = _d.axisX().normalized();
                auto ret = Move(context, _d, axis, _startMousePos, _currentMousePos);
                if (ret) 
                {
                    auto value = lengthWidthStep(ret.value());
                    _mOffset = axis * value;
                }
                outHType = WDEditAxisMoveRotate::HandleType::HT_Move;
            }
            break;
        case WD::WDEditAxisMoveRotate::A_MY:
            {
                const auto axis = _d.axisY().normalized();
                auto ret = Move(context, _d, axis, _startMousePos, _currentMousePos);
                if (ret) 
                {
                    auto value = lengthWidthStep(ret.value());
                    _mOffset = axis * value;
                }
                outHType = WDEditAxisMoveRotate::HandleType::HT_Move;
            }
            break;
        case WD::WDEditAxisMoveRotate::A_MZ:
            {
                const auto axis = _d.axisZ().normalized();
                auto ret = Move(context, _d, axis, _startMousePos, _currentMousePos);
                if (ret) 
                {
                    auto value = lengthWidthStep(ret.value());
                    _mOffset = axis * value;
                }
                outHType = WDEditAxisMoveRotate::HandleType::HT_Move;
            }
            break;
        case WD::WDEditAxisMoveRotate::A_MXY:
            {
                const auto axisA = _d.axisX().normalized();
                const auto axisB = _d.axisY().normalized();
                auto ret = PlaneMove(context, _d, axisA, axisB, _startMousePos, _currentMousePos);
                if (ret) 
                {
                    auto valueA = lengthWidthStep(ret.value().first);
                    auto valueB = lengthWidthStep(ret.value().second);
                    _mOffset = axisA * valueA + axisB * valueB;
                }
                outHType = WDEditAxisMoveRotate::HandleType::HT_Move;
            }
            break;
        case WD::WDEditAxisMoveRotate::A_MYZ:
            {
                const auto axisA = _d.axisY().normalized();
                const auto axisB = _d.axisZ().normalized();
                auto ret = PlaneMove(context, _d, axisA, axisB, _startMousePos, _currentMousePos);
                if (ret) 
                {
                    auto valueA = lengthWidthStep(ret.value().first);
                    auto valueB = lengthWidthStep(ret.value().second);
                    _mOffset = axisA * valueA + axisB * valueB;
                }
                outHType = WDEditAxisMoveRotate::HandleType::HT_Move;
            }
            break;
        case WD::WDEditAxisMoveRotate::A_MZX:
            {
                const auto axisA = _d.axisZ().normalized();
                const auto axisB = _d.axisX().normalized();
                auto ret = PlaneMove(context, _d, axisA, axisB, _startMousePos, _currentMousePos);
                if (ret) 
                {
                    auto valueA = lengthWidthStep(ret.value().first);
                    auto valueB = lengthWidthStep(ret.value().second);
                    _mOffset = axisA * valueA + axisB * valueB;
                }
                outHType = WDEditAxisMoveRotate::HandleType::HT_Move;
            }
            break;
        case WD::WDEditAxisMoveRotate::A_RX:
            {
                rotAxis = _d.axisX().normalized();
                auto ret = Rotate(context, _d, rotAxis, _startMousePos, _currentMousePos);
                if (ret) 
                {
                    _rAngle = angleWidthStep(ret.value());
                }
                outHType = WDEditAxisMoveRotate::HandleType::HT_Rotate;
            }
            break;
        case WD::WDEditAxisMoveRotate::A_RY:
            {
                rotAxis = _d.axisY().normalized();
                auto ret = Rotate(context, _d, rotAxis, _startMousePos, _currentMousePos);
                if (ret) 
                {
                    _rAngle = angleWidthStep(ret.value());
                }
                outHType = WDEditAxisMoveRotate::HandleType::HT_Rotate;
            }
            break;
        case WD::WDEditAxisMoveRotate::A_RZ:
            {
                rotAxis = _d.axisZ().normalized();
                auto ret = Rotate(context, _d, rotAxis, _startMousePos, _currentMousePos);
                if (ret) 
                {
                    _rAngle = angleWidthStep(ret.value());
                }
                outHType = WDEditAxisMoveRotate::HandleType::HT_Rotate;
            }
            break;
        default:
            break;
        }

        switch (outHType)
        {
        case WD::WDEditAxisMoveRotate::HT_Move:
            {
                auto tAV = _mOffset;
                auto tAVLen = tAV.length();
                outAbsoluteOffset = DVec4(tAVLen >= NumLimits<float>::Epsilon ? tAV.normalized() : _d.axisX(), tAVLen);

                auto tRV = _mOffset - oldMOffset;
                auto tRVLen = tRV.length();
                outRelativeOffset = DVec4(tRVLen >= NumLimits<float>::Epsilon ? tRV.normalized() : outAbsoluteOffset.xyz(), tRVLen);

                return true;
            }
            break;
        case WD::WDEditAxisMoveRotate::HT_Rotate:
            {
                auto tA = _rAngle - oldRAngle;
                outRelativeOffset = DVec4(rotAxis, tA);
                outAbsoluteOffset = DVec4(rotAxis, _rAngle);
                return true;
            }
            break;
        default:
            break;
        }

        return false;
    }
    //高亮轴
    WDEditAxisMoveRotate::Axis hoverAxis(WDContext& context, const IVec2& pos)
    {
        WDEditAxisMoveRotate::Axis oldHovered = _hoveredAxis;

        _hoveredAxis = pickAxis(context, pos);

        if (oldHovered != _hoveredAxis)
            _d.sendHoveredDelegate();

        return _hoveredAxis;
    }
public:
    static WDEditAxisMoveRotate::HandleType HandleType(EAxis axis)
    {
        switch (axis)
        {
        case WDEditAxisMoveRotate::A_MX:
        case WDEditAxisMoveRotate::A_MY:
        case WDEditAxisMoveRotate::A_MZ:
        case WDEditAxisMoveRotate::A_MXY:
        case WDEditAxisMoveRotate::A_MYZ:
        case WDEditAxisMoveRotate::A_MZX:
            return WDEditAxisMoveRotate::HT_Move;
            break;
        case WDEditAxisMoveRotate::A_RX:
        case WDEditAxisMoveRotate::A_RY:
        case WDEditAxisMoveRotate::A_RZ:
            return WDEditAxisMoveRotate::HT_Rotate;
            break;
        default:
            break;
        }
        return WDEditAxisMoveRotate::HT_None;

    }
private:
    EAxis getHoveredAxis() const 
    {
        if (_hoveredAxis != EAxis::A_Null)
            return _hoveredAxis;
        return _previewHoveredAxis;
    }
    EAxisArcPart getAxisArcPart() const
    {
        if (_axisArcPart != EAxisArcPart::AAP_None)
            return _axisArcPart;
        return _previewAxisArcPart;
    }
    EHandleType getHType() const
    {
        if (_handleType != EHandleType::HT_None)
            return _handleType;
        return _previewHandleType;
    }

    // 移动轴的绘制标记
    enum MAxisDF
    {
        // 无
        MDF_None                    = 0,
        // 显示轴线
        MDF_AxisShown               = 1 << 0,
        // 轴线高亮
        MDF_AxisHeighlight          = 1 << 1,
        // 显示轴线箭头
        MDF_AxisArrowShown          = 1 << 2,
        // 轴线箭头高亮
        MDF_AxisArrowHeighlight     = 1 << 3,
        // 轴标签
        MDF_AxisLabelShown          = 1 << 4,
        // 轴向文字
        MDF_AxisDirTextShown        = 1 << 5,
        // 显示轴线末端小球
        MDF_AxisSphereShown         = 1 << 6,
        // 轴线末端小球高亮
        MDF_AxisSphereHeighlight    = 1 << 7,
    };
    using MAxisDFs = WDFlags<MAxisDF, uint>;

    // 获取三个移动轴的显示和高亮的标志
    static std::array<MAxisDFs, 3> mAxisDF(EHandleType hType
        , EAxis hoveredAxis
        , EAxisArcPart axisArcPart)
    {
        std::array<MAxisDFs, 3> rFlags = { MDF_None, MDF_None, MDF_None };

        auto& xFlags = rFlags[0];
        auto& yFlags = rFlags[1];
        auto& zFlags = rFlags[2];

        switch (hType)
        {
        case EHandleType::HT_None:
            {
                // x
                xFlags.setFlag(MDF_AxisShown);

                xFlags.setFlag(MDF_AxisLabelShown);

                xFlags.setFlag(MDF_AxisHeighlight
                    ,  hoveredAxis == EAxis::A_MX 
                    || hoveredAxis == EAxis::A_MXY 
                    || hoveredAxis == EAxis::A_MZX
                    || hoveredAxis == EAxis::A_RX
                    || axisArcPart == EAxisArcPart::AAP_XYA
                    || axisArcPart == EAxisArcPart::AAP_ZXB);

                xFlags.setFlag(MDF_AxisArrowShown
                    ,  hoveredAxis == EAxis::A_MX 
                    || hoveredAxis == EAxis::A_MXY 
                    || hoveredAxis == EAxis::A_MZX);

                xFlags.setFlag(MDF_AxisArrowHeighlight
                    ,  hoveredAxis == EAxis::A_MX 
                    || hoveredAxis == EAxis::A_MXY
                    || hoveredAxis == EAxis::A_MZX);

                xFlags.setFlag(MDF_AxisDirTextShown
                    ,  hoveredAxis == EAxis::A_MX
                    || axisArcPart == EAxisArcPart::AAP_XYA
                    || axisArcPart == EAxisArcPart::AAP_ZXB);

                // y
                yFlags.setFlag(MDF_AxisShown);

                yFlags.setFlag(MDF_AxisLabelShown);

                yFlags.setFlag(MDF_AxisHeighlight
                    ,  hoveredAxis == EAxis::A_MY 
                    || hoveredAxis == EAxis::A_MXY 
                    || hoveredAxis == EAxis::A_MYZ
                    || hoveredAxis == EAxis::A_RY
                    || axisArcPart == EAxisArcPart::AAP_XYB
                    || axisArcPart == EAxisArcPart::AAP_YZA);

                yFlags.setFlag(MDF_AxisArrowShown
                    ,  hoveredAxis == EAxis::A_MY 
                    || hoveredAxis == EAxis::A_MXY
                    || hoveredAxis == EAxis::A_MYZ);

                yFlags.setFlag(MDF_AxisArrowHeighlight
                    ,  hoveredAxis == EAxis::A_MY 
                    || hoveredAxis == EAxis::A_MXY
                    || hoveredAxis == EAxis::A_MYZ);

                yFlags.setFlag(MDF_AxisDirTextShown
                    ,  hoveredAxis == EAxis::A_MY
                    || axisArcPart == EAxisArcPart::AAP_XYB
                    || axisArcPart == EAxisArcPart::AAP_YZA);

                // z
                zFlags.setFlag(MDF_AxisShown);

                zFlags.setFlag(MDF_AxisLabelShown);

                zFlags.setFlag(MDF_AxisHeighlight
                    ,  hoveredAxis == EAxis::A_MZ 
                    || hoveredAxis == EAxis::A_MYZ 
                    || hoveredAxis == EAxis::A_MZX
                    || hoveredAxis == EAxis::A_RZ
                    || axisArcPart == EAxisArcPart::AAP_YZB
                    || axisArcPart == EAxisArcPart::AAP_ZXA);

                zFlags.setFlag(MDF_AxisArrowShown
                    ,  hoveredAxis == EAxis::A_MZ
                    || hoveredAxis == EAxis::A_MYZ 
                    || hoveredAxis == EAxis::A_MZX);

                zFlags.setFlag(MDF_AxisArrowHeighlight
                    ,  hoveredAxis == EAxis::A_MZ
                    || hoveredAxis == EAxis::A_MYZ
                    || hoveredAxis == EAxis::A_MZX);

                zFlags.setFlag(MDF_AxisDirTextShown
                    ,  hoveredAxis == EAxis::A_MZ
                    || axisArcPart == EAxisArcPart::AAP_YZB
                    || axisArcPart == EAxisArcPart::AAP_ZXA);

            }
            break;
        case EHandleType::HT_Move:
            {
                // x
                xFlags.setFlag(MDF_AxisShown, 
                       hoveredAxis == EAxis::A_MX
                    || hoveredAxis == EAxis::A_MXY
                    || hoveredAxis == EAxis::A_MZX);

                xFlags.setFlag(MDF_AxisArrowShown
                    ,  hoveredAxis == EAxis::A_MX
                    || hoveredAxis == EAxis::A_MXY
                    || hoveredAxis == EAxis::A_MZX);

                // y
                yFlags.setFlag(MDF_AxisShown
                    ,  hoveredAxis == EAxis::A_MY
                    || hoveredAxis == EAxis::A_MXY
                    || hoveredAxis == EAxis::A_MYZ);

                yFlags.setFlag(MDF_AxisArrowShown
                    ,  hoveredAxis == EAxis::A_MY 
                    || hoveredAxis == EAxis::A_MXY
                    || hoveredAxis == EAxis::A_MYZ);

                // z
                zFlags.setFlag(MDF_AxisShown
                    ,  hoveredAxis == EAxis::A_MZ
                    || hoveredAxis == EAxis::A_MYZ
                    || hoveredAxis == EAxis::A_MZX);

                zFlags.setFlag(MDF_AxisArrowShown
                    ,  hoveredAxis == EAxis::A_MZ
                    || hoveredAxis == EAxis::A_MYZ 
                    || hoveredAxis == EAxis::A_MZX);


            }
            break;
        case EHandleType::HT_Rotate:
            {
                // x
                xFlags.setFlag(MDF_AxisShown);

                xFlags.setFlag(MDF_AxisDirTextShown
                    ,  axisArcPart == EAxisArcPart::AAP_XYA
                    || axisArcPart == EAxisArcPart::AAP_ZXB);

                xFlags.setFlag(MDF_AxisSphereShown
                    , hoveredAxis == EAxis::A_RX);

                // y
                yFlags.setFlag(MDF_AxisShown);


                yFlags.setFlag(MDF_AxisDirTextShown
                    ,  axisArcPart == EAxisArcPart::AAP_XYB
                    || axisArcPart == EAxisArcPart::AAP_YZA);

                yFlags.setFlag(MDF_AxisSphereShown
                    ,  hoveredAxis == EAxis::A_RY);

                // z
                zFlags.setFlag(MDF_AxisShown);

                zFlags.setFlag(MDF_AxisDirTextShown
                    ,  axisArcPart == EAxisArcPart::AAP_YZB
                    || axisArcPart == EAxisArcPart::AAP_ZXA);

                zFlags.setFlag(MDF_AxisSphereShown
                    ,  hoveredAxis == EAxis::A_RZ);
            }
            break;
        default:
            break;
        }

        return rFlags;
    }

    // 面移动轴的绘制标记
    enum MPleneAxisDF
    {
        // 无
        MPDF_None = 0,
        // 显示轴线
        MPDF_AxisShown = 1 << 0,
        // 轴线高亮
        MPDF_AxisHeighlight = 1 << 1,
    };
    using MPleneAxisDFs = WDFlags<MPleneAxisDF, uint>;
    // 获取三个面移动轴的显示和高亮的标志
    static std::array<MPleneAxisDFs, 3> mPlaneAxisDF(EHandleType hType
        , EAxis hoveredAxis
        , EAxisArcPart axisArcPart)
    {
        WDUnused(axisArcPart);

        std::array<MPleneAxisDFs, 3> rFlags = { MPDF_None, MPDF_None, MPDF_None };
        auto& xyFlags = rFlags[0];
        auto& yzFlags = rFlags[1];
        auto& zxFlags = rFlags[2];

        switch (hType)
        {
        case EHandleType::HT_None:
            {
                // xoy
                xyFlags.setFlag(MPDF_AxisShown);
                xyFlags.setFlag(MPDF_AxisHeighlight, hoveredAxis == EAxis::A_MXY);
                // yoz
                yzFlags.setFlag(MPDF_AxisShown);
                yzFlags.setFlag(MPDF_AxisHeighlight, hoveredAxis == EAxis::A_MYZ);
                // zox
                zxFlags.setFlag(MPDF_AxisShown);
                zxFlags.setFlag(MPDF_AxisHeighlight, hoveredAxis == EAxis::A_MZX);
            }
            break;
        case EHandleType::HT_Move:
            {
                // xoy
                xyFlags.setFlag(MPDF_AxisShown, hoveredAxis == EAxis::A_MXY);
                // yoz
                yzFlags.setFlag(MPDF_AxisShown, hoveredAxis == EAxis::A_MYZ);
                // zox
                zxFlags.setFlag(MPDF_AxisShown, hoveredAxis == EAxis::A_MZX);
            }
            break;
        case EHandleType::HT_Rotate:
            {

            }
            break;
        default:
            break;
        }

        return rFlags;
    }

    // 旋转轴弧线的绘制标记
    enum RAxisDF
    {
        // 无
        RDF_None = 0,
        // 显示轴线
        RDF_AxisShown = 1 << 0,
        // 轴线高亮
        RDF_AxisHeighlight = 1 << 1,
        // 显示轴线箭头
        RDF_AxisArrowShown = 1 << 2,
        // 轴线箭头高亮
        RDF_AxisArrowHeighlight = 1 << 3,
    };
    using RAxisDFs = WDFlags<RAxisDF, uint>;
    // 获取6个旋转轴的显示和高亮的标志
    static std::array<RAxisDFs, 6> rAxisDF(EHandleType hType
        , EAxis hoveredAxis
        , EAxisArcPart axisArcPart)
    {
        std::array<RAxisDFs, 6> rFlags = { RDF_None, RDF_None, RDF_None, RDF_None, RDF_None, RDF_None };

        auto& rXAFlags = rFlags[0];
        auto& rXBFlags = rFlags[1];
        auto& rYAFlags = rFlags[2];
        auto& rYBFlags = rFlags[3];
        auto& rZAFlags = rFlags[4];
        auto& rZBFlags = rFlags[5];


        switch (hType)
        {
        case EHandleType::HT_None:
        {
            // xa
            rXAFlags.setFlag(RDF_AxisShown);
            rXAFlags.setFlag(RDF_AxisHeighlight,        axisArcPart == EAxisArcPart::AAP_XYA);
            rXAFlags.setFlag(RDF_AxisArrowShown,        axisArcPart == EAxisArcPart::AAP_XYA);
            rXAFlags.setFlag(RDF_AxisArrowHeighlight,   axisArcPart == EAxisArcPart::AAP_XYA);
            // xb
            rXBFlags.setFlag(RDF_AxisShown);
            rXBFlags.setFlag(RDF_AxisHeighlight,        axisArcPart == EAxisArcPart::AAP_XYB);
            rXBFlags.setFlag(RDF_AxisArrowShown,        axisArcPart == EAxisArcPart::AAP_XYB);
            rXBFlags.setFlag(RDF_AxisArrowHeighlight,   axisArcPart == EAxisArcPart::AAP_XYB);

            // ya
            rYAFlags.setFlag(RDF_AxisShown);
            rYAFlags.setFlag(RDF_AxisHeighlight,        axisArcPart == EAxisArcPart::AAP_YZA);
            rYAFlags.setFlag(RDF_AxisArrowShown,        axisArcPart == EAxisArcPart::AAP_YZA);
            rYAFlags.setFlag(RDF_AxisArrowHeighlight,   axisArcPart == EAxisArcPart::AAP_YZA);
            // yb
            rYBFlags.setFlag(RDF_AxisShown);
            rYBFlags.setFlag(RDF_AxisHeighlight,        axisArcPart == EAxisArcPart::AAP_YZB);
            rYBFlags.setFlag(RDF_AxisArrowShown,        axisArcPart == EAxisArcPart::AAP_YZB);
            rYBFlags.setFlag(RDF_AxisArrowHeighlight,   axisArcPart == EAxisArcPart::AAP_YZB);
                                                                                              
            // za
            rZAFlags.setFlag(RDF_AxisShown);
            rZAFlags.setFlag(RDF_AxisHeighlight,        axisArcPart == EAxisArcPart::AAP_ZXA);
            rZAFlags.setFlag(RDF_AxisArrowShown,        axisArcPart == EAxisArcPart::AAP_ZXA);
            rZAFlags.setFlag(RDF_AxisArrowHeighlight,   axisArcPart == EAxisArcPart::AAP_ZXA);
            // ab
            rZBFlags.setFlag(RDF_AxisShown);
            rZBFlags.setFlag(RDF_AxisHeighlight,        axisArcPart == EAxisArcPart::AAP_ZXB);
            rZBFlags.setFlag(RDF_AxisArrowShown,        axisArcPart == EAxisArcPart::AAP_ZXB);
            rZBFlags.setFlag(RDF_AxisArrowHeighlight,   axisArcPart == EAxisArcPart::AAP_ZXB);
        }
        break;
        case EHandleType::HT_Move:
        {
        }
        break;
        case EHandleType::HT_Rotate:
        {
            // xa
            rXAFlags.setFlag(RDF_AxisShown,         hoveredAxis == EAxis::A_RZ);
            rXAFlags.setFlag(RDF_AxisArrowShown,    axisArcPart == EAxisArcPart::AAP_XYA);
            // xb
            rXBFlags.setFlag(RDF_AxisShown,         hoveredAxis == EAxis::A_RZ);
            rXBFlags.setFlag(RDF_AxisArrowShown,    axisArcPart == EAxisArcPart::AAP_XYB);

            // ya
            rYAFlags.setFlag(RDF_AxisShown,         hoveredAxis == EAxis::A_RX);
            rYAFlags.setFlag(RDF_AxisArrowShown,    axisArcPart == EAxisArcPart::AAP_YZA);
            // yb
            rYBFlags.setFlag(RDF_AxisShown,         hoveredAxis == EAxis::A_RX);
            rYBFlags.setFlag(RDF_AxisArrowShown,    axisArcPart == EAxisArcPart::AAP_YZB);

            // za
            rZAFlags.setFlag(RDF_AxisShown,         hoveredAxis == EAxis::A_RY);
            rZAFlags.setFlag(RDF_AxisArrowShown,    axisArcPart == EAxisArcPart::AAP_ZXA);
            // ab
            rZBFlags.setFlag(RDF_AxisShown,         hoveredAxis == EAxis::A_RY);
            rZBFlags.setFlag(RDF_AxisArrowShown,    axisArcPart == EAxisArcPart::AAP_ZXB);
        }
        break;
        default:
            break;
        }


        return rFlags;
    }

    // 拾取轴
    WDEditAxisMoveRotate::Axis pickAxis(WDContext& context, const IVec2& screen)
    {
        constexpr real distMax = 6.0;

        WDCamera& camera = context.camera();
        const DVec3 mouse(screen.x, screen.y, 0);

        _axisArcPart = EAxisArcPart::AAP_None;

        // 1. 先拾取移动轴的 X,Y,Z
        {
            real dist[3] = { NumLimits<float>::Max, NumLimits<float>::Max, NumLimits<float>::Max };
            for (size_t i = 0; i < 3; i++)
            {
                const auto sPt0 = camera.worldToScreen(_mAxes[i * 2 + 0]);
                const auto sPt1 = camera.worldToScreen(_mAxes[i * 2 + 1]);
                const auto rPt = DSegment3::ClosestPointToPoint(mouse, DVec3(sPt0), DVec3(sPt1));
                dist[i] = DVec3::Distance(rPt, mouse);
            }
            int idx = 0;
            if (dist[1] < dist[idx])
                idx = 1;
            if (dist[2] < dist[idx])
                idx = 2;

            if (dist[idx] < distMax)
            {
                switch (idx)
                {
                case 0:
                {
                    return WDEditAxisMoveRotate::A_MX;
                }
                break;
                case 1:
                {
                    return WDEditAxisMoveRotate::A_MY;
                }
                break;
                case 2:
                {
                    return WDEditAxisMoveRotate::A_MZ;
                }
                break;
                default:
                    break;
                }
            }
        }
        // 2. 再拾取移动轴的面边线
        {
            real dist[3] = { NumLimits<float>::Max, NumLimits<float>::Max, NumLimits<float>::Max };
            for (size_t i = 0; i < 3; i++)
            {
                const auto sPt0 = camera.worldToScreen(_mPlaneAxes[i * 3 + 0]);
                const auto sPt1 = camera.worldToScreen(_mPlaneAxes[i * 3 + 1]);
                const auto sPt2 = camera.worldToScreen(_mPlaneAxes[i * 3 + 2]);

                const auto rPt0 = DSegment3::ClosestPointToPoint(mouse, DVec3(sPt0), DVec3(sPt1));
                const auto rPt1 = DSegment3::ClosestPointToPoint(mouse, DVec3(sPt1), DVec3(sPt2));
                dist[i] = Min(DVec3::Distance(rPt0, mouse), DVec3::Distance(rPt1, mouse));
            }

            int minIdx = 0;
            if (dist[1] < dist[minIdx])
                minIdx = 1;
            if (dist[2] < dist[minIdx])
                minIdx = 2;

            if (dist[minIdx] < distMax)
            {
                switch (minIdx)
                {
                case 0:
                {
                    return WDEditAxisMoveRotate::A_MXY;
                }
                break;
                case 1:
                {
                    return WDEditAxisMoveRotate::A_MYZ;
                }
                break;
                case 2:
                {
                    return WDEditAxisMoveRotate::A_MZX;
                }
                break;
                default:
                    break;
                }
            }
        }
        // 3. 再拾取旋转轴的弧线
        {
            real dist[6] = { NumLimits<float>::Max
                , NumLimits<float>::Max
                , NumLimits<float>::Max
                , NumLimits<float>::Max
                , NumLimits<float>::Max
                , NumLimits<float>::Max };

            for (size_t i = 0; i < 6; i++)
            {
                const auto& arc = _rAxesArc[i];
                if (arc.empty())
                    continue;
                for (int j = 0; j < (arc.size() - 1); j += 2)
                {
                    const auto sPt0 = camera.worldToScreen(arc[j + 0]);
                    const auto sPt1 = camera.worldToScreen(arc[j + 1]);
                    const auto rPt = DSegment3::ClosestPointToPoint(mouse, DVec3(sPt0), DVec3(sPt1));
                    const auto td = DVec3::Distance(rPt, mouse);
                    if (td < dist[i])
                        dist[i] = td;
                }
            }

            int minIdx = 0;
            for (int i = 1; i < 6; ++i)
            {
                if (dist[i] < dist[minIdx])
                    minIdx = i;
            }

            if (dist[minIdx] < distMax)
            {
                switch (minIdx)
                {
                case 0: //XOY part A
                {
                    _axisArcPart = EAxisArcPart::AAP_XYA;
                    return WDEditAxisMoveRotate::A_RZ;
                }
                break;
                case 1: // XOY part B
                {
                    _axisArcPart = EAxisArcPart::AAP_XYB;
                    return WDEditAxisMoveRotate::A_RZ;
                }
                break;
                case 2:  // YOZ part A
                {
                    _axisArcPart = EAxisArcPart::AAP_YZA;
                    return WDEditAxisMoveRotate::A_RX;
                }
                break;
                case 3: // YOZ part B
                {
                    _axisArcPart = EAxisArcPart::AAP_YZB;
                    return WDEditAxisMoveRotate::A_RX;
                }
                break;
                case 4: // ZOX part A
                {
                    _axisArcPart = EAxisArcPart::AAP_ZXA;
                    return WDEditAxisMoveRotate::A_RY;
                }
                break;
                case 5: // ZOX part B
                {
                    _axisArcPart = EAxisArcPart::AAP_ZXB;
                    return WDEditAxisMoveRotate::A_RY;
                }
                break;
                default:
                    _axisArcPart = EAxisArcPart::AAP_None;
                    break;
                }
            }
        }

        // 没有拾取到任何轴
        return  WDEditAxisMoveRotate::A_Null;
    }

    // 使用步长重新计算长度
    inline double lengthWidthStep(double len) const
    {
        int r = static_cast<int>(len / _stepLength);
        return _stepLength * static_cast<double>(r);
    }
    // 使用步长重新计算角度
    inline double angleWidthStep(double angle) const
    {
        if (angle < 0.0)
        {
            int r = static_cast<int>(angle / _stepAngle - 0.5);
            return _stepAngle * static_cast<double>(r);
        }
        else
        {
            int r = static_cast<int>(angle / _stepAngle + 0.5);
            return _stepAngle * static_cast<double>(r);
        }
    }
private:
    static std::optional<double> Move(WDContext& context
        , const WDEditAxisMoveRotate& d
        , const DVec3& axis
        , const IVec2& start
        , const IVec2& end)
    {
        if (start == end)
            return 0.0;

        WDCamera& camera = context.camera();

        DVec3 faceNor = camera.frontDir();
        if (!d.calcAxisPlaneNormalize(camera, axis, faceNor))
            return std::nullopt;

        DRay rayStart = camera.createRayFromScreen(DVec2(start), camera.viewSizeT<int>());
        DRay rayEnd = camera.createRayFromScreen(DVec2(end), camera.viewSizeT<int>());

        DPlane plane(faceNor, d.position());
        auto r0 = rayStart.intersect(plane);
        auto r1 = rayEnd.intersect(plane);

        if (r0.first && r1.first)
        {
            DVec3 retPt0 = rayStart.at(r0.second);
            DVec3 retPt1 = rayEnd.at(r1.second);

            DVec3 retV = retPt1 - retPt0;
            real len = DVec3::Dot(axis, retV);

            return len;
        }
        return std::nullopt;
    }

    static std::optional<std::pair<double, double> > PlaneMove(WDContext& context
        , const WDEditAxisMoveRotate& d
        , const DVec3& axisA
        , const DVec3& axisB
        , const IVec2& start, const IVec2& end)
    {
        if (start == end) 
            return std::make_pair(0.0, 0.0);

        WDCamera& camera = context.camera();

        DVec3 faceNor   = DVec3::Cross(axisA, axisB).normalized();

        DRay rayStart   = camera.createRayFromScreen(DVec2(start), camera.viewSizeT<int>());
        DRay rayEnd     = camera.createRayFromScreen(DVec2(end), camera.viewSizeT<int>());

        DPlane plane(faceNor, d.position());
        auto r0 = rayStart.intersect(plane);
        auto r1 = rayEnd.intersect(plane);
        if (r0.first && r1.first)
        {
            DVec3 retPt0    = rayStart.at(r0.second);
            DVec3 retPt1    = rayEnd.at(r1.second);
            DVec3 retV      = retPt1 - retPt0;

            real xLen = DVec3::Dot(retV, axisA);
            real yLen = DVec3::Dot(retV, axisB);

            return std::make_pair(xLen, yLen);
        }

        return std::nullopt;
    }

    static std::optional<double> Rotate(WDContext& context
        , const WDEditAxisMoveRotate& d
        , const DVec3& axis
        , const IVec2& start, const IVec2& end)
    {
        if (start == end)
            return 0.0;

        WDCamera& camera = context.camera();

        DVec3 faceNor   = axis.normalized();

        DRay rayStart   = camera.createRayFromScreen(DVec2(start), camera.viewSizeT<int>());
        DRay rayEnd     = camera.createRayFromScreen(DVec2(end), camera.viewSizeT<int>());

        DPlane plane(faceNor, d.position());
        auto r0 = rayStart.intersect(plane);
        auto r1 = rayEnd.intersect(plane);
        if (r0.first && r1.first)
        {
            DVec3 retPt0 = rayStart.at(r0.second);
            DVec3 retPt1 = rayEnd.at(r1.second);

            DVec3 v0 = retPt0 - d.position();
            DVec3 v1 = retPt1 - d.position();

            if (v0.lengthSq() <= NumLimits<float>::Epsilon
                || v1.lengthSq() <= NumLimits<float>::Epsilon)
                return std::nullopt;

            auto rAngle = DVec3::Angle(v0, v1);

            if (DVec3::Dot(axis, DVec3::Cross(v0, v1)) < 0.0) 
                rAngle = -rAngle;

            return rAngle;
        }

        return std::nullopt;
    }


    static std::vector<FVec3> ScreenRect(const WDCamera& camera, const DVec3& pos, const DVec2& size, double angle = 45.0) 
    {
        Quat quat = Quat::Rotate(angle, camera.frontDir());
        const auto rightDir = quat * camera.rightDir(); 
        const auto upDir = quat * camera.upDir();
        return 
        {
              FVec3(pos + rightDir * size.x + upDir * size.y)
            , FVec3(pos - rightDir * size.x + upDir * size.y)
            , FVec3(pos - rightDir * size.x - upDir * size.y)
            , FVec3(pos + rightDir * size.x - upDir * size.y)
        };
    }
};

WDEditAxisMoveRotate::WDEditAxisMoveRotate(): WDEditAxis(EAT_MoveRotate)
{
    _p = new WDEditAxisMoveRotatePrivate(*this);
}
WDEditAxisMoveRotate::~WDEditAxisMoveRotate()
{
    delete _p;
    _p = nullptr;
}

WDEditAxisMoveRotate::MDelegate& WDEditAxisMoveRotate::mDelegate()
{
    return _p->_delegate;
}
WDEditAxisMoveRotate::MContextMenuDelegate& WDEditAxisMoveRotate::mContextMenuDelegate()
{
    return _p->_cxtMenuDelegate;
}
WDEditAxisMoveRotate::MClickedDelegate& WDEditAxisMoveRotate::mClickedDelegate()
{
    return _p->_clickedDelegate;
}
WDEditAxisMoveRotate::MDBClickedDelegate& WDEditAxisMoveRotate::mDBClickedDelegate()
{
    return _p->_dbClickedDelegate;
}

void WDEditAxisMoveRotate::setStepLength(double step)
{
    _p->_stepLength = step;
}
double WDEditAxisMoveRotate::stepLength()
{
    return _p->_stepLength;
}

void WDEditAxisMoveRotate::setStepAngle(double step)
{
    _p->_stepAngle = step;
}
double WDEditAxisMoveRotate::stepAngle()
{
    return _p->_stepAngle;
}

WDEditAxisMoveRotate::Axis WDEditAxisMoveRotate::hoveredAxis() const
{
    return _p->_hoveredAxis;
}
WDEditAxisMoveRotate::Axis WDEditAxisMoveRotate::selectedAxis() const
{
    return _p->_selectedAxis;
}
WDEditAxisMoveRotate::AxisArcPart WDEditAxisMoveRotate::axisArcPart() const
{
    return _p->_axisArcPart;
}

void WDEditAxisMoveRotate::cancelHovered()
{
    Axis oldHovered = _p->_hoveredAxis;
    _p->_hoveredAxis = WDEditAxisMoveRotate::Axis::A_Null;
    if (oldHovered != _p->_hoveredAxis)
    {
        this->sendHoveredDelegate();
    }
}
void WDEditAxisMoveRotate::cancelSelected()
{
    Axis oldSelected = _p->_selectedAxis;
    _p->_selectedAxis = WDEditAxisMoveRotate::Axis::A_Null;
    if (oldSelected != _p->_selectedAxis)
    {
        this->sendSelectedDelegate();
    }
}

void WDEditAxisMoveRotate::execMove(const DVec3& moveOffset)
{
    if (moveOffset.lengthSq() <= NumLimits<double>::Epsilon)
        return ;

    _p->_delegate(EditStatus::ES_Command, HandleType::HT_Move, DVec4(0.0), DVec4(moveOffset.normalized(), moveOffset.length()), *this);
}

void WDEditAxisMoveRotate::exec(Axis axis, double offset)
{
    HandleType hType = HandleType::HT_None;
    DVec4 offV = DVec4::Zero();
    switch (axis)
    {
    case WD::WDEditAxisMoveRotate::A_MX:
        {
            offV = DVec4(this->axisX(), offset);
            hType = HandleType::HT_Move;
        }
        break;
    case WD::WDEditAxisMoveRotate::A_MY:
        {
            offV = DVec4(this->axisY(), offset);
            hType = HandleType::HT_Move;
        }
        break;
    case WD::WDEditAxisMoveRotate::A_MZ:
        {
            offV = DVec4(this->axisZ(), offset);
            hType = HandleType::HT_Move;
        }
        break;
    case WD::WDEditAxisMoveRotate::A_MXY:
        {
            offV = DVec4(DVec3::Normalize(this->axisX() + this->axisY()), offset);
            hType = HandleType::HT_Move;
        }
        break;
    case WD::WDEditAxisMoveRotate::A_MYZ:
        {
            offV = DVec4(DVec3::Normalize(this->axisY() + this->axisZ()), offset);
            hType = HandleType::HT_Move;
        }
        break;
    case WD::WDEditAxisMoveRotate::A_MZX:
        {
            offV = DVec4(DVec3::Normalize(this->axisZ() + this->axisX()), offset);
            hType = HandleType::HT_Move;
        }
        break;
    case WD::WDEditAxisMoveRotate::A_RX:
        {
            offV = DVec4(this->axisX(), offset);
            hType = HandleType::HT_Rotate;
        }
        break;
    case WD::WDEditAxisMoveRotate::A_RY:
        {
            offV = DVec4(this->axisY(), offset);
            hType = HandleType::HT_Rotate;
        }
        break;
    case WD::WDEditAxisMoveRotate::A_RZ:
        {
            offV = DVec4(this->axisZ(), offset);
            hType = HandleType::HT_Rotate;
        }
        break;
    default:
        break;
    }

    if (offV.lengthSq() <= NumLimits<double>::Epsilon)
        return ;

    _p->_delegate(EditStatus::ES_Command, hType, DVec4(0.0), offV, *this);
}

void WDEditAxisMoveRotate::beginPreviewAlign(Axis axis, AxisArcPart axisArcPart)
{
    _p->_mOffset            = DVec3::Zero();
    _p->_previewHoveredAxis = axis;
    _p->_previewAxisArcPart = axisArcPart;
    _p->_previewHandleType  = WDEditAxisMoveRotatePrivate::HandleType(axis);

    _p->_previewBaseOri = this->position();
    _p->_previewBaseX   = this->axisX();
    _p->_previewBaseY   = this->axisY();
    _p->_previewBaseZ   = this->axisZ();

}
DVec4 WDEditAxisMoveRotate::calcPreviewAlign(const std::optional<DVec3>& vec)
{
    _p->_mOffset    = DVec3::Zero();
    _p->_rAngle     = 0.0;

    if (_p->_previewHoveredAxis == Axis::A_Null)
        return DVec4(this->axisX(), 0.0);

    const auto& aCen    = _p->_previewBaseOri;
    const auto& aX      = _p->_previewBaseX;
    const auto& aY      = _p->_previewBaseY;
    const auto& aZ      = _p->_previewBaseZ;

    std::optional<DVec3> tX = std::nullopt;
    std::optional<DVec3> tY = std::nullopt;
    std::optional<DVec3> tZ = std::nullopt;

    std::optional<DVec3> axis       = std::nullopt;
    std::optional<DVec3> basicDir   = std::nullopt;

    DVec3 defaultAxis = aX;
    switch (_p->_previewHoveredAxis)
    {
    case A_MX:
        {
            tX = aX;
            defaultAxis = aX;
        }
        break;
    case A_MY:
        {
            tY = aY;
            defaultAxis = aY;
        }
        break;
    case A_MZ:
        {
            tZ = aZ;
            defaultAxis = aZ;
        }
        break;
    case A_MXY:
        {
            tX = aX;
            tY = aY;
            defaultAxis = aX;
        }
        break;
    case A_MYZ:
        {
            tY = aY;
            tZ = aZ;
            defaultAxis = aY;
        }
        break;
    case A_MZX:
        {
            tZ = aZ;
            tX = aX;
            defaultAxis = aZ;
        }
        break;
    case A_RX:
    case A_RY:
    case A_RZ:
        {
            switch (_p->_previewAxisArcPart)
            {
            case WD::WDEditAxisMoveRotate::AAP_XYA:
                {
                    basicDir = aX;
                    axis = aZ;
                    defaultAxis = aZ;
                }
                break;
            case WD::WDEditAxisMoveRotate::AAP_XYB:
                {
                    basicDir = aY;
                    axis = aZ;
                    defaultAxis = aZ;
                }
                break;
            case WD::WDEditAxisMoveRotate::AAP_YZA:
                {
                    basicDir = aY;
                    axis = aX;
                    defaultAxis = aX;
                }
                break;
            case WD::WDEditAxisMoveRotate::AAP_YZB:
                {
                    basicDir = aZ;
                    axis = aX;
                    defaultAxis = aX;
                }
                break;
            case WD::WDEditAxisMoveRotate::AAP_ZXA:
                {
                    basicDir = aZ;
                    axis = aY;
                    defaultAxis = aY;
                }
                break;
            case WD::WDEditAxisMoveRotate::AAP_ZXB:
                {
                    basicDir = aX;
                    axis = aY;
                    defaultAxis = aY;
                }
                break;
            default:
                break;
            }
        }
        break;
    default:
        break;
    }

    if (!vec)
        return DVec4(defaultAxis, 0.0);

    // 位置对齐
    if (tX || tY || tZ) 
    {
        auto v = vec.value() - aCen;
        if (v.lengthSq() <= NumLimits<float>::Epsilon)
            return DVec4(defaultAxis, 0.0);

        DVec3 off = DVec3::Zero();
        if (tX)
        {
            auto d = DVec3::Dot(tX.value(), v);
            off += d * tX.value();
        }
        if (tY)
        {
            auto d = DVec3::Dot(tY.value(), v);
            off += d * tY.value();
        }
        if (tZ)
        {
            auto d = DVec3::Dot(tZ.value(), v);
            off += d * tZ.value();
        }

        auto offLen = off.length();
        if (offLen <= NumLimits<float>::Epsilon)
            return DVec4(defaultAxis, 0.0);

        _p->_mOffset = off;

        return DVec4(off.normalized(), offLen);
    }
    // 朝向对齐
    else if (axis && basicDir) 
    {
        // 如果朝向和轴平行，则不做对齐
        DPlane plane(axis.value(), DVec3::Zero());
        DVec3 tDir  = plane.project(vec.value());
        if (tDir.lengthSq() <= NumLimits<float>::Epsilon)
            return DVec4(defaultAxis, 0.0);

        tDir.normalize();
        auto angle  = DVec3::Angle(basicDir.value(), tDir);
        auto vc     = DVec3::Cross(basicDir.value(), tDir);
        if (DVec3::Dot(vc, axis.value()) <= 0.0)
            angle   = -angle;

        _p->_rAngle = angle;

        return DVec4(axis.value(), angle);
    }
    else
    {
        return DVec4(defaultAxis, 0.0);
    }
}
void WDEditAxisMoveRotate::endPreviewAlign()
{
    _p->_previewHandleType  = HT_None;
    _p->_previewHoveredAxis = A_Null;
    _p->_previewAxisArcPart = AAP_None;
    _p->_mOffset            = DVec3::Zero();
    _p->_rAngle             = 0.0;
}

bool WDEditAxisMoveRotate::mouseButtonPress(WDContext& context, const IVec2& pos, MouseButton btn)
{
    WDUnused(context);
    
    _p->_clickedDelegate(_p->_hoveredAxis, pos, btn, *this);

    switch (btn)
    {
    case WD::WDEditAxis::MB_None:
        break;
    case WD::WDEditAxis::MB_Left:
        {
            _p->_startMousePos      = pos;
            _p->_currentMousePos    = pos;
            _p->_bMouseDown         = true;
            _p->_handleType         = HandleType::HT_None;

            Axis oldSelected    = _p->_selectedAxis;
            _p->_selectedAxis   = _p->_hoveredAxis;

            if (oldSelected != _p->_selectedAxis)
            {
                this->sendSelectedDelegate();
            }


            if (isAxisSelected())
            {
                DVec4 relativeOffset = DVec4::Zero();
                DVec4 absoluteOffset = DVec4::Zero();
                HandleType hType = HandleType::HT_None;
                if (_p->calc(context, relativeOffset, absoluteOffset, hType))
                {
                    _p->_handleType = hType;
                    _p->_delegate(EditStatus::ES_Start
                        , hType
                        , relativeOffset
                        , absoluteOffset
                        , *this);
                }
                return true;
            }

            return false;
        }
        break;
    case WD::WDEditAxis::MB_Middle:
        break;
    case WD::WDEditAxis::MB_Right:
        {
            _p->_handleType = HandleType::HT_None;
            _p->_rDownPos   = pos;
            _p->_rDownAxis  = _p->_hoveredAxis;

            if (_p->_rDownAxis != A_Null)
            {
                _p->_handleType = WDEditAxisMoveRotatePrivate::HandleType(_p->_rDownAxis);

                return true;
            }
        }
        break;
    default:
        break;
    }

    return false;
}
bool WDEditAxisMoveRotate::mouseButtonRelease(WDContext& context, const IVec2& pos, MouseButton btn)
{
    WDUnused(context);

    switch (btn)
    {
    case WD::WDEditAxis::MB_None:
        break;
    case WD::WDEditAxis::MB_Left:
        {
            bool bRet = false;
            if (isAxisSelected())
            {
                DVec4 relativeOffset    = DVec4::Zero();
                DVec4 absoluteOffset    = DVec4::Zero();
                HandleType hType        = HandleType::HT_None;
                if (_p->calc(context, relativeOffset, absoluteOffset, hType))
                {
                    _p->_delegate(EditStatus::ES_End
                        , hType
                        , relativeOffset
                        , absoluteOffset
                        , *this);
                }

                _p->_mOffset = DVec3::Zero();
                _p->_rAngle = 0.0;

                Axis oldSelected = _p->_selectedAxis;
                _p->_selectedAxis = A_Null;
                if (oldSelected != _p->_selectedAxis)
                {
                    this->sendSelectedDelegate();
                }

                bRet = true;
            }

            _p->_bMouseDown         = false;
            _p->_startMousePos      = pos;
            _p->_currentMousePos    = pos;
            _p->_handleType         = HandleType::HT_None;

            return bRet;
        }
        break;
    case WD::WDEditAxis::MB_Middle:
        break;
    case WD::WDEditAxis::MB_Right:
        {
            // 右键菜单
            auto tRDownAxis = _p->_rDownAxis;
            _p->_rDownAxis  = A_Null;
            _p->_handleType = HandleType::HT_None;
            if (DVec2::Distance(DVec2(_p->_rDownPos), DVec2(pos)) <= 8.0
                && tRDownAxis != A_Null)
            {
                _p->_cxtMenuDelegate(tRDownAxis, pos, *this);

                return true;
            }
        }
        break;
    default:
        break;
    }

    return false;
}
bool WDEditAxisMoveRotate::mouseMove(WDContext& context, const IVec2& pos)
{
    bool bRet = false;
    if (isAxisSelected())
    {
        _p->_currentMousePos = pos;
        DVec4 relativeOffset = DVec4::Zero();
        DVec4 absoluteOffset = DVec4::Zero();
        HandleType hType = HandleType::HT_None;
        if (_p->calc(context, relativeOffset, absoluteOffset, hType))
        {
            _p->_delegate(EditStatus::ES_Editting
                , hType
                , relativeOffset
                , absoluteOffset
                , *this);
        }

        bRet = true;
    }
    else if (_p->_rDownAxis != A_Null)
    {
        bRet = true;
    }
    else if (!_p->_bMouseDown)
    {
        _p->hoverAxis(context, pos);

        bRet = false;
    }

    return bRet;
}
bool WDEditAxisMoveRotate::mouseDoubleClicked(WDContext& context, const IVec2& pos, MouseButton btn)
{
    WDUnused(context);

    _p->_dbClickedDelegate(_p->_hoveredAxis, pos, btn, *this);

    return (_p->_hoveredAxis != Axis::A_Null);
}

void WDEditAxisMoveRotate::update(WDContext& context)
{
    _p->update(context);

}
void WDEditAxisMoveRotate::render(WDContext& context)
{
    if (!_internalFlags.hasFlag(InteralFlag_Visible))
        return;
    _p->render(context);
}

WD_NAMESPACE_END
