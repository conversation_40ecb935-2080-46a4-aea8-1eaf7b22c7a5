#include "WDBMCommon.h"
#include "../message/WDMessage.h"
#include "typeMgr/WDBMTypeMgr.h"
#include "../WDCore.h"
#include "WDRapidxml.h"
#include "../node/WDNode.h"
#include "typeMgr/private/WDBMTypeRegistHelpter.h"


WD_NAMESPACE_BEGIN;


WD_API  const char* ObstructionLevelTypeToString(WDBMObstructionLevelType type)
{
    switch (type)
    {
    case WDBMObstructionLevelType::BMOL_None:
        return "None";
        break;
    case WDBMObstructionLevelType::BMOL_Soft:
        return "Soft";
        break;
    case WDBMObstructionLevelType::BMOL_Hard:
        return "Hard";
        break;
    default:
        break;
    }
    return "None";
}
WD_API WDBMObstructionLevelType ObstructionLevelTypeFromString(const char* str)
{
    if (_stricmp(str, "None") == 0)
        return WDBMObstructionLevelType::BMOL_None;
    else if (_stricmp(str, "Soft") == 0)
        return WDBMObstructionLevelType::BMOL_Soft;
    else if (_stricmp(str, "Hard") == 0)
        return WDBMObstructionLevelType::BMOL_Hard;
    else
        return WDBMObstructionLevelType::BMOL_None;
}


/********************************     解析类型节点XML配置文件   **************************************/
// 根节点
const char* XMLNode_Root                    = "Root";
// 节点类型
const char* XMLNode_Type                    = "Type";
// 类型名称
const char* XMLNode_TypeName                = "Name";
// 类型父节点类型列表
const char* XMLNode_TypeParents             = "Parents";
// 类型标志列表
const char* XMLNode_TypeFlags               = "Flags";
// 类型的变换类型
const char* XMLNode_TypeTransformType       = "TransformType";
// 类型属性列表
const char* XMLNode_TypeAttrs               = "Attributes";
// 类型属性列表中指定的系统属性
const char* XMLAttr_TypeAttrsSystem         = "system";
// 类型属性项
const char* XMLNode_TypeAttr                = "Attribute";
// 类型描述
const char* XMLNode_TypeDescription         = "Description";
// 类型资源列表
const char* XMLNode_TypeResources           = "Resources";
// 类型资源项
const char* XMLNode_TypeResource            = "Resource";
// 类型资源类型
const char* XMLAttr_TypeResourceType        = "type";
// 类型资源值
const char* XMLAttr_TypeResourceValue       = "value";

// 自定义类型的参考类型
const char* XMLNode_TypeBaseType            = "BaseType";
// 自定义类型的子节点类型列表
const char* XMLNode_TypeMembers             = "Members";
// 自定义类型的隐藏属性列表
const char* XMLNode_TypeHiddenAttributes    = "HiddenAttributes";
// 自定义类型的自定义属性列表
const char* XMLNode_TypeCustomAttributes    = "CustomAttributes";

// 属性名称
const char* XMLAttr_AttrName                = "name";
// 属性名称简称
const char* XMLAttr_AttrSampleName          = "sampleName";
// 属性值类型
const char* XMLAttr_AttrType                = "type";
// 属性类别
const char* XMLAttr_AttrCategory            = "category";
// 属性标记
const char* XMLAttr_AttrFlags               = "flags";
// 属性默认值
const char* XMLAttr_AttrDefault             = "defaultValue";
// 最小值
const char* XMLAttr_AttrMinValue            = "minValue";
// 最大值
const char* XMLAttr_AttrMaxValue            = "maxValue";
// 步长
const char* XMLAttr_AttrStep                = "step";
// 精度
const char* XMLAttr_AttrDecimals            = "decimals";
// 字典名称
const char* XMLAttr_AttrDictionaryName      = "dictionaryName";
// 正则表达式
const char* XMLAttr_AttrRegexp              = "regexp";
// 引用属性引用节点所属模块的名称
const char* XMLAttr_AttrRefNodeModuleName   = "refNodeModuleName";
// 属性描述的数据标志
const char* XMLAttr_AttrDataFlags           = "dataFlags";

void ConnectionsAttrRegist(WDBMTypeDesc& typeDesc)
{
    //连接属性  "Connections"
    typeDesc.add("Connections", WDBMAttrValueType::T_NodeRefs
        , [](const WDNode& node)  -> WDBMNodeRefs
        {
            auto pTypeDesc = node.getTypeDesc();
            if (pTypeDesc == nullptr)
                return WDBMNodeRefs();

            WDBMNodeRefs rConns;
            WDBMNodeRef tRef;
            for (const auto& attrDesc : pTypeDesc->attrDescs())
            {
                if (!attrDesc->dataFlags().hasFlag(WDBMAttrDesc::DataFlag::DF_DConnections))
                    continue;
                if (!attrDesc->value(node).toNodeRef(tRef))
                    continue;
                rConns.push_back(tRef);
            }
            return rConns;
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , WDBMAttrDesc::Flags(WDBMAttrDesc::F_Hidden | WDBMAttrDesc::F_ReadOnly));
}

class WDTypeConfigIO
{
public:
    WDTypeConfigIO()
    {
        _sysAttrs["Connections"] = SysAttr("Connections", ConnectionsAttrRegist, false);
    }
    ~WDTypeConfigIO()
    {
    }
public:
    bool load(const char* fileName, WDBMTypeMgr& typeMgr)
    {
        WDFileReader file(fileName);
        if (file.isBad())
            return false;

        file.readAll();
        XMLDoc doc;
        doc.parse<0>((char*)file.data());

        XMLNode* pXmlNodeRoot = doc.first_node(XMLNode_Root);
        if (pXmlNodeRoot == nullptr)
            return false;

        // 这里存储一下xml中配置的类型，用来校验是否配置重复
        std::set<std::string> cfgTypes;
        //  保存USER节点（万能节点）的父节点的vec
        StringVector userParentsStrVec;
        for (XMLNode* pXmlNodeType = pXmlNodeRoot->first_node(XMLNode_Type)
            ; pXmlNodeType != nullptr
            ; pXmlNodeType = pXmlNodeType->next_sibling())
        {
            // 类型节点名称
            XMLNode* pXmlNodeName = pXmlNodeType->first_node(XMLNode_TypeName);
            if (pXmlNodeName == nullptr)
            {
                assert(false && "无效的类型名称!");
                continue;
            }
            // 注册类型节点描述
            std::string typeName = pXmlNodeName->value();
            if (typeName.empty())
            {
                assert(false && "无效的类型名称!");
                continue;
            }
            assert(cfgTypes.find(typeName) == cfgTypes.end() && "注意: 类型配置重复!");
            // 添加
            cfgTypes.insert(typeName);
            // 先获取类型，如果获取不到，再注册
            WDBMTypeDesc* pTypeDesc = typeMgr.get(typeName);
            if (pTypeDesc == nullptr)
                pTypeDesc = typeMgr.regist(typeName);

            if (pTypeDesc == nullptr)
            {
                assert(false && "类型注册失败!");
                continue;
            }
            //  USER可以挂载在任意节点下
            userParentsStrVec.push_back(typeName);

            // 支持挂载到的父类型
            XMLNode* pXmlNodeParent = pXmlNodeType->first_node(XMLNode_TypeParents);
            if (pXmlNodeParent != nullptr)
            {
                auto parentNames = StrSpiltWithSpace(pXmlNodeParent->value());
                //  WORL是根节点，根节点父节点为空
                if (typeName != "WORL")
                    parentNames.push_back("USER");
                if (!parentNames.empty())
                    pTypeDesc->setParentTypes(parentNames);
            }
            // 类型的标记列表
            XMLNode* pXmlNodeFlags = pXmlNodeType->first_node(XMLNode_TypeFlags);
            if (pXmlNodeFlags != nullptr)
            {
                auto strFlags = StrSpiltWithSpace(pXmlNodeFlags->value());
                WDBMTypeDesc::Flags flags;
                for (const auto& strFlag : strFlags)
                {
                    if (strFlag.empty())
                        continue;
                    flags.addFlag(WDBMTypeDesc::FlagFromStr(strFlag));
                }
                pTypeDesc->setFlags(flags);
            }
            // 类型的变换属性类型
            XMLNode* pXmlNodeTsfmType = pXmlNodeType->first_node(XMLNode_TypeTransformType);
            if (pXmlNodeTsfmType != nullptr)
            {
                auto tsfmType = WDBMTypeDesc::TransformTypeFromStr(pXmlNodeTsfmType->value());
                pTypeDesc->setTransformType(tsfmType);
            }

            // 默认属性模板列表
            XMLNode* pXmlNodeAttrs = pXmlNodeType->first_node(XMLNode_TypeAttrs);
            if (pXmlNodeAttrs != nullptr)
            {
                // 系统属性列表
                StringVector sysAttrNames;
                auto pXmlAttrSysAttrs = pXmlNodeAttrs->first_attribute(XMLAttr_TypeAttrsSystem);
                if (pXmlAttrSysAttrs != nullptr)
                {
                    sysAttrNames = StrSpiltWithSpace(pXmlAttrSysAttrs->value());
                }
                // 处理挂载在所有属性之前的系统属性列表
                for (const auto& sysAttrName : sysAttrNames)
                {
                    auto fItr = _sysAttrs.find(sysAttrName);
                    if (fItr == _sysAttrs.end())
                    {
                        assert(false && "无效的系统类型名称");
                        continue;
                    }
                    if (!fItr->second.bFront)
                        continue;
                    // 调用注册函数
                    fItr->second.registFunc(*pTypeDesc);
                }
                // 根据变换类型，注册相关的变换属性
                auto tsfmType = pTypeDesc->transformType();
                switch (tsfmType)
                {
                case WD::WDBMTypeDesc::TT_Pos:
                {
                    PosAttrRegistHelpter<WDBMTTsfm>(*pTypeDesc);
                }
                break;
                case WD::WDBMTypeDesc::TT_PosOri:
                {
                    PosAttrRegistHelpter<WDBMTRTsfm>(*pTypeDesc);
                    OriAttrRegistHelpter<WDBMTRTsfm>(*pTypeDesc);
                }
                break;
                case WD::WDBMTypeDesc::TT_PosOriScale:
                {
                    PosAttrRegistHelpter<WDBMTRSTsfm>(*pTypeDesc);
                    OriAttrRegistHelpter<WDBMTRSTsfm>(*pTypeDesc);
                    ScaleAttrRegistHelpter<WDBMTRSTsfm>(*pTypeDesc);
                }
                break;
                case WD::WDBMTypeDesc::TT_SEPosDrnBangle:
                {
                    BAngleAttrRegistHelpter<WDBMSEPosDrnBangleTsfm>(*pTypeDesc);
                    SEPosAttrRegistHelpter<WDBMSEPosDrnBangleTsfm>(*pTypeDesc);
                    SEDrnAttrRegistHelpter<WDBMSEPosDrnBangleTsfm>(*pTypeDesc);
                }
                break;
                case WD::WDBMTypeDesc::TT_SEDrn:
                {
                    SEDrnAttrRegistHelpter<WDBMSEDrnTsfm>(*pTypeDesc);
                }
                break;
                case WD::WDBMTypeDesc::TT_HTPosDir:
                {
                    HTPosAttrRegistHelpter<WDBMHTPosDirTsfm>(*pTypeDesc);
                    HTDirAttrRegistHelpter<WDBMHTPosDirTsfm>(*pTypeDesc);
                }
                break;
                case WD::WDBMTypeDesc::TT_HTPos:
                {
                    HTPosAttrRegistHelpter<WDBMHTPosTsfm>(*pTypeDesc);
                }
                break;
                case WD::WDBMTypeDesc::TT_PosSjustBangle:
                {
                    BAngleAttrRegistHelpter<WDBMPosSjustBangleTsfm>(*pTypeDesc);
                    PosAttrRegistHelpter<WDBMPosSjustBangleTsfm>(*pTypeDesc);
                }
                break;
                case WD::WDBMTypeDesc::TT_DelposPoslineZdisBangle:
                {
                    BAngleAttrRegistHelpter<WDBMDelposPoslineZdisBangleTsfm>(*pTypeDesc);
                    DelpositionAttrRegistHelpter<WDBMDelposPoslineZdisBangleTsfm>(*pTypeDesc);
                }
                break;
                case WDBMTypeDesc::TT_DelposZdirBangle:
                {
                }
                break;
                default:
                    break;
                }

                // 处理配置的属性列表
                for (XMLNode* pXmlNodeAttr = pXmlNodeAttrs->first_node();
                    pXmlNodeAttr != nullptr;
                    pXmlNodeAttr = pXmlNodeAttr->next_sibling())
                {
                    const char* nodeName = pXmlNodeAttr->name();
                    //属性节点
                    if (_strcmpi(nodeName, XMLNode_TypeAttr) == 0)
                    {
                        if (!parseAttrDesc(*pTypeDesc, pXmlNodeAttr))
                        {
                            assert(false && "配置属性解析失败!");
                        }
                    }
                }
                // 处理挂载在所有属性之后的系统属性列表
                for (const auto& sysAttrName : sysAttrNames)
                {
                    auto fItr = _sysAttrs.find(sysAttrName);
                    if (fItr == _sysAttrs.end())
                        continue;
                    if (fItr->second.bFront)
                        continue;
                    // 调用注册函数
                    fItr->second.registFunc(*pTypeDesc);
                }
            }
            // 类型说明
            XMLNode* pXmlNodeDesc = pXmlNodeType->first_node(XMLNode_TypeDescription);
            if (pXmlNodeDesc != nullptr)
            {
                pTypeDesc->setDescription(pXmlNodeDesc->value());
            }
            // 资源列表
            XMLNode* pXmlNodeRess = pXmlNodeType->first_node(XMLNode_TypeResources);
            if (pXmlNodeRess != nullptr)
            {
                XMLNode* pXmlNodeRes = pXmlNodeRess->first_node(XMLNode_TypeResource);
                if (pXmlNodeRes != nullptr)
                {
                    XMLAttr* pAttrResName = pXmlNodeRes->first_attribute(XMLAttr_TypeResourceType);
                    XMLAttr* pAttrResValue = pXmlNodeRes->first_attribute(XMLAttr_TypeResourceValue);
                    if (pAttrResName == nullptr || pAttrResValue == nullptr)
                        continue;
                    pTypeDesc->addResource(pAttrResName->value(), pAttrResValue->value());
                }
            }
        }
        if (!userParentsStrVec.empty())
        {
            //  设置USER节点的父节点
            auto  pUSERNodeDesc = typeMgr.get("USER");
            if (pUSERNodeDesc != nullptr)
                pUSERNodeDesc->setParentTypes(userParentsStrVec);
        }
        return true;
    }
public:
    // 使用空格分割字符串
    static StringVector StrSpiltWithSpace(const std::string& str)
    {
        if (str.empty())
            return {};

        // 解析结果
        std::smatch smatch;
        // 匹配轴规则(匹配长度至少为1的空格)
        std::regex partten("\\s{1,}");
        // 解析
        StringVector outStrs;
        std::sregex_iterator itr(str.begin(), str.end(), partten);
        for (; itr != std::sregex_iterator(); ++itr)
        {
            smatch = *itr;
            const std::string& tstr = smatch.prefix();
            if (!tstr.empty())
                outStrs.push_back(tstr);
        }

        if (outStrs.empty())
        {
            outStrs.push_back(str);
            return outStrs;
        }

        std::string tstr = smatch.suffix();
        if (!tstr.empty())
            outStrs.push_back(tstr);

        return outStrs;
    }
private:
    // 解析单个属性描述
    bool parseAttrDesc(WDBMTypeDesc& typeDesc, XMLNode* pXmlNodeAttr)
    {
        if (pXmlNodeAttr == nullptr)
        {
            return false;
        }
        //  1, 名称 和 类型 (必须有)
        XMLAttr* pAttrName = pXmlNodeAttr->first_attribute(XMLAttr_AttrName);
        if (pAttrName == nullptr)
        {
            assert(false && "需要配置属性名称!");
            return false;
        }
        std::string attrName = pAttrName->value();
        if (attrName.empty())
        {
            assert(false && "配置属性的名称空!");
            return false;
        }
        XMLAttr* pAttrType = pXmlNodeAttr->first_attribute(XMLAttr_AttrType);
        if (pAttrType == nullptr)
        {
            assert(false && "需要配置属性类型!");
            return false;
        }
        const WDBMAttrValueType attrType = WDBMAttrValue::TypeFromStr(pAttrType->value());
        if (attrType == WDBMAttrValueType::T_Null)
        {
            assert(false && "配置属性的类型不支持!");
            return false;
        }
        // 属性名称简称
        std::string attrSampleName = "";
        XMLAttr* pAttrSampleName = pXmlNodeAttr->first_attribute(XMLAttr_AttrSampleName);
        if (pAttrSampleName != nullptr)
        {
            attrSampleName = pAttrSampleName->value();
        }
        // 属性标志
        WDBMAttrDesc::Flags flags = WDBMAttrDesc::Flag::F_None;
        XMLAttr* pAttrfalgs = pXmlNodeAttr->first_attribute(XMLAttr_AttrFlags);
        if (pAttrfalgs != nullptr)
        {
            auto attrFlagStrs = StrSpiltWithSpace(pAttrfalgs->value());
            for (const auto& flagStr : attrFlagStrs)
            {
                flags.addFlag(WDBMAttrDesc::FlagFromStr(flagStr));
            }
        }
        // 添加属性描述对象
        auto pRDesc = typeDesc.add(attrName, attrType, attrSampleName, flags);
        if (pRDesc == nullptr)
        {
            assert(false && "配置属性的类型不支持!");
            return false;
        }
        // 默认值
        XMLAttr* pAttrdef = pXmlNodeAttr->first_attribute(XMLAttr_AttrDefault);
        if (pAttrdef != nullptr)
        {
            auto rDefValue = WDBMAttrValue::FromString(pRDesc->type(), pAttrdef->value());
            if (rDefValue.valid())
                pRDesc->setDefaultValue(rDefValue);
        }
        // 根据类型读取最小值，最大值，步长，精度
        switch (attrType)
        {
        case T_Int:
        case T_IntVector:
        {
            bool bOk = false;
            // 最小值
            XMLAttr* pAttrMinValue = pXmlNodeAttr->first_attribute(XMLAttr_AttrMinValue);
            if (pAttrMinValue != nullptr)
            {
                auto s = std::string(pAttrMinValue->value());
                if (!s.empty())
                {
                    auto v = FromString<int>(s, &bOk);
                    if (bOk)
                        pRDesc->setMinimumValue(v);
                }
            }
            // 最大值
            XMLAttr* pAttrMaxValue = pXmlNodeAttr->first_attribute(XMLAttr_AttrMaxValue);
            if (pAttrMaxValue != nullptr)
            {
                auto s = std::string(pAttrMaxValue->value());
                if (!s.empty())
                {
                    auto v = FromString<int>(s, &bOk);
                    if (bOk)
                        pRDesc->setMaximumValue(v);
                }
            }
            // 步长
            XMLAttr* pAttrStep = pXmlNodeAttr->first_attribute(XMLAttr_AttrStep);
            if (pAttrStep != nullptr)
            {
                auto s = std::string(pAttrStep->value());
                if (!s.empty())
                {
                    auto v = FromString<int>(s, &bOk);
                    if (bOk)
                        pRDesc->setSingleStep(v);
                }
            }
        }
        break;
        case T_Double:
        case T_DoubleVector:
        {
            bool bOk = false;
            // 最小值
            XMLAttr* pAttrMinValue = pXmlNodeAttr->first_attribute(XMLAttr_AttrMinValue);
            if (pAttrMinValue != nullptr)
            {
                auto s = std::string(pAttrMinValue->value());
                if (!s.empty())
                {
                    auto v = FromString<double>(s, &bOk);
                    if (bOk)
                        pRDesc->setMinimumValue(v);
                }
            }
            // 最大值
            XMLAttr* pAttrMaxValue = pXmlNodeAttr->first_attribute(XMLAttr_AttrMaxValue);
            if (pAttrMaxValue != nullptr)
            {
                auto s = std::string(pAttrMaxValue->value());
                if (!s.empty())
                {
                    auto v = FromString<double>(s, &bOk);
                    if (bOk)
                        pRDesc->setMaximumValue(v);
                }
            }
            // 步长
            XMLAttr* pAttrStep = pXmlNodeAttr->first_attribute(XMLAttr_AttrStep);
            if (pAttrStep != nullptr)
            {
                auto s = std::string(pAttrStep->value());
                if (!s.empty())
                {
                    auto v = FromString<double>(s, &bOk);
                    if (bOk)
                        pRDesc->setSingleStep(v);
                }
            }
            // 精度
            XMLAttr* pAttrDecimals = pXmlNodeAttr->first_attribute(XMLAttr_AttrDecimals);
            if (pAttrDecimals != nullptr)
            {
                auto s = std::string(pAttrDecimals->value());
                if (!s.empty())
                {
                    auto v = FromString<int>(s, &bOk);
                    if (bOk)
                        pRDesc->setDecimals(v);
                }
            }
        }
        break;
        default:
            break;
        }
        // 字典名称
        XMLAttr* pAttrDictionaryName = pXmlNodeAttr->first_attribute(XMLAttr_AttrDictionaryName);
        if (pAttrDictionaryName != nullptr)
        {
            auto rDictName = pAttrDictionaryName->value();
            pRDesc->setEnumDictionaryName(rDictName);
        }
        // 正则表达式
        XMLAttr* pAttrRegexp = pXmlNodeAttr->first_attribute(XMLAttr_AttrRegexp);
        if (pAttrRegexp != nullptr)
        {
            auto rRegexp = pAttrRegexp->value();
            pRDesc->setRegexp(rRegexp);
        }
        // 引用节点所属的模块名称
        XMLAttr* pAttrRefNodeModuleName = pXmlNodeAttr->first_attribute(XMLAttr_AttrRefNodeModuleName);
        if (pAttrRefNodeModuleName != nullptr)
        {
            auto rName = pAttrRefNodeModuleName->value();
            pRDesc->setRefNodeModuleName(rName);
        }
        // 属性的数据标志
        XMLAttr* pAttrDataFlags = pXmlNodeAttr->first_attribute(XMLAttr_AttrDataFlags);
        if (pAttrDataFlags != nullptr)
        {
            WDBMAttrDesc::DataFlags dataFlags;
            StringVector dataFlagStrs = StrSpiltWithSpace(pAttrDataFlags->value());
            for (const auto& dataFlagStr : dataFlagStrs)
            {
                dataFlags.addFlag(WDBMAttrDesc::DataFlagFromStr(dataFlagStr));
            }
            pRDesc->dataFlags() = dataFlags;
        }
        return true;
    }
private:
    struct SysAttr
    {
        using RegFunc = std::function<void(WDBMTypeDesc& typeMgr)>;

        // 系统属性的名称
        std::string name;
        // 属性的注册函数
        RegFunc registFunc;
        // 是否加在所有属性之前还是加在所有属性之后
        bool bFront = false;
    public:
        SysAttr(const std::string& name = ""
            , const RegFunc& registFunc = RegFunc()
            , bool bFront = true)
            : name(name)
            , registFunc(registFunc)
            , bFront(bFront)
        {
        }
    };
    std::map<std::string, SysAttr> _sysAttrs;
};


bool ParseAttribute(WDBMTypeDesc& typeDesc, XMLNode* pXmlNodeAttr, bool bFixed = false)
{
    if (pXmlNodeAttr == nullptr)
    {
        return false;
    }
    //  1, 名称 和 类型 (必须有)
    XMLAttr* pAttrName = pXmlNodeAttr->first_attribute(XMLAttr_AttrName);
    if (pAttrName == nullptr)
    {
        assert(false && "需要配置属性名称!");
        return false;
    }
    XMLAttr* pAttrType = pXmlNodeAttr->first_attribute(XMLAttr_AttrType);
    if ( pAttrType == nullptr)
    {
        assert(false && "需要配置属性类型!");
        return false;
    }
    std::string attrName = pAttrName->value();
    if (attrName.empty())
    {
        assert(false && "配置属性的名称空!");
        return false;
    }
    const WDBMAttrValueType attrType = WDBMAttrValue::TypeFromStr(pAttrType->value());
    if (attrType == WDBMAttrValueType::T_Null)
    {
        assert(false && "配置属性的类型不支持!");
        return false;
    }
    // 属性名称简称
    std::string attrSampleName = "";
    XMLAttr* pAttrSampleName = pXmlNodeAttr->first_attribute(XMLAttr_AttrSampleName);
    if (pAttrSampleName != nullptr) 
    {
        attrSampleName = pAttrSampleName->value();
    }
    // 不是固定属性，就是自定义属性, 如果是自定义属性, 则名称前自动加自定义属性前缀
    if (!bFixed)
    {
        if (!attrName.empty())
            attrName = WDBMAttrDesc::CustomAttrNamePrefix + attrName;
        if (!attrSampleName.empty())
            attrSampleName = WDBMAttrDesc::CustomAttrNamePrefix + attrSampleName;
    }
    // 属性标志
    WDBMAttrDesc::Flags flags = WDBMAttrDesc::Flag::F_None;
    XMLAttr* pAttrfalgs = pXmlNodeAttr->first_attribute(XMLAttr_AttrFlags);
    if (pAttrfalgs != nullptr)
    {
        auto attrfalgs = pAttrfalgs->value();
        StringVector vec = StringSplit(attrfalgs, "|");
        for (const auto& each : vec)
        {
            flags.addFlag(WDBMAttrDesc::FlagFromStr(each));
        }
    }
    // 添加属性描述对象
    auto pRDesc = typeDesc.add(attrName, attrType, attrSampleName, flags);
    if (pRDesc == nullptr)
    {
        assert(false && "配置属性的类型不支持!");
        return false;
    }
    // 默认值
    XMLAttr* pAttrdef = pXmlNodeAttr->first_attribute(XMLAttr_AttrDefault);
    if (pAttrdef != nullptr)
    {
        auto rDefValue = WDBMAttrValue::FromString(pRDesc->type(), pAttrdef->value());
        if (rDefValue.valid())
            pRDesc->setDefaultValue(rDefValue);
    }
    // 根据类型读取最小值，最大值，步长，精度
    switch (attrType)
    {
    case WD::T_Int:
    case WD::T_IntVector:
    {
        bool bOk = false;
        // 最小值
        XMLAttr* pAttrMinValue = pXmlNodeAttr->first_attribute(XMLAttr_AttrMinValue);
        if (pAttrMinValue != nullptr)
        {
            auto s = std::string(pAttrMinValue->value());
            if (!s.empty())
            {
                auto v = FromString<int>(s, &bOk);
                if (bOk)
                    pRDesc->setMinimumValue(v);
            }
        }
        // 最大值
        XMLAttr* pAttrMaxValue = pXmlNodeAttr->first_attribute(XMLAttr_AttrMaxValue);
        if (pAttrMaxValue != nullptr)
        {
            auto s = std::string(pAttrMaxValue->value());
            if (!s.empty())
            {
                auto v = FromString<int>(s, &bOk);
                if (bOk)
                    pRDesc->setMaximumValue(v);
            }
        }
        // 步长
        XMLAttr* pAttrStep = pXmlNodeAttr->first_attribute(XMLAttr_AttrStep);
        if (pAttrStep != nullptr)
        {
            auto s = std::string(pAttrStep->value());
            if (!s.empty())
            {
                auto v = FromString<int>(s, &bOk);
                if (bOk)
                    pRDesc->setSingleStep(v);
            }
        }
    }
    break;
    case WD::T_Double:
    case WD::T_DoubleVector:
    {
        bool bOk = false;
        // 最小值
        XMLAttr* pAttrMinValue = pXmlNodeAttr->first_attribute(XMLAttr_AttrMinValue);
        if (pAttrMinValue != nullptr)
        {
            auto s = std::string(pAttrMinValue->value());
            if (!s.empty())
            {
                auto v = FromString<double>(s, &bOk);
                if (bOk)
                    pRDesc->setMinimumValue(v);
            }
        }
        // 最大值
        XMLAttr* pAttrMaxValue = pXmlNodeAttr->first_attribute(XMLAttr_AttrMaxValue);
        if (pAttrMaxValue != nullptr)
        {
            auto s = std::string(pAttrMaxValue->value());
            if (!s.empty())
            {
                auto v = FromString<double>(s, &bOk);
                if (bOk)
                    pRDesc->setMaximumValue(v);
            }
        }
        // 步长
        XMLAttr* pAttrStep = pXmlNodeAttr->first_attribute(XMLAttr_AttrStep);
        if (pAttrStep != nullptr)
        {
            auto s = std::string(pAttrStep->value());
            if (!s.empty())
            {
                auto v = FromString<double>(s, &bOk);
                if (bOk)
                    pRDesc->setSingleStep(v);
            }
        }
        // 精度
        XMLAttr* pAttrDecimals = pXmlNodeAttr->first_attribute(XMLAttr_AttrDecimals);
        if (pAttrDecimals != nullptr)
        {
            auto s = std::string(pAttrDecimals->value());
            if (!s.empty())
            {
                auto v = FromString<int>(s, &bOk);
                if (bOk)
                    pRDesc->setDecimals(v);
            }
        }
    }
    break;
    default:
        break;
    }
    // 字典名称
    XMLAttr* pAttrDictionaryName = pXmlNodeAttr->first_attribute(XMLAttr_AttrDictionaryName);
    if (pAttrDictionaryName != nullptr)
    {
        auto rDictName = pAttrDictionaryName->value();
        pRDesc->setEnumDictionaryName(rDictName);
    }
    // 正则表达式
    XMLAttr* pAttrRegexp = pXmlNodeAttr->first_attribute(XMLAttr_AttrRegexp);
    if (pAttrRegexp != nullptr)
    {
        auto rRegexp = pAttrRegexp->value();
        pRDesc->setRegexp(rRegexp);
    }

    return true;
}


WD_API bool LoadTypeNodeConfigFromXML(const std::string& nodeTypeXMLFileName, WD::WDBMTypeMgr& typeMgr)
{
    WD::WDFileReader    file(nodeTypeXMLFileName);
    if (file.isBad())
        return false;
    file.readAll();
    WD::XMLDoc          doc;
    doc.parse<0>((char*)file.data());

    WD::XMLNode* pXmlNodeRoot    =   doc.first_node(XMLNode_Root);
    if (pXmlNodeRoot == nullptr)
        return false;
    // 这里存储一下xml中配置的类型，用来校验是否配置重复
    std::set<std::string> cfgTypes;
    // 分割父节点类型数据字符串
    auto analysisStr = [](const std::string& string, StringVector& outStrVec)
    {
        if (string.empty())
            return;
        // 解析结果
        std::smatch smatch;
        // 匹配轴规则(匹配长度至少为1的空格)
        std::regex partten("\\s{1,}");
        // 解析
        std::sregex_iterator itr(string.begin(), string.end(), partten);
        for (; itr != std::sregex_iterator(); ++itr)
        {
            smatch = *itr;
            const std::string& str = smatch.prefix();
            if (!str.empty())
                outStrVec.push_back(str);
        }
        if (outStrVec.empty())
        {
            outStrVec.push_back(string);
            return;
        }
        const std::string& str = smatch.suffix();
        if (!str.empty())
            outStrVec.push_back(str);
    };
    //  保存USER节点（万能节点）的父节点的vec
    WD::StringVector    UserParentsStrVec;
    for (WD::XMLNode* pXmlNodeType = pXmlNodeRoot->first_node(XMLNode_Type)
        ; pXmlNodeType != nullptr
        ; pXmlNodeType = pXmlNodeType->next_sibling())
    {
        // 类型节点名称
        WD::XMLNode*    pXmlNodeName    =   pXmlNodeType->first_node(XMLNode_TypeName);
        if (pXmlNodeName == nullptr)
        {
            assert(false && "无效的类型名称!");
            continue;
        }
        // 注册类型节点描述
        std::string typeName    = pXmlNodeName->value();
        if (typeName.empty())
        {
            assert(false && "无效的类型名称!");
            continue;
        }
        assert(cfgTypes.find(typeName) == cfgTypes.end() && "注意: 类型配置重复!");
        // 添加
        cfgTypes.insert(typeName);

        WD::WDBMTypeDesc*   pTypeDesc = typeMgr.get(typeName);
        if (pTypeDesc == nullptr)
        {
            assert(false && "类型注册失败!");
            continue;
        }
        //  USER可以挂载在任意节点下
        UserParentsStrVec.push_back(typeName);
        // 支持挂载到的父类型
        WD::XMLNode*    pXmlNodeParent  =   pXmlNodeType->first_node(XMLNode_TypeParents);
        if (pXmlNodeParent != nullptr)
        {
            WD::StringVector    parentsStrs;
            analysisStr(pXmlNodeParent->value(), parentsStrs);
            //  WORL是根节点，根节点父节点为空
            if(typeName != "WORL")
                parentsStrs.push_back("USER");
            if (!parentsStrs.empty())
                pTypeDesc->setParentTypes(parentsStrs);
        }
        // 默认属性模板列表
        WD::XMLNode*    pXmlNodePtys    =   pXmlNodeType->first_node(XMLNode_TypeAttrs);
        if (pXmlNodePtys != nullptr)
        {
            for (WD::XMLNode* pXmlNodePty = pXmlNodePtys->first_node();
                pXmlNodePty != nullptr;
                pXmlNodePty = pXmlNodePty->next_sibling())
            {
                const char* nodeName = pXmlNodePty->name();
                //属性节点
                if (_strcmpi(nodeName, XMLNode_TypeAttr) == 0)
                {
                    if (!ParseAttribute(*pTypeDesc, pXmlNodePty)) 
                    {
                        WD_WARN("配置属性解析失败!");
                    }
                }
            }
        }
        // 类型说明
        WD::XMLNode*    pXmlNodeDesc    =   pXmlNodeType->first_node(XMLNode_TypeDescription);
        if (pXmlNodeDesc != nullptr)
        {
            pTypeDesc->setDescription(pXmlNodeDesc->value());
        }
        // 资源列表
        WD::XMLNode*    pXmlNodeRess    =   pXmlNodeType->first_node(XMLNode_TypeResources);
        if (pXmlNodeRess != nullptr)
        {
            WD::XMLNode*    pXmlNodeRes =   pXmlNodeRess->first_node(XMLNode_TypeResource);
            if (pXmlNodeRes != nullptr)
            {
                WD::XMLAttr*    pAttrResName    =   pXmlNodeRes->first_attribute(XMLAttr_TypeResourceType);
                WD::XMLAttr*    pAttrResValue   =   pXmlNodeRes->first_attribute(XMLAttr_TypeResourceValue);
                if (pAttrResName == nullptr || pAttrResValue == nullptr)
                    continue;
                pTypeDesc->addResource(pAttrResName->value(), pAttrResValue->value());
            }
        }
    }
    if (!UserParentsStrVec.empty())
    {
        //  设置USER节点的父节点
        auto  pUSERNodeDesc = typeMgr.get("USER");
        if (pUSERNodeDesc != nullptr)
            pUSERNodeDesc->setParentTypes(UserParentsStrVec);
    }
    return true;
}
WD_API bool LoadTypeNodeConfigFromXMLV1(const std::string& nodeTypeXMLFileName, WD::WDBMTypeMgr& typeMgr)
{
    WDTypeConfigIO cfgIO;
    return cfgIO.load(nodeTypeXMLFileName.c_str(), typeMgr);
}

WD_API void LoadProjectConfigXML(const std::string& attrCFGFilePath, const std::string& typeCFGFilePath, WD::WDBMTypeMgr& typeMgr)
{
    // 读取自定义类型
    WD::WDFileReader    file;
    if (file.open(typeCFGFilePath.c_str()))
    {
        file.readAll();
        WD::XMLDoc* doc = new WD::XMLDoc();
        doc->parse<0>((char*)file.data());
        auto pRoot = doc->first_node(XMLNode_Root);
        if (pRoot != nullptr)
        {
            for (auto pType = pRoot->first_node(XMLNode_Type); pType != nullptr; pType = pType->next_sibling(XMLNode_Type))
            {
                // 获取自定义类型名称
                auto pTypeName = pType->first_node(XMLNode_TypeName);
                if (pTypeName == nullptr)
                    continue;
                std::string typeName = pTypeName->value();
                if (typeName.empty())
                    continue;

                if (typeName.front() != ':')
                {
                    typeName.insert(typeName.begin(), ':');
                }
                // 自定义类型前面最多只能有一个 :
                else if (typeName.size() < 2 || typeName[1] == ':')
                {
                    // LOG_INFO << ;
                    continue;
                }
                // 类型名称重复
                auto pTypeDesc = typeMgr.get(typeName);
                if (pTypeDesc != nullptr)
                {
                    // LOG_INFO << ;
                    continue;
                }

                // 获取自定义类型参考类型名称
                auto pBaseTypeName = pType->first_node(XMLNode_TypeBaseType);
                if (pBaseTypeName == nullptr)
                    continue;
                std::string baseTypeName = pBaseTypeName->value();
                if (baseTypeName.empty())
                    continue;
                if (baseTypeName == "WORL")
                {
                    // LOG_INFO << ;
                    continue;
                }

                // 获取参考类型描述
                WD::WDBMTypeDesc*   pBaseTypeDesc = typeMgr.get(baseTypeName);
                if (pBaseTypeDesc == nullptr)
                {
                    // LOG_INFO << ;
                    continue;
                }

                pTypeDesc = typeMgr.registByBaseType(typeName, *pBaseTypeDesc);
                if (pTypeDesc == nullptr)
                    continue;

                // 支持挂载到的父类型
                XMLNode* pXmlNodeParent = pType->first_node(XMLNode_TypeParents);
                if (pXmlNodeParent != nullptr)
                {
                    auto parentNames = WDTypeConfigIO::StrSpiltWithSpace(pXmlNodeParent->value());
                    if (parentNames.empty())
                        continue;
                    parentNames.push_back("USER");
                    pTypeDesc->setParentTypes(parentNames);
                }
                // 可以挂载到的子类型
                XMLNode* pXmlNodeMembers = pType->first_node(XMLNode_TypeMembers);
                if (pXmlNodeMembers != nullptr)
                {
                    auto memberNames = WDTypeConfigIO::StrSpiltWithSpace(pXmlNodeMembers->value());
                    for (auto& eachMember : memberNames)
                    {
                        WD::WDBMTypeDesc* pMemberType = typeMgr.get(eachMember);
                        if (pMemberType == nullptr)
                            continue;
                        auto parentTypes = pMemberType->parentTypes();
                        parentTypes.push_back(typeName);
                        pMemberType->setParentTypes(parentTypes);
                    }
                }
                // 要隐藏的继承属性
                XMLNode* pXmlNodeHiddenAttributes = pType->first_node(XMLNode_TypeHiddenAttributes);
                if (pXmlNodeHiddenAttributes != nullptr)
                {
                    auto hiddenAttrNames = WDTypeConfigIO::StrSpiltWithSpace(pXmlNodeHiddenAttributes->value());
                    for (auto& eachHAttr : hiddenAttrNames)
                    {
                        auto pAttr = pTypeDesc->get(eachHAttr);
                        if (pAttr == nullptr)
                            continue;

                        pAttr->flags().addFlag(WDBMAttrDesc::F_Hidden);
                    }
                }

                // 默认属性模板列表
                WD::XMLNode*    pXmlNodePtys    =   pType->first_node(XMLNode_TypeCustomAttributes);
                if (pXmlNodePtys != nullptr)
                {
                    for (auto pAttribute = pXmlNodePtys->first_node(XMLNode_TypeAttr);
                        pAttribute != nullptr;
                        pAttribute = pAttribute->next_sibling(XMLNode_TypeAttr))
                    {
                        if (!ParseAttribute(*pTypeDesc, pAttribute)) 
                            WD_WARN("配置属性解析失败!");
                    }
                }

                // 类型说明
                XMLNode* pXmlNodeDesc = pType->first_node(XMLNode_TypeDescription);
                if (pXmlNodeDesc != nullptr)
                    pTypeDesc->setDescription(pXmlNodeDesc->value());
            }
        }
        file.close();
    }
    // 读取自定义属性
    if (file.open(attrCFGFilePath.c_str()))
    {
        file.readAll();
        WD::XMLDoc* doc = new WD::XMLDoc();
        doc->parse<0>((char*)file.data());
        auto pRoot = doc->first_node(XMLNode_Root);
        if (pRoot != nullptr)
        {
            for (auto pType = pRoot->first_node(XMLNode_Type); pType != nullptr; pType = pType->next_sibling(XMLNode_Type))
            {
                auto pTypeName = pType->first_node(XMLNode_TypeName);
                if (pTypeName == nullptr)
                    continue;
                std::string typeName = pTypeName->value();
                if (typeName.empty())
                    continue;
                WD::WDBMTypeDesc*   pTypeDesc = typeMgr.get(typeName);
                if (pTypeDesc == nullptr)
                {
                    assert(false && "类型获取失败!");
                    continue;
                }
                // 默认属性模板列表
                WD::XMLNode*    pXmlNodePtys    =   pType->first_node(XMLNode_TypeAttrs);
                if (pXmlNodePtys != nullptr)
                {
                    for (auto pAttribute = pXmlNodePtys->first_node(XMLNode_TypeAttr);
                        pAttribute != nullptr;
                        pAttribute = pAttribute->next_sibling(XMLNode_TypeAttr))
                    {
                        if (!ParseAttribute(*pTypeDesc, pAttribute)) 
                        {
                            WD_WARN("配置属性解析失败!");
                        }
                    }
                }
            }
        }
        file.close();
    }
}

WD_NAMESPACE_END
