#pragma     once

#include "WDBMTypeDesc.h"

WD_NAMESPACE_BEGIN

class WDCore;
class WDNode;
class WDBMBase;

/**
* @brief 业务数据类型管理
*/
class  WD_API WDBMTypeMgr
{
public:
    using NodeSPtr = std::shared_ptr<WDNode>;
public:
    WDBMTypeMgr(WDBMBase& bmBase);
    WDBMTypeMgr(const WDBMTypeMgr& right) = delete;
    WDBMTypeMgr(WDBMTypeMgr&& right) = delete;
    WDBMTypeMgr& operator=(const WDBMTypeMgr& right) = delete;
    WDBMTypeMgr& operator=(WDBMTypeMgr&& right) = delete;
    ~WDBMTypeMgr();
public:
    /**
    * @brief 获取WDBMBase对象引用
    */
    inline WDBMBase& bmBase() const;
    /**
    * @brief 注册类型
    *   当对应名称的类型已存在时，将注册失败
    * @return 注册成功返回类型描述对象指针，失败返回nullptr
    */
    WDBMTypeDesc* regist(const std::string_view& typeName);
    /**
    * @brief 注册类型
    *   当对应名称的类型已存在时，将注册失败
    * @return 注册成功返回类型描述对象指针，失败返回nullptr
    */
    WDBMTypeDesc* registByBaseType(const std::string_view& typeName, const WDBMTypeDesc& baseTypeDesc);
    /**
     * @brief 指定类型名称获取类型描述
     * @param typeName 类型名称
     * @return 如果对应名称的描述信息不存在，则返回nullptr
    */
    WDBMTypeDesc* get(const std::string_view& typeName) const;
    /**
     * @brief 通过指定类型Id获取类型
     */
    WDBMTypeDesc* getById(ushort id) const;
    /**
    * @brief 某个类型是否已经注册
    */
    inline bool contains(const std::string_view& typeName) const;
    /**
    * @brief 获取所有已注册的类型信息
    */
    inline const std::vector<WDBMTypeDesc*>& registedTypes() const;
    /**
    * @brief 查询某种子类型可挂载到的父类型
    */
    const StringVector& supportedParentTypes(const std::string_view& typeName) const;
    /**
    * @brief 查询某种类型所支持挂载的子类型
    */
    StringVector supportedChildTypes(const std::string_view& typeName) const;
    /**
     * @brief 校验某个类型是否支持挂载到父节点的类型上
     * @param type 类型
     * @param parentType 父节点类型, 如果type为根节点, 则父类型可以指定为 空字符串
     * @return 返回true表示支持挂载，false表示不支持挂载
    */
    bool checkParent(const std::string_view& typeName, const std::string_view& parentTypeName) const;
public:
    /**
     * @brief 初始化
     * @param bmBase 业务模块对象基类
    */
    void init();
    /**
     * @brief 是否已被初始化
    */
    inline bool initialized() const
    {
        return _initialized;
    }
    /**
    * @brief 构建节点
    * @param typeName 要创建的节点类型, 必须指定, 用于查询类型的描述信息
    * @param nodeName 节点名称, 可选, 不指定时以类型名称为节点名称
    * @param pParentNode 父节点对象，可选，指定时将校验父节点能否挂载当前节点
    * @param pNextNode 当前节点的下一个节点，可选，指定时，将以插入的方式(插入在pNextNode之前)添加到父节点上
    * @return 创建成功返回节点对象指针，失败(类型未注册或指定的类型名称有误时)返回nullptr
    */
    NodeSPtr build(WDBMBase& bmBase
        , const std::string_view& typeName
        , const std::string_view& nodeName = ""
        , NodeSPtr pParentNode = nullptr
        , NodeSPtr pNextNode = nullptr) const;
    /**
     * @brief 构建节点
     *  指定节点对象以及节点类型，向节点中设置业务数据对象
     * @param node 节点对象
     * @param typeName 节点类型名称
     * @return 构建成功则返回已设置的业务数据对象指针,失败返回nullptr, 如果指定的节点类型名称不合法或未注册，则构建失败
    */
    WDBDBase* build(WDBMBase& bmBase
        , WDNode& node
        , const std::string_view& typeName) const;
private:
    WDBMBase& _bmBase;
    // 保存类型描述列表、
    //  1.使用下标来作为类型ID
    //  2.保持注册的先后顺序
    std::vector<WDBMTypeDesc*> _types;
    // 保存类型名称对应的类型ID,方便使用类型名称来索引对应的类型描述
    std::unordered_map<std::string_view, WDBMTypeDesc*> _nameMap;
    // 是否已经初始化
    bool _initialized;
};

WD_NAMESPACE_END


#include "WDBMTypeMgr.inl"