#pragma once

#include "../../../WDBDBase.h"
#include "../../private/WDBMDModelHelpter.h"

WD_NAMESPACE_BEGIN

/**
* @brief 分支层级数据
*/
class WD_API WDBMDPipeBRAN : public WDBDBase
    , public WDSelectionInterface
    , public WDGraphableInterface
{
public:
    /**
    * @brief 数据标志
    */
    enum Flag
    {
        //无
        Flag_None = 0,
        //显示管线流向
        Flag_PipelineDirectionDisplay = 1 << 0,
    };
    /**
    * @brief 数据标志
    */
    using Flags = WDFlags<Flag, uint>;
public:
    /**
    * @brief 更新桥架分支的所有管件连接
    * @param pBranchNode 桥架分支节点
    */
    static bool UpdateConnectionCt(WDCore& core, WDNode::SharedPtr pCtBran);
private:
    //数据标志
    Flags _flags;
    // 分支与第一个管件(或分支尾)的未连接的虚线
    WDGraphableLines _lines;
    // 分支与第一个管件(或分支尾)的未连接的虚线
    //// 分支中所有管件之间未连接的虚线
    //WDGraphableLines                _lines;
public:
    WDBMDPipeBRAN(WDNode& node);
    virtual ~WDBMDPipeBRAN();
public:
    /**
    * @brief 获取数据标志
    */
    inline Flags            flags() const
    {
        return _flags;
    }
    /**
    * @brief 设置数据标志
    */
    void                    setFlags(Flags flgs);
public:
    virtual WDBDBase* clone(WDNode& node) const override;
    virtual void copy(const WDBDBase& src) override;
public:
    virtual WDGraphableInterface* graphableSupporter() override;
    virtual const WDSelectionInterface* selectionSupporter() const override;
public:
    virtual bool pickup(const DMat4& transformMatrix
        , const WDPickupParam& param
        , const WDClip* pClip
        , PickupResult& outResult) const override;
    virtual FrameSelectResult frameSelect(const DMat4& transformMatrix
        , const WDFrameSelectParam& param) const override;

    virtual DAabb3 gRenderAabb() const override;
    virtual const WDGraphableLines* gLines() override;
public:
    virtual const WDNode& ownerNode() const override
    {
        return this->node();
    }
protected:
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
private:
    /**
     * @brief 更新管子伴随删除操作
     * @param bran 分支
     * @param tubi 管子
     * @param gHPos 起始坐标
     * @param gHDir 起始朝向
     * @param hBore 起始管径
     * @param gTPos 结束坐标
     * @param gTDir 结束朝向
     * @param tBore 结束管径
     * @param tubiCount 分支内管子总数
     * @param unconnPts 未连接的线
    */
    static void UpdateTubiWithRemove(WDNode& bran
        , WDNode& tubi
        , const DVec3& gHPos
        , const DVec3& gHDir
        , const std::string& hBore
        , const DVec3& gTPos
        , const DVec3& gTDir
        , const std::string& tBore
        , bool prevIsBend
        , FVec3Vector& unconnPts);
    /**
     * @brief 创建并更新管子
     * @param bran 分支
     * @param pNext 下一个节点
     * @param pSpco 管子节点引用等级spco
     * @param gHPos 起始坐标
     * @param gHDir 起始朝向
     * @param hBore 起始管径
     * @param gTPos 结束坐标
     * @param gTDir 结束朝向
     * @param tBore 结束管径
     * @param tubiCount 分支内管子总数
     * @param unconnPts 未连接的线
    */
    static WDNode::SharedPtr UpdateTubiWithCreate(WDNode& bran
        , WDNode::SharedPtr pNext
        , WDNode::SharedPtr pSpco
        , const DVec3& gHPos
        , const DVec3& gHDir
        , const std::string& hBore
        , const DVec3& gTPos
        , const DVec3& gTDir
        , const std::string& tBore
        , FVec3Vector& unconnPts);
    /**
    * @brief 更新分支的所有管件连接
    * @param pBranchNode 分支节点
    */
    static bool UpdateConnection(WDCore& core, WDNode& branch);
};

WD_NAMESPACE_END

