#include    "WDBMNodeAttrsSerialize.h"
#include    "../WDBDBase.h"
#include    "businessModule/typeMgr/WDBMTypeMgr.h"
#include    "businessModule/WDBMBase.h"

WD_NAMESPACE_BEGIN

WDBMNodeAttrsSerialize::WDBMNodeAttrsSerialize(WDBMBase& bmBase)
    :_bmBase(bmBase)
{
}
WDBMNodeAttrsSerialize::~WDBMNodeAttrsSerialize()
{
}

void WDBMNodeAttrsSerialize::writeFromNode(const WDNode& node
    , IWriter& iWriter
    , BaseAttrSerialFlags flags) const
{
    WDBMAttrWriter writer(iWriter);
    // 节点guid
    writer.write("Id", node.uuid());
    // 节点名称
    writer.write("Name", node.srcName());
    // 节点类型
    writer.write("Type", std::string_view(node.actType()));

    WDNode::Flags nodeFlags = node.flags();
    // 可见性
    if (flags.hasFlag(BASF_Visible))
    {
        if (!nodeFlags.hasFlag(WDNode::F_Visible))
            writer.write("Visible", false);
    }
    // 是否锁定
    if (nodeFlags.hasFlag(WDNode::F_Lock))
        writer.write("Lock", true);

    // 隐藏标志
    if (nodeFlags.hasFlag(WDNode::F_ItemHidden))
        writer.write("Hidden", true);

    // 基本颜色和材质
    if (flags.hasFlag(BASF_BasicColor))
    {
        // 基本颜色
        writer.write("BasicColor", node.basicColor());
        // !TODO: 材质Id
    }
    
    // 节点业务对象属性
    auto pBase = node.getBDBase();
    assert(pBase != nullptr && "注意:节点业务数据对象获取失败!");
    if (pBase != nullptr)
        pBase->write(writer);
}
void WDBMNodeAttrsSerialize::readToNode(WDNode& node, IReader& iReader
    , BaseAttrSerialFlags flags) const
{
    WDBMAttrReader reader(iReader);

    // 节点guid
    WDUuid nodeId;
    reader.read("Id", nodeId);
    node.setUuid(nodeId);

    // 节点名称
    std::string nodeName;
    reader.read("Name", nodeName);
    node.setName(nodeName);

    // 节点类型
    std::string typeName;
    reader.read("Type", typeName);
    node.setType(_bmBase, typeName);

    // 根据类型创建数据对象
    auto pBase = node.getBDBase();
    if (pBase == nullptr)
    {
        pBase = node.createBD();
        assert((pBase != nullptr) && "注意:节点业务数据对象创建失败,确保节点类型合法或已注册!");
    }

    WDNode::Flags nodeFlags = node.flags();
    // 可见性
    if (flags.hasFlag(BASF_Visible))
        reader.read("Visible", nodeFlags, WDNode::F_Visible);
    // 是否锁定
    reader.read("Lock", nodeFlags, WDNode::F_Lock);
    // 隐藏标志
    reader.read("Hidden", nodeFlags, WDNode::F_ItemHidden);
    node.setFlags(nodeFlags);
    // 基本颜色和材质
    if (flags.hasFlag(BASF_BasicColor))
    {
        // 节点颜色/材质相关
        Color basicColor;
        if (reader.read("BasicColor", basicColor))
        {
            node.setBasicColor(basicColor);
        }
        // !TODO: 材质Id
    }

    // 节点业务数据属性
    if (pBase != nullptr)
        pBase->read(reader);
}

void WDBMNodeAttrsSerialize::writeBaseFromNode(const WDNode& node, IWriter& iWriter) const
{
    WDBMAttrWriter writer(iWriter);
    // guid
    writer.write("Id", node.uuid());
    // 名称
    writer.write("Name", node.srcName());
    // 类型
    writer.write("Type", std::string_view(node.actType()));
}
void WDBMNodeAttrsSerialize::writeAttrFromNode(const WDNode& node
    , IWriter& iWriter
    , BaseAttrSerialFlags flags) const
{
    WDBMAttrWriter writer(iWriter);
    // 属性Id(使用的是节点ID)
    writer.write("Id", node.uuid());

    WDNode::Flags nodeFlags = node.flags();
    // 可见性
    if (flags.hasFlag(BASF_Visible))
    {
        if (!nodeFlags.hasFlag(WDNode::F_Visible))
            writer.write("Visible", false);
    }
    // 是否锁定
    if (nodeFlags.hasFlag(WDNode::F_Lock))
        writer.write("Lock", true);

    // 隐藏标志
    if (nodeFlags.hasFlag(WDNode::F_ItemHidden))
        writer.write("Hidden", true);

    // 基本颜色和材质
    if (flags.hasFlag(BASF_BasicColor))
    {
        // 基本颜色
        writer.write("BasicColor", node.basicColor());
        // !TODO: 材质Id
    }
    
    // 节点业务对象属性
    auto pBase = node.getBDBase();
    assert(pBase != nullptr && "注意:节点业务数据对象获取失败!");
    if (pBase != nullptr)
        pBase->write(writer);
}

bool WDBMNodeAttrsSerialize::readBaseToNode(WDNode& node, IReader& iReader) const
{
    WDBMAttrReader reader(iReader);
    // 节点guid
    WDUuid nodeId;
    reader.read("Id", nodeId);
    node.setUuid(nodeId);
    // 节点名称
    std::string nodeName;
    reader.read("Name", nodeName);
    node.setName(nodeName);
    // 节点类型
    std::string typeName;
    reader.read("Type", typeName);
    node.setType(_bmBase, typeName);

    return true;
}
bool WDBMNodeAttrsSerialize::readAttrToNode(WDNode& node
    , IReader& iReader
    , BaseAttrSerialFlags flags) const
{
    WDBMAttrReader reader(iReader);

    auto pBase = node.getBDBase();
    if (pBase == nullptr)
    {
        pBase = node.createBD();
        assert((pBase != nullptr) && "注意:节点业务数据对象创建失败,确保节点类型合法或已注册!");
    }

    WDNode::Flags nodeFlags = node.flags();
    // 可见性
    if (flags.hasFlag(BASF_Visible))
        reader.read("Visible", nodeFlags, WDNode::F_Visible);
    // 是否锁定
    reader.read("Lock", nodeFlags, WDNode::F_Lock);
    // 隐藏标志
    reader.read("Hidden", nodeFlags, WDNode::F_ItemHidden);

    node.setFlags(nodeFlags);
    // 节点颜色/材质相关 
    if (flags.hasFlag(BASF_BasicColor))
    {
        // 基本颜色
        Color basicColor;
        if (reader.read("BasicColor", basicColor))
        {
            node.setBasicColor(basicColor);
        }
        // !TODO: 材质Id
    }
    
    if (pBase != nullptr)
    {
        pBase->read(reader);
        return true;
    }
    else
    {
        assert(false && "注意:节点业务数据对象创建失败,确保节点类型合法或已注册!");
        return false;
    }
}


WD_NAMESPACE_END

