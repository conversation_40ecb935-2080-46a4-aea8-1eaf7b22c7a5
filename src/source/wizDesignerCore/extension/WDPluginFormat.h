/**
* @file WDPluginTool.h
* @brief 工具基类
* <AUTHOR>
* @date 0000-01-01
*/
#pragma     once
#include    "WDPlugin.h"
#include    "../common/WDTDelegate.hpp"
#include    <set>
#include    "../common/WDStream.h"
#include    "../businessModule/WDBMCommon.h"
#include    "../node/WDNode.h"
#include    "../common/WDTask.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 日志类
*/
class PluginLog
{
public:
    enum Level
    {
        L_Info,     // 普通日志
        L_Warn,     // 警告日志
        L_Error,    // 错误日志
        L_User,     // 自定义日志
    };
    struct LogItem
    {
        // 记录级别
        Level       level;
        // 文本, 统一使用UTF-8编码
        std::string text;
    };
    using LogItems = std::vector<LogItem>;
public:
    /**
     * @brief 添加一条日志
     * @param text 日志信息
     * @param level 日志级别
     * 注: 这里三个重载是为了涵盖所有情况，避免进入变长参数的重载函数中
    */
    inline void addLog(const char* text, Level level = Level::L_Warn)
    {
        _logs.push_back({});
        _logs.back().level = level;
        _logs.back().text = text;
    }
    /**
    * @brief 添加一条日志
    * @param level 日志等级
    * @param fmt 格式控制符
    * @param ...args 变长参数
    * 注:这里用 fmt 参数 和 变长参数args 来 sprintf 组合出日志字符串，所以变长参数需要是 char* 类型
    */
    template<class...   Arglist>
    inline void addLog(Level level, const char* fmt, Arglist&&... args)
    {
        char    text[1024] = {0};
        sprintf_s(text, sizeof(text), fmt, args...);
        _logs.push_back({});
        _logs.back().level = std::forward<Level>(level);
        _logs.back().text = std::forward<std::string>(text);
    }
    /**
     * @brief 添加一条日志
     * 不指定等级，默认为    L_Warn
     * @param fmt 格式控制符
     * @param ...args 变长参数
     * 注:这里用 fmt 参数 和 变长参数args 来 sprintf 组合出日志字符串，所以变长参数需要是 char* 类型
    */
    template<class...   Arglist>
    inline void addLog(const char* fmt, Arglist&&... args)
    {
        addLog(Level::L_Warn, fmt, args...);
    }
    /**
     * @brief 添加一条日志
     * @param text 日志信息
    */
    inline void addLog(const LogItem& log)
    {
        _logs.push_back(log);
    }
    /**
     * @brief 获取所有的日志信息
    */
    inline const LogItems& logs() const
    {
        return  _logs;
    }
    inline void clear()
    {
        _logs.clear();
    }
private:
    LogItems _logs;
};
/**
* @brief Format插件扩展
*   用于对各种数据格式的读取和写入支持
*/
class WD_API WDPluginFormat : public WDPlugin
{
public:
    static constexpr const char* ExtensionType = "plugin.format";
public:
    using EventProgress = std::function<void(double,const char*)>;
public:
    enum FormatAttr
    {
        FA_Read     =   0,
        FA_Write    =   1,
    };
public:
    enum EncodingFormat
    {
        EF_Utf8,
        EF_Ansi
    };
    /**
    * @brief 参数
    */
    struct FormatParam
    {
        // 读取的数据文件全路径名称
        std::string                 fileName;
        // 版本
        std::string                 version;
        // 进度通知
        mutable EventProgress       evtProgress;
        // 模块类型
        std::string                 moduleType;
        // 当前树上选中的节点
        WD::WDObject::WeakPtr       currentNode;
        // 文件描述
        // 一个组件可能会遇到处理相同后缀的不同类型文件
        // 如：PDMS组件要处理模型脚本和数据脚本两种 .txt 文件且处理方式不相同
        // 此描述用于在组件内部判断用何种方式进行处理
        std::string                 fileDesc;
        // 文件的编码格式
        EncodingFormat              encodingFormat = EncodingFormat::EF_Utf8;
        // 节点过滤方法
        std::function<bool(WDNode::SharedPtr)> nodeFilter;
        // 单位转换的比例
        double unitRatio = 1.0;
        // 一些独立的额外参数{参数名，参数值}
        std::map<std::string, std::any> extraParams;
    };
    /**
    * @brief 支持的格式列表
    */
    struct  Format
    {
        // 支持的文件后缀
        std::string fmt;
        // 文件版本号
        std::string version;
        // 文件的描述
        std::string desc;
    public:
        // 获取组件的支持文件的格式
        std::string format() const
        {
            if (version.empty())
                return  desc + "(*" +fmt + ")";
            else
                return  version + " " + desc + "(*" +fmt + ")";
        }
    };
    using   Formats     =   std::vector<Format>;
    using   Objects     =   std::vector<WDObject::SharedPtr>;
    using   ObjectSet   =   std::set<WDObject::SharedPtr>;
private:
    PluginLog _log;
public:
    /**
    * @brief 构造
    */
    WDPluginFormat(WDCore& app);
    /**
    * @brief 析构
    */
    virtual ~WDPluginFormat();
public:
    /**
    * @brief format:查询是否支持某种数据格式
    * @brief v: 版本 =="" 则任意匹配
    */
    inline bool support(const Format& fmt,const FormatAttr& attr = FA_Read) const
    {
        const Formats& formats = supportFormats(attr);
        for (auto& var : formats)
        {
            if (var.fmt != fmt.fmt )
                continue;
            if (fmt.version.empty())
                return  true;
            else if(fmt.version == var.version)
                return  true;
        }
        return  false;
    }
public:
    /**
    * @brief 获取所有支持的数据格式
    * @param attr:读写属性,默认参数读取 see @FormatAttr
    */
    virtual const Formats& supportFormats(const FormatAttr& attr = FA_Read) const = 0;
    /**
    * @brief 当前插件输入/输出的格式是否支持单位转换
    * @return 支持返回true,不支持返回false
    */
    virtual bool supportUintConvert() const;
    /**
    *   @brief 从文件中读取对象
    *   @param param:输入参数
    *   @param pSetProgress 设置进度条进度的函数指针
    *   @param pSetText 设置进度条文本的函数指针
    *   @param result:读取结果数组
    *   @return 读取的字节数量,0:失败
    */

    virtual size_t  read(const FormatParam& param,Objects& result)    =   0;
    /**
    *   @brief 从流中读取对象
    *   @param param:输入参数
    *   @param result:读取结果数组
    *   @return 读取的字节数量
    */
    virtual size_t  read(WDInStream* stream,Objects& result)    =   0;
    /**
    *   @brief  写入对象数组到文件
    *   @return 返回写入的字节数量,0:失败
    */
    virtual size_t  write(const FormatParam& param,const Objects& objs)     =   0;
    /**
    *   @brief  写入对象数组到流中
    *   @return 返回写入的字节数量,0:失败
    */
    virtual size_t  write(WDOutStream* stream,const Objects& result)     =   0;
public:
    /**
     * @brief 这里是一个临时接口，暂时用于PDMS脚本导入，后续考虑支持这种方式; 其他format组件不建议使用
     *  一次导入多个PDMS脚本文件，并且文件里面对象出现相互引用的情况，需要保证文件乱序导入的情况下仍然能正常引用到其他文件的节点对象
    */
    virtual void  batchPDMSReadBegin() 
    {
    }
    /**
     * @brief 这里是一个临时接口，暂时用于PDMS脚本导入，后续考虑支持这种方式; 其他format组件不建议使用
     *  一次导入多个PDMS脚本文件，并且文件里面对象出现相互引用的情况，需要保证文件乱序导入的情况下仍然能正常引用到其他文件的节点对象
    */
    virtual void  batchPDMSReadEnd()
    {
    }
public:
    /**
    * @brief 返回日志对象
    */
    inline const PluginLog& log() const
    {
        return _log;
    }
    /**
     * @brief 重载，返回非const的日志对象
    */
    inline PluginLog& log()
    {
        return _log;
    }
protected:
    /**
    * @brief 设置 读取/写入 出现错误的原因, 由子类调用
    */
    void setLog(const PluginLog& log)
    {
        _log = log;
    }
    static  Formats FormatNull;
};

using   Formats             =   WDPluginFormat::Formats;

WD_NAMESPACE_END


