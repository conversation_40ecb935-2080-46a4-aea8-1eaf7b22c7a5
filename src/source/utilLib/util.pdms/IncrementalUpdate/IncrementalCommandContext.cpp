#include "IncrementalCommandContext.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/common/WDStringConvert.h"

WD_NAMESPACE_BEGIN


IncrementalCommandContext::IncrementalCommandContext(WDCore& core, UpdateDataSummary& dataSummary)
    : _core(core), unSupport(this->logMgr), dataSummary(dataSummary), nodeMgr(core, "Catalog")
{
}
IncrementalCommandContext::~IncrementalCommandContext()
{
    nodeMgr.clear();
}

WDBMBase& IncrementalCommandContext::getModule() const
{
    return static_cast<WDBMBase&>(_core.getBMCatalog());
}

const bool IncrementalCommandContext::bNodeIsNewNode(WD::WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return false;
    return newNodes.find(pNode) != newNodes.end();
}

const WD::WDNode::SharedPtr IncrementalCommandContext::getOperateNode(const std::string& PDMSNodeName)  const
{
    return PDMSCommon::GetOperateNode(PDMSNodeName
        , [&](const NodeUnicode& nodeCode)->const WD::WDNode::SharedPtr
    {
        return nodeMgr.getNode(nodeCode);
    }
        , [&](const std::string& name)->std::string
    {
        return keyWordXMLMgr.getWDTypeByPDMSType(name);
    }, &dataSummary);
}
void    IncrementalCommandContext::removeOperateNodeByName(const NodeUnicode& nodeCode)
{
    if (!nodeCode.isValid())
    {
        assert(false);
        return;
    }
    auto& nodeMap = nodeMgr.nodeMap;
    auto itr = nodeMap.find(nodeCode);
    if (itr != nodeMap.end())
        nodeMap.erase(itr);
}
void    IncrementalCommandContext::changeOperateNodeName(const NodeUnicode& nodeCode, const std::string& newName)
{
    if (!nodeCode.isValid())
    {
        assert(false);
        return;
    }
    auto& nodeMap = nodeMgr.nodeMap;
    NodeUnicode newCode = nodeCode;
    newCode.name = newName;
    if (nodeMap.find(newCode) != nodeMap.end())
    {
        assert(false && "名称已存在");
        return;
    }

    auto itr = nodeMap.find(nodeCode);
    if (itr != nodeMap.end())
    {
        auto pNode = itr->second;
        nodeMap.erase(itr);
        nodeMap.emplace(newCode, pNode);
    }
}
bool    IncrementalCommandContext::addOperateNode(const WD::WDNode::SharedPtr pNewNode, const NodeUnicode& nodeCode)
{
    if (pNewNode == nullptr)
        return false;

    auto tNodeCode = nodeCode;
    if (!tNodeCode.isValid())
    {
        tNodeCode.name = pNewNode->srcName();
        tNodeCode.refNo = pNewNode->getAttribute("RefNo").toString();
        if (!tNodeCode.isValid())
            return true;

    }
    auto& nodeMap = nodeMgr.nodeMap;
    //  查找节点
    auto itr = nodeMap.find(tNodeCode);
    if (itr == nodeMap.end())
    {
        nodeMap.emplace(tNodeCode, pNewNode);
        return true;
    }
    else
    {
        assert(false && "名称或引用号重复!");
        return false;
    }
}
void IncrementalCommandContext::clear()
{
    nodeMgr.clear();
    logMgr.clear();
    refTypeHandle.clear();
}
WD_NAMESPACE_END
