
#include    "WDSvgPath.h"
#include    "WDSvgXmlCreator.h"

WD_NAMESPACE_BEGIN

WDSvgPath::WDSvgPath()
    :WDSvgObject(T_Path)
{}

bool    WDSvgPath::fromSvg(XMLDoc&, XMLNode* node)
{
    _attr.fromSvgAttrs(*node);

    XMLAttr* data = node->first_attribute("d");
    if (data == nullptr)
        return  false;
    else
    {
        /// 替换所有的','->' ';
        std::string src = StringReplace(data->value(), ",", " ");
        buildPath(src.c_str());

        return !cmds.empty();
    }
}

XMLNode*WDSvgPath::toSvg(XMLDoc& doc, XMLNode* parent) const
{
    static const char* fmt = "%f,%f ";
    static auto toString = [](const Vec2& point)->std::string
    {
        char buf[64];
        sprintf(buf, fmt, point.x, point.y);
        return buf;
    };

    std::string data;
    for (auto& var : cmds)
    {
        switch (var.cmd)
        {
        case PC_moveTo:
            if (!var.datas.empty())
            {
                data    +=  "M";
                data    +=  toString(var.datas.front());
            }
            break;
        case PC_lineTo:
            if (!var.datas.empty())
            {
                data    +=  "L";
                for (auto& point : var.datas)
                {
                    data    +=  toString(point);
                    data    +=  " ";
                }
            }
            break;
        case PC_cubicTo:
            if (!var.datas.empty())
            {
                data    +=  "C";
                for (auto& point : var.datas)
                {
                    data    +=  toString(point);
                    data    +=  " ";
                }
            }
            break;
        case PC_Z:
            {
                data    +=  "Z";
            }
            break;
        }
    }

    auto nd = createSvg(doc, parent);
    WDSvgXmlCreator::Node(doc, nd).attr("d", data);

    return nd;
}

const char* WDSvgPath::buildPath(const char* src)
{
    const char* pData = NextCMD(src);
    Vec3        cp0;
    Vec3        cp1;
    char        cmd;
    while (pData)
    {
        cmd = *pData;
        /// 当前命令
        switch (cmd)
        {
        case 'M':
        case 'm':
            {
                pData = pathMoveTo(pData + 1, cp0, cmd == 'm');
                cp1 = cp0;
            }
            break;
        case 'L':
        case 'l':
            {
                pData = pathLineTo(pData + 1, cp0, cmd == 'l');
                cp1 = cp0;
            }
            break;
        case 'H':
        case 'h':
            {
                pData = pathHLineTo(pData + 1, cp0, cmd == 'h');
                cp1 = cp0;
            }
            break;
        case 'V':
        case 'v':
            {
                pData = pathVLineTo(pData + 1, cp0, cmd == 'v');
                cp1 = cp0;
            }
            break;
        case 'C':
        case 'c':
            {
                pData = pathcubicBezierTo(pData + 1, cp0, cp1, cmd == 'c');
            }
            break;
        case 'S':
        case 's':
            {
                pData = pathCubicBezierShortTo(pData + 1, cp0, cp1, cmd == 's');
            }
            break;
        case 'Q':
        case 'q':
            {
                pData = pathQuadBezierTo(pData + 1, cp0, cp1, cmd == 'q');
            }
            break;
        case 'T':
        case 't':
            {
                pData = pathQuadBezierShortTo(pData + 1, cp0, cp1, cmd == 't');
            }
            break;
        case 'A':
        case 'a':
            {
                pData = pathArcTo(pData + 1, cp0, cmd == 'a');
                cp1 = cp0;
            }
            break;
            /// path 中有多个z,即一个path
        case 'Z':
        case 'z':
            {
                CMDData cmdData = {PC_Z, {}};
                cmds.push_back(cmdData);
                ++pData;
            }
            break;
        default:
            break;
        }
        pData = NextCMD(pData);
    }
    return  pData;
}


const char* WDSvgPath::pathMoveTo(const char* data, Vec3& cp, bool ref)
{
    while (*data == ' ')
        ++data;

    char    szXVal[64] = { 0 };
    char    szYVal[64] = { 0 };
    data = ParseNumber(data, szXVal);
    data = ParseNumber(data, szYVal);

    double  xVal = atof(szXVal);
    double  yVal = atof(szYVal);
    if (ref)
    {
        cp.x += xVal;
        cp.y += yVal;
    }
    else
    {
        cp.x = xVal;
        cp.y = yVal;
    }
    moveTo<double>(cp.x, cp.y);
    // 当前cp代表绝对位置
    CMDData cmd = { PC_moveTo, {Vec2(cp.x, cp.y)} };
    cmds.push_back(cmd);

    // 考虑隐式指令的情况
    // M后面如果跟了多个点
    // 则第二个点开始相当于存在一个隐式的L
    while (1)
    {
        data = ParseNumber(data, szXVal);
        data = ParseNumber(data, szYVal);

        try
        {
            xVal = std::stod(szXVal);
            yVal = std::stod(szYVal);

            if (ref)
            {
                cp.x += xVal;
                cp.y += yVal;
            }
            else
            {
                cp.x = xVal;
                cp.y = yVal;
            }
            lineTo<double>(cp.x, cp.y);
            CMDData hiddenCmd = { PC_lineTo, {Vec2(cp.x, cp.y)} };
            cmds.push_back(hiddenCmd);
        }
        catch (...)
        {
            break;
        }
    }

    return  data;
}

const char* WDSvgPath::pathLineTo(const char* data, Vec3& cp, bool ref)
{
    while (*data == ' ')  ++data;

    // 考虑隐式命令
    while (1)
    {
        char    szXVal[64] = { 0 };
        char    szYVal[64] = { 0 };
        data = ParseNumber(data, szXVal);
        data = ParseNumber(data, szYVal);
        try
        {
            double  xVal = std::stod(szXVal);
            double  yVal = std::stod(szYVal);
            if (ref)
            {
                cp.x += xVal;
                cp.y += yVal;
            }
            else
            {
                cp.x = xVal;
                cp.y = yVal;
            }
            lineTo<double>(cp.x, cp.y);

            CMDData cmd = { PC_lineTo, {Vec2(cp.x, cp.y)} };
            cmds.push_back(cmd);
        }
        catch (...)
        {
            break;
        }
    }
    return  data;
}

const char* WDSvgPath::pathHLineTo(const char* data, Vec3& cp, bool ref)
{
    while (*data == ' ')  ++data;
    // 考虑隐式指令
    // 如果H/h后面跟了多个数字
    // 则表示省略了第二个及以后数字前面的H/h
    while (1)
    {
        char    szXVal[64] = { 0 };
        data = ParseNumber(data, szXVal);
        double xVal;
        try
        {
            xVal = std::stod(szXVal);
            if (ref)
                cp.x += xVal;
            else
                cp.x = xVal;

            lineTo<double>(cp.x, cp.y);

            CMDData cmd = { PC_lineTo,{Vec2(cp.x, cp.y)} };
            cmds.push_back(cmd);
        }
        catch (...)
        {
            break;
        }
    }
    return  data;
}
const char* WDSvgPath::pathVLineTo(const char* data, Vec3& cp, bool ref)
{
    while (*data == ' ')  ++data;

    while (1)
    {
        char    szBuf[64] = { 0 };
        data = ParseNumber(data, szBuf);
        double  yVal;
        try
        {
            yVal = std::stod(szBuf);
            if (ref)
                cp.y += yVal;
            else
                cp.y = yVal;
            lineTo<double>(cp.x, cp.y);

            CMDData cmd = { PC_lineTo, {Vec2(cp.x, cp.y)} };
            cmds.push_back(cmd);
        }
        catch (...)
        {
            break;
        }
    }

    return  data;
}
const char* WDSvgPath::pathcubicBezierTo(const char*data, Vec3& cp0, Vec3& cp1, bool ref)
{
    while(*data == ' ')  ++data;

    while(1)
    {
        char    szcx0[64]   =   {0};
        char    szcy0[64]   =   {0};
        char    szcx1[64]   =   {0};
        char    szcy1[64]   =   {0};
        char    szX[64]     =   {0};
        char    szY[64]     =   {0};
        data = ParseNumber(data, szcx0);
        data = ParseNumber(data, szcy0);
        data = ParseNumber(data, szcx1);
        data = ParseNumber(data, szcy1);
        data = ParseNumber(data, szX);
        data = ParseNumber(data, szY);

        try
        {
            double  cx0 = std::stod(szcx0);
            double  cy0 = std::stod(szcy0);

            double  cx1 = std::stod(szcx1);
            double  cy1 = std::stod(szcy1);

            double  x = std::stod(szX);
            double  y = std::stod(szY);

            if (ref)
            {
                cx0 += cp0.x;
                cy0 += cp0.y;

                cx1 += cp0.x;
                cy1 += cp0.y;

                x += cp0.x;
                y += cp0.y;
            }

            cubicBezierTo(
                Vec3(cx0, cy0, 0)
                , Vec3(cx1, cy1, 0)
                , Vec3(x, y, 0));

            CMDData cmd = { PC_cubicTo
                , {Vec2(cx0, cy0), Vec2(cx1, cy1), Vec2(x, y)}
            };
            cmds.push_back(cmd);

            cp0.x = x;
            cp0.y = y;

            cp1.x = cx1;
            cp1.y = cy1;
        }
        catch (...)
        {
            break;
        }
    }

    return  data;
}
const char* WDSvgPath::pathCubicBezierShortTo(const char*data, Vec3& cp0, Vec3& cp1, bool ref)
{
    while(*data == ' ')  ++data;

    while(1)
    {
        char    szcx1[64] = { 0 };
        char    szcy1[64] = { 0 };
        char    szX[64] = { 0 };
        char    szY[64] = { 0 };

        data = ParseNumber(data, szcx1);
        data = ParseNumber(data, szcy1);
        data = ParseNumber(data, szX);
        data = ParseNumber(data, szY);

        double  cx0 = 0;
        double  cy0 = 0;
        try
        {
            double  cx1 = std::stod(szcx1);
            double  cy1 = std::stod(szcy1);

            double  x = std::stod(szX);
            double  y = std::stod(szY);

            if (ref)
            {
                cx1 += cp0.x;
                cy1 += cp0.y;

                x += cp0.x;
                y += cp0.y;
            }

            // 
            cx0 = 2.0 * cp0.x - cp1.x;
            cy0 = 2.0 * cp0.y - cp1.y;

            cubicBezierTo(
                Vec3(cx0, cy0, 0)
                , Vec3(cx1, cy1, 0)
                , Vec3(x, y, 0));

            CMDData cmd =
            {
                PC_cubicTo,
                {Vec2(cx0, cy0), Vec2(cx1, cy1), Vec2(x, y)}
            };
            cmds.push_back(cmd);

            cp0.x = x;
            cp0.y = y;

            cp1.x = cx1;
            cp1.y = cy1;
        }
        catch (...)
        {
            break;
        }
    }

    return  data; 
}
const char* WDSvgPath::pathQuadBezierTo(const char*data, Vec3& cp0, Vec3& cp1, bool ref)
{
    double x1, y1, x2, y2, cx, cy;
    double cx1, cy1, cx2, cy2;

    while(1)
    {
        char    szcx[64] = { 0 };
        char    szcy[64] = { 0 };
        char    szx2[64] = { 0 };
        char    szy2[64] = { 0 };

        data = ParseNumber(data, szcx);
        data = ParseNumber(data, szcy);
        data = ParseNumber(data, szx2);
        data = ParseNumber(data, szy2);

        try
        {
            cx = std::stod(szcx);
            cy = std::stod(szcy);

            x2 = std::stod(szx2);
            y2 = std::stod(szy2);

            x1 = cp0.x;
            y1 = cp0.y;

            if (ref)
            {
                cx += cp0.x;
                cy += cp0.y;
                x2 += cp0.x;
                y2 += cp0.y;
            }

            /// convert to cubic bezier
            cx1 = x1 + 2.0 / 3.0 * (cx - x1);
            cy1 = y1 + 2.0 / 3.0 * (cy - y1);
            cx2 = x2 + 2.0 / 3.0 * (cx - x2);
            cy2 = y2 + 2.0 / 3.0 * (cy - y2);

            cubicBezierTo(
                Vec3(cx1, cy1, 0)
                , Vec3(cx2, cy2, 0)
                , Vec3(x2, y2, 0));

            CMDData cmd =
            {
                PC_cubicTo,
                {Vec2(cx1, cy1), Vec2(cx2, cy2), Vec2(x2, y2)}
            };
            cmds.push_back(cmd);

            cp0.x = x2;
            cp0.y = y2;

            cp1.x = cx;
            cp1.y = cy;
        }
        catch (...)
        {
            break;
        }
    }

    return  data;
}
const char* WDSvgPath::pathQuadBezierShortTo(const char*data, Vec3& cp0, Vec3& cp1, bool ref)
{
    double x1, y1, x2, y2, cx, cy;
    double cx1, cy1, cx2, cy2;

    while(1)
    {
        x1 = cp0.x;
        y1 = cp0.y;

        char    szx2[64] = { 0 };
        char    szy2[64] = { 0 };

        data = ParseNumber(data, szx2);
        data = ParseNumber(data, szy2);
        try
        {
            x2 = std::stod(szx2);
            y2 = std::stod(szy2);

            if (ref)
            {
                x2 += cp0.x;
                y2 += cp0.y;
            }

            cx = 2.0f * x1 - cp1.x;
            cy = 2.0f * y1 - cp1.y;

            // Convert to cubix bezier
            cx1 = x1 + 2.0f / 3.0f * (cx - x1);
            cy1 = y1 + 2.0f / 3.0f * (cy - y1);
            cx2 = x2 + 2.0f / 3.0f * (cx - x2);
            cy2 = y2 + 2.0f / 3.0f * (cy - y2);

            cubicBezierTo
            (
                Vec3(cx1, cy1, 0),
                Vec3(cx2, cy2, 0),
                Vec3(x2, y2, 0)
            );

            CMDData cmd =
            {
                PC_cubicTo,
                {Vec2(cx1, cy1), Vec2(cx2, cy2), Vec2(x2, y2)}
            };
            cmds.push_back(cmd);

            cp0.x = x2;
            cp0.y = y2;

            cp1.x = cx;
            cp1.y = cy;
        }
        catch (...)
        {
            break;
        }
    }

    return  data;
}

static float pow2(float x) { return x * x; }
static float lenXY(float x, float y) { return sqrtf(x * x + y * y); }

static float vecrat(float ux, float uy, float vx, float vy)
{
    return (ux * vx + uy * vy) / (lenXY(ux, uy) * lenXY(vx, vy));
}


static float vecang(float ux, float uy, float vx, float vy)
{
    float r = vecrat(ux, uy, vx, vy);
    if (r < -1.0f) r = -1.0f;
    if (r > 1.0f) r = 1.0f;
    return ((ux * vy < uy * vx) ? -1.0f : 1.0f) * acosf(r);
}

static void xformPoint(float* dx, float* dy, float x, float y, float* t)
{
    *dx = x * t[0] + y * t[2] + t[4];
    *dy = x * t[1] + y * t[3] + t[5];
}

static void xformVec(float* dx, float* dy, float x, float y, float* t)
{
    *dx = x * t[0] + y * t[2];
    *dy = x * t[1] + y * t[3];
}

const char* WDSvgPath::pathArcTo(const char* data, Vec3& cp0, bool ref)
{
    WDUnused(cp0);
    WDUnused(ref);

    // Ported from canvg (https://code.google.com/p/canvg/)
    float   rx, ry, rotx;
    float   x1, y1, x2, y2, cx, cy, dx, dy, d;
    float   x1p, y1p, cxp, cyp, s, sa, sb;
    float   ux, uy, vx, vy, a1, da;
    float   x, y, tanx, tany, a, px = 0, py = 0, ptanx = 0, ptany = 0, t[6];
    float   sinrx, cosrx;
    int     fa, fs;
    int     i, ndivs;
    float   hda, kappa;

    while(1)
    {
        char    szXRadius[64] = { 0 };
        char    szYRadius[64] = { 0 };
        char    szXRote[64] = { 0 };
        char    szLargeArc[64] = { 0 };
        char    szSweepDir[64] = { 0 };
        char    szStartX[64] = { 0 };
        char    szStartY[64] = { 0 };

        data = ParseNumber(data, szXRadius);
        data = ParseNumber(data, szYRadius);
        data = ParseNumber(data, szXRote);
        data = ParseNumber(data, szLargeArc);
        data = ParseNumber(data, szSweepDir);
        data = ParseNumber(data, szStartX);
        data = ParseNumber(data, szStartY);

        double  args[7] = { 0 };
        try
        {
            args[0] = std::stod(szXRadius);
            args[1] = std::stod(szYRadius);
            args[2] = std::stod(szXRote);
            args[3] = std::stod(szLargeArc);
            args[4] = std::stod(szSweepDir);
            args[5] = std::stod(szStartX);
            args[6] = std::stod(szStartY);
        }
        catch (...)
        {
            break;
        }

        rx = (float)abs(args[0]);                    // y radius
        ry = (float)abs(args[1]);                    // x radius
        rotx = (float)(args[2] / 180.0f * PI);    // x rotation angle
        fa = (float)abs(args[3]) > 1e-6 ? 1 : 0;     // Large arc
        fs = (float)abs(args[4]) > 1e-6 ? 1 : 0;     // Sweep direction
        x1 = (float)cp0.x;					        // start point
        y1 = (float)cp0.y;
        if (ref)
        {
            // end point
            x2 = (float)(cp0.x + args[5]);
            y2 = (float)(cp0.y + args[6]);
        }
        else
        {
            x2 = (float)args[5];
            y2 = (float)args[6];
        }

        dx = x1 - x2;
        dy = y1 - y2;
        d = sqrtf(dx * dx + dy * dy);
        if (d < 1e-6f || rx < 1e-6f || ry < 1e-6f)
        {
            // The arc degenerates to a line
            lineTo(Vec3(x2, y2, 0));
            CMDData cmd = { PC_lineTo, {Vec2(x2, y2)} };
            cmds.push_back(cmd);

            cp0.x = x2;
            cp0.y = y2;
            return  data;
        }
        sinrx = sinf(rotx);
        cosrx = cosf(rotx);

        // Convert to center point parameterization.
        // http://www.w3.org/TR/SVG11/implnote.html#ArcImplementationNotes
        // 1) Compute x1', y1'
        x1p = cosrx * dx / 2.0f + sinrx * dy / 2.0f;
        y1p = -sinrx * dx / 2.0f + cosrx * dy / 2.0f;
        d = pow2(x1p) / pow2(rx) + pow2(y1p) / pow2(ry);

        if (d > 1)
        {
            d = sqrtf(d);
            rx *= d;
            ry *= d;
        }
        // 2) Compute cx', cy'
        s = 0.0f;
        sa = pow2(rx) * pow2(ry) - pow2(rx) * pow2(y1p) - pow2(ry) * pow2(x1p);
        sb = pow2(rx) * pow2(y1p) + pow2(ry) * pow2(x1p);

        if (sa < 0.0f) sa = 0.0f;
        if (sb > 0.0f)
            s = sqrtf(sa / sb);
        if (fa == fs)
            s = -s;
        cxp = s * rx * y1p / ry;
        cyp = s * -ry * x1p / rx;

        // 3) Compute cx,cy from cx',cy'
        cx = (x1 + x2) / 2.0f + cosrx * cxp - sinrx * cyp;
        cy = (y1 + y2) / 2.0f + sinrx * cxp + cosrx * cyp;

        // 4) Calculate theta1, and delta theta.
        ux = (x1p - cxp) / rx;
        uy = (y1p - cyp) / ry;
        vx = (-x1p - cxp) / rx;
        vy = (-y1p - cyp) / ry;
        a1 = vecang(1.0f, 0.0f, ux, uy);	// Initial angle
        da = vecang(ux, uy, vx, vy);		// Delta angle


        if (fs == 0 && da > 0)
            da -= (float)(2 * PI);
        else if (fs == 1 && da < 0)
            da += (float)(2 * PI);

        // Approximate the arc using cubic spline segments.
        t[0] = cosrx;
        t[1] = sinrx;
        t[2] = -sinrx;
        t[3] = cosrx;
        t[4] = cx;
        t[5] = cy;

        // Split arc into max 90 degree segments.
        // The loop assumes an iteration per end point (including start and end), this +1.
        ndivs = (int)(fabsf(da) / (PI * 0.5f) + 1.0f);
        hda = (da / (float)ndivs) / 2.0f;
        // Fix for ticket #179: division by 0: avoid cotangens around 0 (infinite)
        if ((hda < 1e-3f) && (hda > -1e-3f))
            hda *= 0.5f;
        else
            hda = (1.0f - cosf(hda)) / sinf(hda);
        kappa = fabsf(4.0f / 3.0f * hda);

        if (da < 0.0f)
            kappa = -kappa;

        for (i = 0; i <= ndivs; i++)
        {
            a = a1 + da * ((float)i / (float)ndivs);
            dx = cosf(a);
            dy = sinf(a);
            xformPoint(&x, &y, dx * rx, dy * ry, t);                      // position
            xformVec(&tanx, &tany, -dy * rx * kappa, dx * ry * kappa, t); // tangent
            if (i > 0)
            {
                cubicBezierTo(Vec3(px + ptanx, py + ptany, 0)
                    , Vec3(x - tanx, y - tany, 0)
                    , Vec3(x, y, 0));

                CMDData cmd =
                {
                    PC_cubicTo
                    ,{Vec2(px + ptanx, py + ptany), Vec2(x - tanx, y - tany), Vec2(x, y)}
                };
                cmds.push_back(cmd);
            }
            px = x;
            py = y;
            ptanx = tanx;
            ptany = tany;
        }
        cp0.x = x2;
        cp0.y = y2;
    }

    return  data;
}
const char* WDSvgPath::NextCMD(const char* src)
{
    for (; *src ; ++ src)
    {
        switch (*src)
        {
        case 'M':
        case 'm':
        case 'L':
        case 'l':
        case 'H':
        case 'h':
        case 'V':
        case 'v':
        case 'C':
        case 'c':
        case 'S':
        case 's':
        case 'Q':
        case 'q':
        case 'T':
        case 't':
        case 'A':
        case 'a':
        case 'Z':
        case 'z':
            return  src;
        default:
            break;
        }
    }
    return  nullptr;
}
int     WDSvgPath::IsDigit(char c)
{
    return c >= '0' && c <= '9';
}

bool    WDSvgPath::IsCmd(char ch)
{
    switch (ch)
    {
    case 'M':
    case 'm':
    case 'L':
    case 'l':
    case 'H':
    case 'h':
    case 'V':
    case 'v':
    case 'C':
    case 'c':
    case 'S':
    case 's':
    case 'Q':
    case 'q':
    case 'T':
    case 't':
    case 'A':
    case 'a':
    case 'Z':
    case 'z':
        return  true;
    }
    return  false;
}

WD_NAMESPACE_END